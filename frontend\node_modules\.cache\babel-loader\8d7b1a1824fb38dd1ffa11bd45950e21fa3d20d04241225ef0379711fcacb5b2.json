{"ast": null, "code": "function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\nexport default function () {\n  return this.each(raise);\n}", "map": {"version": 3, "names": ["raise", "nextS<PERSON>ling", "parentNode", "append<PERSON><PERSON><PERSON>", "each"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/raise.js"], "sourcesContent": ["function raise() {\n  if (this.nextSibling) this.parentNode.appendChild(this);\n}\n\nexport default function() {\n  return this.each(raise);\n}\n"], "mappings": "AAAA,SAASA,KAAKA,CAAA,EAAG;EACf,IAAI,IAAI,CAACC,WAAW,EAAE,IAAI,CAACC,UAAU,CAACC,WAAW,CAAC,IAAI,CAAC;AACzD;AAEA,eAAe,YAAW;EACxB,OAAO,IAAI,CAACC,IAAI,CAACJ,KAAK,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}