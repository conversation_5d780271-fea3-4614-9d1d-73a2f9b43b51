{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\charts\\\\DonutChart.tsx\",\n  _s = $RefreshSig$();\n/**\n * DonutChart Component using D3.js\n * Professional donut chart with center text and hover interactions\n */\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as d3 from 'd3';\nimport { Box } from '@mui/material';\n\n// ============================================================================\n// Types\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_COLORS = ['#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0', '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'];\n\n// ============================================================================\n// DonutChart Component\n// ============================================================================\n\nconst DonutChart = ({\n  data,\n  width = 300,\n  height = 300,\n  innerRadius = 60,\n  outerRadius = 120,\n  centerText,\n  colors = DEFAULT_COLORS,\n  onSegmentHover\n}) => {\n  _s();\n  const svgRef = useRef(null);\n  const [tooltip, setTooltip] = useState({\n    visible: false,\n    content: '',\n    x: 0,\n    y: 0\n  });\n  useEffect(() => {\n    if (!svgRef.current || !data.length) return;\n    const svg = d3.select(svgRef.current);\n    svg.selectAll('*').remove();\n    const radius = Math.min(width, height) / 2;\n    const g = svg.append('g').attr('transform', `translate(${width / 2}, ${height / 2})`);\n\n    // Create pie generator\n    const pie = d3.pie().value(d => d.value).sort(null);\n\n    // Create arc generator\n    const arc = d3.arc().innerRadius(innerRadius).outerRadius(outerRadius);\n\n    // Create hover arc generator\n    const hoverArc = d3.arc().innerRadius(innerRadius).outerRadius(outerRadius + 5);\n\n    // Calculate total for percentages\n    const total = data.reduce((sum, d) => sum + d.value, 0);\n\n    // Create arcs\n    const arcs = g.selectAll('.arc').data(pie(data)).enter().append('g').attr('class', 'arc');\n\n    // Add paths\n    arcs.append('path').attr('d', arc).attr('fill', (d, i) => d.data.color || colors[i % colors.length]).attr('stroke', '#fff').attr('stroke-width', 2).style('cursor', 'pointer').on('mouseenter', function (event, d) {\n      // Hover effect\n      d3.select(this).transition().duration(200).attr('d', hoverArc);\n\n      // Show tooltip\n      const percentage = (d.data.value / total * 100).toFixed(1);\n      const content = `${d.data.label}: ${d.data.value.toLocaleString()} (${percentage}%)`;\n      setTooltip({\n        visible: true,\n        content,\n        x: event.pageX,\n        y: event.pageY\n      });\n      onSegmentHover === null || onSegmentHover === void 0 ? void 0 : onSegmentHover(d.data);\n    }).on('mouseleave', function (event, d) {\n      // Remove hover effect\n      d3.select(this).transition().duration(200).attr('d', arc);\n\n      // Hide tooltip\n      setTooltip(prev => ({\n        ...prev,\n        visible: false\n      }));\n      onSegmentHover === null || onSegmentHover === void 0 ? void 0 : onSegmentHover(null);\n    }).on('mousemove', function (event) {\n      setTooltip(prev => ({\n        ...prev,\n        x: event.pageX,\n        y: event.pageY\n      }));\n    });\n\n    // Add center text\n    if (centerText) {\n      g.append('text').attr('text-anchor', 'middle').attr('dominant-baseline', 'middle').style('font-size', '18px').style('font-weight', '600').style('fill', '#333').text(centerText);\n    }\n\n    // Add total count in center\n    g.append('text').attr('text-anchor', 'middle').attr('dominant-baseline', 'middle').attr('y', centerText ? 20 : 0).style('font-size', '24px').style('font-weight', '700').style('fill', '#1976d2').text(total.toLocaleString());\n\n    // Animate on mount\n    arcs.selectAll('path').style('opacity', 0).transition().duration(800).delay((d, i) => i * 100).style('opacity', 1);\n  }, [data, width, height, innerRadius, outerRadius, centerText, colors, onSegmentHover]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      display: 'inline-block'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n      ref: svgRef,\n      width: width,\n      height: height,\n      style: {\n        overflow: 'visible'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), tooltip.visible && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'fixed',\n        left: tooltip.x + 10,\n        top: tooltip.y - 10,\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        color: 'white',\n        padding: '8px 12px',\n        borderRadius: '4px',\n        fontSize: '14px',\n        pointerEvents: 'none',\n        zIndex: 1000,\n        transform: 'translate(-50%, -100%)'\n      },\n      children: tooltip.content\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 187,\n    columnNumber: 5\n  }, this);\n};\n_s(DonutChart, \"8BR1MqDtrMYnHIvgCc74Y0zsh+E=\");\n_c = DonutChart;\nexport default DonutChart;\nvar _c;\n$RefreshReg$(_c, \"DonutChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "d3", "Box", "jsxDEV", "_jsxDEV", "DEFAULT_COLORS", "<PERSON><PERSON><PERSON><PERSON>", "data", "width", "height", "innerRadius", "outerRadius", "centerText", "colors", "onSegmentHover", "_s", "svgRef", "tooltip", "setTooltip", "visible", "content", "x", "y", "current", "length", "svg", "select", "selectAll", "remove", "radius", "Math", "min", "g", "append", "attr", "pie", "value", "d", "sort", "arc", "hoverArc", "total", "reduce", "sum", "arcs", "enter", "i", "color", "style", "on", "event", "transition", "duration", "percentage", "toFixed", "label", "toLocaleString", "pageX", "pageY", "prev", "text", "delay", "sx", "position", "display", "children", "ref", "overflow", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "left", "top", "backgroundColor", "padding", "borderRadius", "fontSize", "pointerEvents", "zIndex", "transform", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/charts/DonutChart.tsx"], "sourcesContent": ["/**\n * DonutChart Component using D3.js\n * Professional donut chart with center text and hover interactions\n */\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport * as d3 from 'd3';\nimport { Box, Tooltip } from '@mui/material';\n\n// ============================================================================\n// Types\n// ============================================================================\n\ninterface DonutChartData {\n  label: string;\n  value: number;\n  color?: string;\n}\n\ninterface DonutChartProps {\n  data: DonutChartData[];\n  width?: number;\n  height?: number;\n  innerRadius?: number;\n  outerRadius?: number;\n  centerText?: string;\n  colors?: string[];\n  onSegmentHover?: (data: DonutChartData | null) => void;\n}\n\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_COLORS = [\n  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',\n  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'\n];\n\n// ============================================================================\n// DonutChart Component\n// ============================================================================\n\nconst DonutChart: React.FC<DonutChartProps> = ({\n  data,\n  width = 300,\n  height = 300,\n  innerRadius = 60,\n  outerRadius = 120,\n  centerText,\n  colors = DEFAULT_COLORS,\n  onSegmentHover,\n}) => {\n  const svgRef = useRef<SVGSVGElement>(null);\n  const [tooltip, setTooltip] = useState<{\n    visible: boolean;\n    content: string;\n    x: number;\n    y: number;\n  }>({\n    visible: false,\n    content: '',\n    x: 0,\n    y: 0,\n  });\n\n  useEffect(() => {\n    if (!svgRef.current || !data.length) return;\n\n    const svg = d3.select(svgRef.current);\n    svg.selectAll('*').remove();\n\n    const radius = Math.min(width, height) / 2;\n    const g = svg\n      .append('g')\n      .attr('transform', `translate(${width / 2}, ${height / 2})`);\n\n    // Create pie generator\n    const pie = d3\n      .pie<DonutChartData>()\n      .value(d => d.value)\n      .sort(null);\n\n    // Create arc generator\n    const arc = d3\n      .arc<d3.PieArcDatum<DonutChartData>>()\n      .innerRadius(innerRadius)\n      .outerRadius(outerRadius);\n\n    // Create hover arc generator\n    const hoverArc = d3\n      .arc<d3.PieArcDatum<DonutChartData>>()\n      .innerRadius(innerRadius)\n      .outerRadius(outerRadius + 5);\n\n    // Calculate total for percentages\n    const total = data.reduce((sum, d) => sum + d.value, 0);\n\n    // Create arcs\n    const arcs = g\n      .selectAll('.arc')\n      .data(pie(data))\n      .enter()\n      .append('g')\n      .attr('class', 'arc');\n\n    // Add paths\n    arcs\n      .append('path')\n      .attr('d', arc)\n      .attr('fill', (d, i) => d.data.color || colors[i % colors.length])\n      .attr('stroke', '#fff')\n      .attr('stroke-width', 2)\n      .style('cursor', 'pointer')\n      .on('mouseenter', function(event, d) {\n        // Hover effect\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .attr('d', hoverArc);\n\n        // Show tooltip\n        const percentage = ((d.data.value / total) * 100).toFixed(1);\n        const content = `${d.data.label}: ${d.data.value.toLocaleString()} (${percentage}%)`;\n        \n        setTooltip({\n          visible: true,\n          content,\n          x: event.pageX,\n          y: event.pageY,\n        });\n\n        onSegmentHover?.(d.data);\n      })\n      .on('mouseleave', function(event, d) {\n        // Remove hover effect\n        d3.select(this)\n          .transition()\n          .duration(200)\n          .attr('d', arc);\n\n        // Hide tooltip\n        setTooltip(prev => ({ ...prev, visible: false }));\n        onSegmentHover?.(null);\n      })\n      .on('mousemove', function(event) {\n        setTooltip(prev => ({\n          ...prev,\n          x: event.pageX,\n          y: event.pageY,\n        }));\n      });\n\n    // Add center text\n    if (centerText) {\n      g.append('text')\n        .attr('text-anchor', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .style('font-size', '18px')\n        .style('font-weight', '600')\n        .style('fill', '#333')\n        .text(centerText);\n    }\n\n    // Add total count in center\n    g.append('text')\n      .attr('text-anchor', 'middle')\n      .attr('dominant-baseline', 'middle')\n      .attr('y', centerText ? 20 : 0)\n      .style('font-size', '24px')\n      .style('font-weight', '700')\n      .style('fill', '#1976d2')\n      .text(total.toLocaleString());\n\n    // Animate on mount\n    arcs\n      .selectAll('path')\n      .style('opacity', 0)\n      .transition()\n      .duration(800)\n      .delay((d, i) => i * 100)\n      .style('opacity', 1);\n\n  }, [data, width, height, innerRadius, outerRadius, centerText, colors, onSegmentHover]);\n\n  return (\n    <Box sx={{ position: 'relative', display: 'inline-block' }}>\n      <svg\n        ref={svgRef}\n        width={width}\n        height={height}\n        style={{ overflow: 'visible' }}\n      />\n      \n      {tooltip.visible && (\n        <Box\n          sx={{\n            position: 'fixed',\n            left: tooltip.x + 10,\n            top: tooltip.y - 10,\n            backgroundColor: 'rgba(0, 0, 0, 0.8)',\n            color: 'white',\n            padding: '8px 12px',\n            borderRadius: '4px',\n            fontSize: '14px',\n            pointerEvents: 'none',\n            zIndex: 1000,\n            transform: 'translate(-50%, -100%)',\n          }}\n        >\n          {tooltip.content}\n        </Box>\n      )}\n    </Box>\n  );\n};\n\nexport default DonutChart;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,SAASC,GAAG,QAAiB,eAAe;;AAE5C;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAmBA;AACA;AACA;;AAEA,MAAMC,cAAc,GAAG,CACrB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CACtD;;AAED;AACA;AACA;;AAEA,MAAMC,UAAqC,GAAGA,CAAC;EAC7CC,IAAI;EACJC,KAAK,GAAG,GAAG;EACXC,MAAM,GAAG,GAAG;EACZC,WAAW,GAAG,EAAE;EAChBC,WAAW,GAAG,GAAG;EACjBC,UAAU;EACVC,MAAM,GAAGR,cAAc;EACvBS;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,MAAM,GAAGjB,MAAM,CAAgB,IAAI,CAAC;EAC1C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAKnC;IACDmB,OAAO,EAAE,KAAK;IACdC,OAAO,EAAE,EAAE;IACXC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC,CAAC;EAEFxB,SAAS,CAAC,MAAM;IACd,IAAI,CAACkB,MAAM,CAACO,OAAO,IAAI,CAAChB,IAAI,CAACiB,MAAM,EAAE;IAErC,MAAMC,GAAG,GAAGxB,EAAE,CAACyB,MAAM,CAACV,MAAM,CAACO,OAAO,CAAC;IACrCE,GAAG,CAACE,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC;IAE3B,MAAMC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACvB,KAAK,EAAEC,MAAM,CAAC,GAAG,CAAC;IAC1C,MAAMuB,CAAC,GAAGP,GAAG,CACVQ,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,WAAW,EAAE,aAAa1B,KAAK,GAAG,CAAC,KAAKC,MAAM,GAAG,CAAC,GAAG,CAAC;;IAE9D;IACA,MAAM0B,GAAG,GAAGlC,EAAE,CACXkC,GAAG,CAAiB,CAAC,CACrBC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACD,KAAK,CAAC,CACnBE,IAAI,CAAC,IAAI,CAAC;;IAEb;IACA,MAAMC,GAAG,GAAGtC,EAAE,CACXsC,GAAG,CAAiC,CAAC,CACrC7B,WAAW,CAACA,WAAW,CAAC,CACxBC,WAAW,CAACA,WAAW,CAAC;;IAE3B;IACA,MAAM6B,QAAQ,GAAGvC,EAAE,CAChBsC,GAAG,CAAiC,CAAC,CACrC7B,WAAW,CAACA,WAAW,CAAC,CACxBC,WAAW,CAACA,WAAW,GAAG,CAAC,CAAC;;IAE/B;IACA,MAAM8B,KAAK,GAAGlC,IAAI,CAACmC,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAG,GAAGN,CAAC,CAACD,KAAK,EAAE,CAAC,CAAC;;IAEvD;IACA,MAAMQ,IAAI,GAAGZ,CAAC,CACXL,SAAS,CAAC,MAAM,CAAC,CACjBpB,IAAI,CAAC4B,GAAG,CAAC5B,IAAI,CAAC,CAAC,CACfsC,KAAK,CAAC,CAAC,CACPZ,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC;;IAEvB;IACAU,IAAI,CACDX,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,GAAG,EAAEK,GAAG,CAAC,CACdL,IAAI,CAAC,MAAM,EAAE,CAACG,CAAC,EAAES,CAAC,KAAKT,CAAC,CAAC9B,IAAI,CAACwC,KAAK,IAAIlC,MAAM,CAACiC,CAAC,GAAGjC,MAAM,CAACW,MAAM,CAAC,CAAC,CACjEU,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CACtBA,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACvBc,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAC1BC,EAAE,CAAC,YAAY,EAAE,UAASC,KAAK,EAAEb,CAAC,EAAE;MACnC;MACApC,EAAE,CAACyB,MAAM,CAAC,IAAI,CAAC,CACZyB,UAAU,CAAC,CAAC,CACZC,QAAQ,CAAC,GAAG,CAAC,CACblB,IAAI,CAAC,GAAG,EAAEM,QAAQ,CAAC;;MAEtB;MACA,MAAMa,UAAU,GAAG,CAAEhB,CAAC,CAAC9B,IAAI,CAAC6B,KAAK,GAAGK,KAAK,GAAI,GAAG,EAAEa,OAAO,CAAC,CAAC,CAAC;MAC5D,MAAMlC,OAAO,GAAG,GAAGiB,CAAC,CAAC9B,IAAI,CAACgD,KAAK,KAAKlB,CAAC,CAAC9B,IAAI,CAAC6B,KAAK,CAACoB,cAAc,CAAC,CAAC,KAAKH,UAAU,IAAI;MAEpFnC,UAAU,CAAC;QACTC,OAAO,EAAE,IAAI;QACbC,OAAO;QACPC,CAAC,EAAE6B,KAAK,CAACO,KAAK;QACdnC,CAAC,EAAE4B,KAAK,CAACQ;MACX,CAAC,CAAC;MAEF5C,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGuB,CAAC,CAAC9B,IAAI,CAAC;IAC1B,CAAC,CAAC,CACD0C,EAAE,CAAC,YAAY,EAAE,UAASC,KAAK,EAAEb,CAAC,EAAE;MACnC;MACApC,EAAE,CAACyB,MAAM,CAAC,IAAI,CAAC,CACZyB,UAAU,CAAC,CAAC,CACZC,QAAQ,CAAC,GAAG,CAAC,CACblB,IAAI,CAAC,GAAG,EAAEK,GAAG,CAAC;;MAEjB;MACArB,UAAU,CAACyC,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAExC,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;MACjDL,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,IAAI,CAAC;IACxB,CAAC,CAAC,CACDmC,EAAE,CAAC,WAAW,EAAE,UAASC,KAAK,EAAE;MAC/BhC,UAAU,CAACyC,IAAI,KAAK;QAClB,GAAGA,IAAI;QACPtC,CAAC,EAAE6B,KAAK,CAACO,KAAK;QACdnC,CAAC,EAAE4B,KAAK,CAACQ;MACX,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;;IAEJ;IACA,IAAI9C,UAAU,EAAE;MACdoB,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACbC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CACnCc,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BA,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAC3BA,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CACrBY,IAAI,CAAChD,UAAU,CAAC;IACrB;;IAEA;IACAoB,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACbC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CACnCA,IAAI,CAAC,GAAG,EAAEtB,UAAU,GAAG,EAAE,GAAG,CAAC,CAAC,CAC9BoC,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BA,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAC3BA,KAAK,CAAC,MAAM,EAAE,SAAS,CAAC,CACxBY,IAAI,CAACnB,KAAK,CAACe,cAAc,CAAC,CAAC,CAAC;;IAE/B;IACAZ,IAAI,CACDjB,SAAS,CAAC,MAAM,CAAC,CACjBqB,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBG,UAAU,CAAC,CAAC,CACZC,QAAQ,CAAC,GAAG,CAAC,CACbS,KAAK,CAAC,CAACxB,CAAC,EAAES,CAAC,KAAKA,CAAC,GAAG,GAAG,CAAC,CACxBE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;EAExB,CAAC,EAAE,CAACzC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,WAAW,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,cAAc,CAAC,CAAC;EAEvF,oBACEV,OAAA,CAACF,GAAG;IAAC4D,EAAE,EAAE;MAAEC,QAAQ,EAAE,UAAU;MAAEC,OAAO,EAAE;IAAe,CAAE;IAAAC,QAAA,gBACzD7D,OAAA;MACE8D,GAAG,EAAElD,MAAO;MACZR,KAAK,EAAEA,KAAM;MACbC,MAAM,EAAEA,MAAO;MACfuC,KAAK,EAAE;QAAEmB,QAAQ,EAAE;MAAU;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,EAEDtD,OAAO,CAACE,OAAO,iBACdf,OAAA,CAACF,GAAG;MACF4D,EAAE,EAAE;QACFC,QAAQ,EAAE,OAAO;QACjBS,IAAI,EAAEvD,OAAO,CAACI,CAAC,GAAG,EAAE;QACpBoD,GAAG,EAAExD,OAAO,CAACK,CAAC,GAAG,EAAE;QACnBoD,eAAe,EAAE,oBAAoB;QACrC3B,KAAK,EAAE,OAAO;QACd4B,OAAO,EAAE,UAAU;QACnBC,YAAY,EAAE,KAAK;QACnBC,QAAQ,EAAE,MAAM;QAChBC,aAAa,EAAE,MAAM;QACrBC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE;MACb,CAAE;MAAAf,QAAA,EAEDhD,OAAO,CAACG;IAAO;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxD,EAAA,CA5KIT,UAAqC;AAAA2E,EAAA,GAArC3E,UAAqC;AA8K3C,eAAeA,UAAU;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}