{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Redux Store Configuration for SkyGeni Dashboard\n * \n * This file sets up the Redux store with:\n * - Redux Toolkit for modern Redux patterns\n * - TypeScript support\n * - DevTools integration\n * - Middleware configuration\n * - Root state type exports\n */\n\nimport { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch, useSelector } from 'react-redux';\nimport dataSlice from './slices/dataSlice';\n\n// ============================================================================\n// Store Configuration\n// ============================================================================\n\n/**\n * Configure the Redux store with all slices and middleware\n */\nexport const store = configureStore({\n  reducer: {\n    data: dataSlice\n    // Add more slices here as the application grows\n    // ui: uiSlice,\n    // auth: authSlice,\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    // Configure serializable check for better performance\n    serializableCheck: {\n      ignoredActions: [\n      // Ignore these action types in serializable check\n      'persist/PERSIST', 'persist/REHYDRATE'],\n      ignoredPaths: [\n      // Ignore these paths in state for serializable check\n      'data.lastFetched']\n    },\n    // Enable immutability check in development\n    immutableCheck: {\n      warnAfter: 128 // Warn if immutability check takes longer than 128ms\n    }\n  }),\n  // Enable Redux DevTools in development\n  devTools: process.env.NODE_ENV !== 'production',\n  // Preloaded state (useful for SSR or initial state)\n  preloadedState: undefined\n});\n\n// ============================================================================\n// Type Definitions\n// ============================================================================\n\n/**\n * Root state type - inferred from the store\n */\n\n/**\n * App dispatch type - includes thunk types\n */\n\n// ============================================================================\n// Typed Hooks\n// ============================================================================\n\n/**\n * Typed version of useDispatch hook\n * Use this instead of plain useDispatch to get proper TypeScript support\n */\nexport const useAppDispatch = () => {\n  _s();\n  return useDispatch();\n};\n\n/**\n * Typed version of useSelector hook\n * Use this instead of plain useSelector to get proper TypeScript support\n */\n_s(useAppDispatch, \"jI3HA1r1Cumjdbu14H7G+TUj798=\", false, function () {\n  return [useDispatch];\n});\nexport const useAppSelector = useSelector;\n\n// ============================================================================\n// Store Utilities\n// ============================================================================\n\n/**\n * Get the current state of the store\n * Useful for debugging or testing\n */\nexport const getCurrentState = () => store.getState();\n\n/**\n * Subscribe to store changes\n * Returns an unsubscribe function\n */\nexport const subscribeToStore = listener => {\n  return store.subscribe(listener);\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\nif (process.env.NODE_ENV === 'development') {\n  // Make store available globally for debugging\n  window.__REDUX_STORE__ = store;\n\n  // Disable store logging to prevent infinite loops\n  // store.subscribe(() => {\n  //   const state = store.getState();\n  //   console.log('🔄 Store State Updated:', {\n  //     data: {\n  //       loading: state.data.loading,\n  //       error: state.data.error,\n  //       hasData: !!state.data.dashboardData,\n  //       lastFetched: state.data.lastFetched,\n  //     },\n  //   });\n  // });\n}\n\n// ============================================================================\n// Store Persistence (Optional)\n// ============================================================================\n\n/**\n * Save state to localStorage\n * This is a simple implementation - consider using redux-persist for production\n */\nexport const saveStateToLocalStorage = state => {\n  try {\n    const serializedState = JSON.stringify({\n      // Only save specific parts of the state\n      data: {\n        lastFetched: state.data.lastFetched\n        // Don't save actual data to avoid stale data issues\n      }\n    });\n    localStorage.setItem('skygeni-dashboard-state', serializedState);\n  } catch (error) {\n    console.warn('⚠️ Could not save state to localStorage:', error);\n  }\n};\n\n/**\n * Load state from localStorage\n */\nexport const loadStateFromLocalStorage = () => {\n  try {\n    const serializedState = localStorage.getItem('skygeni-dashboard-state');\n    if (serializedState === null) {\n      return undefined;\n    }\n    return JSON.parse(serializedState);\n  } catch (error) {\n    console.warn('⚠️ Could not load state from localStorage:', error);\n    return undefined;\n  }\n};\n\n/**\n * Clear persisted state\n */\nexport const clearPersistedState = () => {\n  try {\n    localStorage.removeItem('skygeni-dashboard-state');\n    console.log('✅ Persisted state cleared');\n  } catch (error) {\n    console.warn('⚠️ Could not clear persisted state:', error);\n  }\n};\n\n// ============================================================================\n// Store Event Listeners\n// ============================================================================\n\n/**\n * Listen for specific actions and perform side effects\n */\nstore.subscribe(() => {\n  const state = store.getState();\n\n  // Auto-save state to localStorage when data is fetched\n  if (state.data.loading === 'succeeded' && state.data.dashboardData) {\n    saveStateToLocalStorage(state);\n  }\n\n  // Clear error after successful data fetch\n  if (state.data.loading === 'succeeded' && state.data.error) {\n    // This would be handled by the reducer, but we can add additional logic here\n  }\n});\n\n// ============================================================================\n// Export Default Store\n// ============================================================================\n\nexport default store;", "map": {"version": 3, "names": ["configureStore", "useDispatch", "useSelector", "dataSlice", "store", "reducer", "data", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions", "ignoredPaths", "immutableCheck", "warnAfter", "devTools", "process", "env", "NODE_ENV", "preloadedState", "undefined", "useAppDispatch", "_s", "useAppSelector", "getCurrentState", "getState", "subscribeToStore", "listener", "subscribe", "window", "__REDUX_STORE__", "saveStateToLocalStorage", "state", "serializedState", "JSON", "stringify", "lastFetched", "localStorage", "setItem", "error", "console", "warn", "loadStateFromLocalStorage", "getItem", "parse", "clearPersistedState", "removeItem", "log", "loading", "dashboardData"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/redux/store.ts"], "sourcesContent": ["/**\n * Redux Store Configuration for SkyGeni Dashboard\n * \n * This file sets up the Redux store with:\n * - Redux Toolkit for modern Redux patterns\n * - TypeScript support\n * - DevTools integration\n * - Middleware configuration\n * - Root state type exports\n */\n\nimport { configureStore } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\nimport dataSlice from './slices/dataSlice';\n\n// ============================================================================\n// Store Configuration\n// ============================================================================\n\n/**\n * Configure the Redux store with all slices and middleware\n */\nexport const store = configureStore({\n  reducer: {\n    data: dataSlice,\n    // Add more slices here as the application grows\n    // ui: uiSlice,\n    // auth: authSlice,\n  },\n  \n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      // Configure serializable check for better performance\n      serializableCheck: {\n        ignoredActions: [\n          // Ignore these action types in serializable check\n          'persist/PERSIST',\n          'persist/REHYDRATE',\n        ],\n        ignoredPaths: [\n          // Ignore these paths in state for serializable check\n          'data.lastFetched',\n        ],\n      },\n      \n      // Enable immutability check in development\n      immutableCheck: {\n        warnAfter: 128, // Warn if immutability check takes longer than 128ms\n      },\n    }),\n  \n  // Enable Redux DevTools in development\n  devTools: process.env.NODE_ENV !== 'production',\n  \n  // Preloaded state (useful for SSR or initial state)\n  preloadedState: undefined,\n});\n\n// ============================================================================\n// Type Definitions\n// ============================================================================\n\n/**\n * Root state type - inferred from the store\n */\nexport type RootState = ReturnType<typeof store.getState>;\n\n/**\n * App dispatch type - includes thunk types\n */\nexport type AppDispatch = typeof store.dispatch;\n\n// ============================================================================\n// Typed Hooks\n// ============================================================================\n\n/**\n * Typed version of useDispatch hook\n * Use this instead of plain useDispatch to get proper TypeScript support\n */\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\n\n/**\n * Typed version of useSelector hook\n * Use this instead of plain useSelector to get proper TypeScript support\n */\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n\n// ============================================================================\n// Store Utilities\n// ============================================================================\n\n/**\n * Get the current state of the store\n * Useful for debugging or testing\n */\nexport const getCurrentState = (): RootState => store.getState();\n\n/**\n * Subscribe to store changes\n * Returns an unsubscribe function\n */\nexport const subscribeToStore = (listener: () => void): (() => void) => {\n  return store.subscribe(listener);\n};\n\n// ============================================================================\n// Development Utilities\n// ============================================================================\n\nif (process.env.NODE_ENV === 'development') {\n  // Make store available globally for debugging\n  (window as any).__REDUX_STORE__ = store;\n\n  // Disable store logging to prevent infinite loops\n  // store.subscribe(() => {\n  //   const state = store.getState();\n  //   console.log('🔄 Store State Updated:', {\n  //     data: {\n  //       loading: state.data.loading,\n  //       error: state.data.error,\n  //       hasData: !!state.data.dashboardData,\n  //       lastFetched: state.data.lastFetched,\n  //     },\n  //   });\n  // });\n}\n\n// ============================================================================\n// Store Persistence (Optional)\n// ============================================================================\n\n/**\n * Save state to localStorage\n * This is a simple implementation - consider using redux-persist for production\n */\nexport const saveStateToLocalStorage = (state: RootState): void => {\n  try {\n    const serializedState = JSON.stringify({\n      // Only save specific parts of the state\n      data: {\n        lastFetched: state.data.lastFetched,\n        // Don't save actual data to avoid stale data issues\n      },\n    });\n    localStorage.setItem('skygeni-dashboard-state', serializedState);\n  } catch (error) {\n    console.warn('⚠️ Could not save state to localStorage:', error);\n  }\n};\n\n/**\n * Load state from localStorage\n */\nexport const loadStateFromLocalStorage = (): Partial<RootState> | undefined => {\n  try {\n    const serializedState = localStorage.getItem('skygeni-dashboard-state');\n    if (serializedState === null) {\n      return undefined;\n    }\n    return JSON.parse(serializedState);\n  } catch (error) {\n    console.warn('⚠️ Could not load state from localStorage:', error);\n    return undefined;\n  }\n};\n\n/**\n * Clear persisted state\n */\nexport const clearPersistedState = (): void => {\n  try {\n    localStorage.removeItem('skygeni-dashboard-state');\n    console.log('✅ Persisted state cleared');\n  } catch (error) {\n    console.warn('⚠️ Could not clear persisted state:', error);\n  }\n};\n\n// ============================================================================\n// Store Event Listeners\n// ============================================================================\n\n/**\n * Listen for specific actions and perform side effects\n */\nstore.subscribe(() => {\n  const state = store.getState();\n  \n  // Auto-save state to localStorage when data is fetched\n  if (state.data.loading === 'succeeded' && state.data.dashboardData) {\n    saveStateToLocalStorage(state);\n  }\n  \n  // Clear error after successful data fetch\n  if (state.data.loading === 'succeeded' && state.data.error) {\n    // This would be handled by the reducer, but we can add additional logic here\n  }\n});\n\n// ============================================================================\n// Export Default Store\n// ============================================================================\n\nexport default store;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAA+BC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAC5E,OAAOC,SAAS,MAAM,oBAAoB;;AAE1C;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,KAAK,GAAGJ,cAAc,CAAC;EAClCK,OAAO,EAAE;IACPC,IAAI,EAAEH;IACN;IACA;IACA;EACF,CAAC;EAEDI,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnB;IACAC,iBAAiB,EAAE;MACjBC,cAAc,EAAE;MACd;MACA,iBAAiB,EACjB,mBAAmB,CACpB;MACDC,YAAY,EAAE;MACZ;MACA,kBAAkB;IAEtB,CAAC;IAED;IACAC,cAAc,EAAE;MACdC,SAAS,EAAE,GAAG,CAAE;IAClB;EACF,CAAC,CAAC;EAEJ;EACAC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;EAE/C;EACAC,cAAc,EAAEC;AAClB,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMpB,WAAW,CAAc,CAAC;AAAA;;AAE9D;AACA;AACA;AACA;AAHAoB,EAAA,CAFaD,cAAc;EAAA,QAASnB,WAAW;AAAA;AAM/C,OAAO,MAAMqB,cAA+C,GAAGpB,WAAW;;AAE1E;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMqB,eAAe,GAAGA,CAAA,KAAiBnB,KAAK,CAACoB,QAAQ,CAAC,CAAC;;AAEhE;AACA;AACA;AACA;AACA,OAAO,MAAMC,gBAAgB,GAAIC,QAAoB,IAAmB;EACtE,OAAOtB,KAAK,CAACuB,SAAS,CAACD,QAAQ,CAAC;AAClC,CAAC;;AAED;AACA;AACA;;AAEA,IAAIX,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1C;EACCW,MAAM,CAASC,eAAe,GAAGzB,KAAK;;EAEvC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,uBAAuB,GAAIC,KAAgB,IAAW;EACjE,IAAI;IACF,MAAMC,eAAe,GAAGC,IAAI,CAACC,SAAS,CAAC;MACrC;MACA5B,IAAI,EAAE;QACJ6B,WAAW,EAAEJ,KAAK,CAACzB,IAAI,CAAC6B;QACxB;MACF;IACF,CAAC,CAAC;IACFC,YAAY,CAACC,OAAO,CAAC,yBAAyB,EAAEL,eAAe,CAAC;EAClE,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEF,KAAK,CAAC;EACjE;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,yBAAyB,GAAGA,CAAA,KAAsC;EAC7E,IAAI;IACF,MAAMT,eAAe,GAAGI,YAAY,CAACM,OAAO,CAAC,yBAAyB,CAAC;IACvE,IAAIV,eAAe,KAAK,IAAI,EAAE;MAC5B,OAAOb,SAAS;IAClB;IACA,OAAOc,IAAI,CAACU,KAAK,CAACX,eAAe,CAAC;EACpC,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,4CAA4C,EAAEF,KAAK,CAAC;IACjE,OAAOnB,SAAS;EAClB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMyB,mBAAmB,GAAGA,CAAA,KAAY;EAC7C,IAAI;IACFR,YAAY,CAACS,UAAU,CAAC,yBAAyB,CAAC;IAClDN,OAAO,CAACO,GAAG,CAAC,2BAA2B,CAAC;EAC1C,CAAC,CAAC,OAAOR,KAAK,EAAE;IACdC,OAAO,CAACC,IAAI,CAAC,qCAAqC,EAAEF,KAAK,CAAC;EAC5D;AACF,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACAlC,KAAK,CAACuB,SAAS,CAAC,MAAM;EACpB,MAAMI,KAAK,GAAG3B,KAAK,CAACoB,QAAQ,CAAC,CAAC;;EAE9B;EACA,IAAIO,KAAK,CAACzB,IAAI,CAACyC,OAAO,KAAK,WAAW,IAAIhB,KAAK,CAACzB,IAAI,CAAC0C,aAAa,EAAE;IAClElB,uBAAuB,CAACC,KAAK,CAAC;EAChC;;EAEA;EACA,IAAIA,KAAK,CAACzB,IAAI,CAACyC,OAAO,KAAK,WAAW,IAAIhB,KAAK,CAACzB,IAAI,CAACgC,KAAK,EAAE;IAC1D;EAAA;AAEJ,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,eAAelC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}