{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport { Box, Grid, Typography, Paper, Alert, Fade, useTheme } from '@mui/material';\nimport { CustomerTypeCard, AccountIndustryCard, TeamCard, ACVRangeCard } from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0 // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.summary)) return null;\n    const {\n      summary\n    } = dashboardData;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 1,\n      sx: {\n        p: 2,\n        mb: 3,\n        border: `1px solid ${theme.palette.divider}`,\n        borderRadius: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          fontWeight: 600,\n          color: \"text.primary\",\n          children: \"Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), lastFetched && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          color: \"text.secondary\",\n          children: [\"Last updated: \", new Date(lastFetched).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: summary.totalCustomers.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                color: 'success.main',\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: [\"$\", summary.totalRevenue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                color: 'info.main',\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: summary.totalTeams\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Active Teams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 1.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              sx: {\n                color: 'warning.main',\n                fontWeight: 600,\n                mb: 0.5\n              },\n              children: [\"$\", Math.round(summary.averageACV).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Average ACV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Loader, {\n          size: \"large\",\n          message: \"Loading dashboard data...\",\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          error: error,\n          title: \"Failed to Load Dashboard\",\n          showRetryButton: true,\n          onRetry: refetch,\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          action: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refetch,\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this),\n          children: \"No dashboard data available. Please check your data sources.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"SkyGeni Dashboard\",\n    children: /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 500,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            gutterBottom: true,\n            fontWeight: 600,\n            color: \"text.primary\",\n            sx: {\n              mb: 0.5\n            },\n            children: \"Data Analytics Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Customer insights and performance metrics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryStats, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: {\n            xs: 2,\n            sm: 3,\n            md: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 600,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 700,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 800,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(TeamCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 900,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 317,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"pF5peWU5QlZmCSoxyU0BtdoBz10=\", false, function () {\n  return [useTheme, useData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Typography", "Paper", "<PERSON><PERSON>", "Fade", "useTheme", "CustomerTypeCard", "AccountIndustryCard", "TeamCard", "ACVRangeCard", "Layout", "Loader", "ErrorMessage", "useData", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "theme", "dashboardData", "isLoading", "isError", "error", "isEmpty", "refetch", "lastFetched", "autoFetch", "refreshInterval", "SummaryStats", "summary", "elevation", "sx", "p", "mb", "border", "palette", "divider", "borderRadius", "children", "display", "alignItems", "justifyContent", "variant", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Date", "toLocaleString", "container", "spacing", "item", "xs", "sm", "textAlign", "totalCustomers", "totalRevenue", "totalTeams", "Math", "round", "averageACV", "title", "minHeight", "size", "message", "centered", "showRetryButton", "onRetry", "severity", "action", "onClick", "in", "timeout", "component", "gutterBottom", "md", "lg", "xl", "height", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Paper,\n  Alert,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  CustomerTypeCard,\n  AccountIndustryCard,\n  TeamCard,\n  ACVRangeCard,\n} from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\n\nconst Dashboard: React.FC = () => {\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched,\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0, // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats: React.FC = () => {\n    if (!dashboardData?.summary) return null;\n\n    const { summary } = dashboardData;\n\n    return (\n      <Paper\n        elevation={1}\n        sx={{\n          p: 2,\n          mb: 3,\n          border: `1px solid ${theme.palette.divider}`,\n          borderRadius: 1,\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>\n          <Typography variant=\"h6\" fontWeight={600} color=\"text.primary\">\n            Overview\n          </Typography>\n          {lastFetched && (\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              Last updated: {new Date(lastFetched).toLocaleString()}\n            </Typography>\n          )}\n        </Box>\n\n        <Grid container spacing={2}>\n          <Grid item xs={6} sm={3}>\n            <Box sx={{ textAlign: 'center', p: 1.5 }}>\n              <Typography\n                variant=\"h5\"\n                sx={{\n                  color: 'primary.main',\n                  fontWeight: 600,\n                  mb: 0.5,\n                }}\n              >\n                {summary.totalCustomers.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n              >\n                Total Customers\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box sx={{ textAlign: 'center', p: 1.5 }}>\n              <Typography\n                variant=\"h5\"\n                sx={{\n                  color: 'success.main',\n                  fontWeight: 600,\n                  mb: 0.5,\n                }}\n              >\n                ${summary.totalRevenue.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n              >\n                Total Revenue\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box sx={{ textAlign: 'center', p: 1.5 }}>\n              <Typography\n                variant=\"h5\"\n                sx={{\n                  color: 'info.main',\n                  fontWeight: 600,\n                  mb: 0.5,\n                }}\n              >\n                {summary.totalTeams}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n              >\n                Active Teams\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box sx={{ textAlign: 'center', p: 1.5 }}>\n              <Typography\n                variant=\"h5\"\n                sx={{\n                  color: 'warning.main',\n                  fontWeight: 600,\n                  mb: 0.5,\n                }}\n              >\n                ${Math.round(summary.averageACV).toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n              >\n                Average ACV\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Loader\n            size=\"large\"\n            message=\"Loading dashboard data...\"\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <ErrorMessage\n            error={error}\n            title=\"Failed to Load Dashboard\"\n            showRetryButton={true}\n            onRetry={refetch}\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Alert\n            severity=\"info\"\n            action={\n              <button onClick={refetch}>\n                Retry\n              </button>\n            }\n          >\n            No dashboard data available. Please check your data sources.\n          </Alert>\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return (\n    <Layout title=\"SkyGeni Dashboard\">\n      <Fade in={true} timeout={500}>\n        <Box>\n          {/* Page Header */}\n          <Box sx={{ mb: 3 }}>\n            <Typography\n              variant=\"h5\"\n              component=\"h1\"\n              gutterBottom\n              fontWeight={600}\n              color=\"text.primary\"\n              sx={{ mb: 0.5 }}\n            >\n              Data Analytics Dashboard\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n            >\n              Customer insights and performance metrics\n            </Typography>\n          </Box>\n\n          {/* Summary Statistics */}\n          <SummaryStats />\n\n          {/* Data Cards Grid */}\n          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n            {/* Customer Types Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={600}>\n                <Box sx={{ height: '100%' }}>\n                  <CustomerTypeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Account Industries Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={700}>\n                <Box sx={{ height: '100%' }}>\n                  <AccountIndustryCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Teams Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={800}>\n                <Box sx={{ height: '100%' }}>\n                  <TeamCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* ACV Ranges Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={900}>\n                <Box sx={{ height: '100%' }}>\n                  <ACVRangeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n          </Grid>\n\n          {/* Additional Information */}\n          <Box sx={{ mt: 4, textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\n            </Typography>\n          </Box>\n        </Box>\n      </Fade>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJc,aAAa;IACbC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC,GAAGZ,OAAO,CAAC;IACVa,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,CAAC,CAAE;EACtB,CAAC,CAAC;;EAEF;EACA;EACA;;EAEA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACT,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEU,OAAO,GAAE,OAAO,IAAI;IAExC,MAAM;MAAEA;IAAQ,CAAC,GAAGV,aAAa;IAEjC,oBACEJ,OAAA,CAACb,KAAK;MACJ4B,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACFC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,aAAahB,KAAK,CAACiB,OAAO,CAACC,OAAO,EAAE;QAC5CC,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBAEFvB,OAAA,CAAChB,GAAG;QAACgC,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,cAAc,EAAE,eAAe;UAAER,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACzFvB,OAAA,CAACd,UAAU;UAACyC,OAAO,EAAC,IAAI;UAACC,UAAU,EAAE,GAAI;UAACC,KAAK,EAAC,cAAc;UAAAN,QAAA,EAAC;QAE/D;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZvB,WAAW,iBACVV,OAAA,CAACd,UAAU;UAACyC,OAAO,EAAC,SAAS;UAACE,KAAK,EAAC,gBAAgB;UAAAN,QAAA,GAAC,gBACrC,EAAC,IAAIW,IAAI,CAACxB,WAAW,CAAC,CAACyB,cAAc,CAAC,CAAC;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENjC,OAAA,CAACf,IAAI;QAACmD,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAd,QAAA,gBACzBvB,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YAACgC,EAAE,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAExB,CAAC,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACvCvB,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,IAAI;cACZX,EAAE,EAAE;gBACFa,KAAK,EAAE,cAAc;gBACrBD,UAAU,EAAE,GAAG;gBACfV,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,EAEDT,OAAO,CAAC4B,cAAc,CAACP,cAAc,CAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbjC,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,OAAO;cACfE,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EACvB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YAACgC,EAAE,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAExB,CAAC,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACvCvB,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,IAAI;cACZX,EAAE,EAAE;gBACFa,KAAK,EAAE,cAAc;gBACrBD,UAAU,EAAE,GAAG;gBACfV,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,GACH,GACE,EAACT,OAAO,CAAC6B,YAAY,CAACR,cAAc,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbjC,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,OAAO;cACfE,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EACvB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YAACgC,EAAE,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAExB,CAAC,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACvCvB,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,IAAI;cACZX,EAAE,EAAE;gBACFa,KAAK,EAAE,WAAW;gBAClBD,UAAU,EAAE,GAAG;gBACfV,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,EAEDT,OAAO,CAAC8B;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbjC,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,OAAO;cACfE,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EACvB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACqD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAjB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YAACgC,EAAE,EAAE;cAAEyB,SAAS,EAAE,QAAQ;cAAExB,CAAC,EAAE;YAAI,CAAE;YAAAM,QAAA,gBACvCvB,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,IAAI;cACZX,EAAE,EAAE;gBACFa,KAAK,EAAE,cAAc;gBACrBD,UAAU,EAAE,GAAG;gBACfV,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,GACH,GACE,EAACsB,IAAI,CAACC,KAAK,CAAChC,OAAO,CAACiC,UAAU,CAAC,CAACZ,cAAc,CAAC,CAAC;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACbjC,OAAA,CAACd,UAAU;cACTyC,OAAO,EAAC,OAAO;cACfE,KAAK,EAAC,gBAAgB;cAAAN,QAAA,EACvB;YAED;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA;EACA;;EAEA,IAAI5B,SAAS,EAAE;IACb,oBACEL,OAAA,CAACL,MAAM;MAACqD,KAAK,EAAC,mBAAmB;MAAAzB,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpBwB,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,eAEFvB,OAAA,CAACJ,MAAM;UACLsD,IAAI,EAAC,OAAO;UACZC,OAAO,EAAC,2BAA2B;UACnCC,QAAQ,EAAE;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAI3B,OAAO,EAAE;IACX,oBACEN,OAAA,CAACL,MAAM;MAACqD,KAAK,EAAC,mBAAmB;MAAAzB,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpBwB,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,eAEFvB,OAAA,CAACH,YAAY;UACXU,KAAK,EAAEA,KAAM;UACbyC,KAAK,EAAC,0BAA0B;UAChCK,eAAe,EAAE,IAAK;UACtBC,OAAO,EAAE7C,OAAQ;UACjB2C,QAAQ,EAAE;QAAK;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAIzB,OAAO,EAAE;IACX,oBACER,OAAA,CAACL,MAAM;MAACqD,KAAK,EAAC,mBAAmB;MAAAzB,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfE,cAAc,EAAE,QAAQ;UACxBD,UAAU,EAAE,QAAQ;UACpBwB,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,eAEFvB,OAAA,CAACZ,KAAK;UACJmE,QAAQ,EAAC,MAAM;UACfC,MAAM,eACJxD,OAAA;YAAQyD,OAAO,EAAEhD,OAAQ;YAAAc,QAAA,EAAC;UAE1B;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAV,QAAA,EACF;QAED;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,oBACEjC,OAAA,CAACL,MAAM;IAACqD,KAAK,EAAC,mBAAmB;IAAAzB,QAAA,eAC/BvB,OAAA,CAACX,IAAI;MAACqE,EAAE,EAAE,IAAK;MAACC,OAAO,EAAE,GAAI;MAAApC,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;QAAAuC,QAAA,gBAEFvB,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAK,QAAA,gBACjBvB,OAAA,CAACd,UAAU;YACTyC,OAAO,EAAC,IAAI;YACZiC,SAAS,EAAC,IAAI;YACdC,YAAY;YACZjC,UAAU,EAAE,GAAI;YAChBC,KAAK,EAAC,cAAc;YACpBb,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAI,CAAE;YAAAK,QAAA,EACjB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACd,UAAU;YACTyC,OAAO,EAAC,OAAO;YACfE,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EACvB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNjC,OAAA,CAACa,YAAY;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhBjC,OAAA,CAACf,IAAI;UAACmD,SAAS;UAACC,OAAO,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEsB,EAAE,EAAE;UAAE,CAAE;UAAAvC,QAAA,gBAE/CvB,OAAA,CAACf,IAAI;YAACqD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACuB,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACqE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAApC,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEiD,MAAM,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,eAC1BvB,OAAA,CAACT,gBAAgB;kBAACwB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACqD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACuB,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACqE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAApC,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEiD,MAAM,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,eAC1BvB,OAAA,CAACR,mBAAmB;kBAACuB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACqD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACuB,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACqE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAApC,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEiD,MAAM,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,eAC1BvB,OAAA,CAACP,QAAQ;kBAACsB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACqD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACuB,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAzC,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACqE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAApC,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEiD,MAAM,EAAE;gBAAO,CAAE;gBAAA1C,QAAA,eAC1BvB,OAAA,CAACN,YAAY;kBAACqB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPjC,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEkD,EAAE,EAAE,CAAC;YAAEzB,SAAS,EAAE;UAAS,CAAE;UAAAlB,QAAA,eACtCvB,OAAA,CAACd,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACE,KAAK,EAAC,gBAAgB;YAAAN,QAAA,EAAC;UAEnD;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAAC/B,EAAA,CAxSID,SAAmB;EAAA,QACTX,QAAQ,EASlBQ,OAAO;AAAA;AAAAqE,EAAA,GAVPlE,SAAmB;AA0SzB,eAAeA,SAAS;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}