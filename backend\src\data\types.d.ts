/**
 * Data-specific type definitions for JSON data files
 * These types match the structure of the JSON data files that will be provided
 */

// ============================================================================
// Raw JSON Data Types (matching the actual JSON file structures)
// ============================================================================

/**
 * Raw Customer Type data from CustomerType.json
 * This interface should match the exact structure of the JSON file
 */
export interface RawCustomerTypeData {
  [key: string]: any; // Flexible structure until we see the actual JSON
}

/**
 * Raw Account Industry data from AccountIndustry.json
 * This interface should match the exact structure of the JSON file
 */
export interface RawAccountIndustryData {
  [key: string]: any; // Flexible structure until we see the actual JSON
}

/**
 * Raw Team data from Team.json
 * This interface should match the exact structure of the JSON file
 */
export interface RawTeamData {
  [key: string]: any; // Flexible structure until we see the actual JSON
}

/**
 * Raw ACV Range data from ACVRange.json
 * This interface should match the exact structure of the JSON file
 */
export interface RawACVRangeData {
  [key: string]: any; // Flexible structure until we see the actual JSON
}

// ============================================================================
// Data Processing Types
// ============================================================================

/**
 * Data loader configuration
 */
export interface DataLoaderConfig {
  dataDirectory: string;
  enableCaching: boolean;
  cacheTimeout: number; // in milliseconds
}

/**
 * Data validation result
 */
export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Data transformation options
 */
export interface DataTransformOptions {
  normalizePercentages: boolean;
  calculateTotals: boolean;
  sortBy?: string;
  filterEmpty: boolean;
}

// ============================================================================
// Data Service Types
// ============================================================================

/**
 * Data service interface for handling JSON data operations
 */
export interface DataService {
  loadCustomerTypes(): Promise<any[]>;
  loadAccountIndustries(): Promise<any[]>;
  loadTeams(): Promise<any[]>;
  loadACVRanges(): Promise<any[]>;
  loadAllData(): Promise<{
    customerTypes: any[];
    accountIndustries: any[];
    teams: any[];
    acvRanges: any[];
  }>;
  validateData(data: any, type: string): DataValidationResult;
  transformData(data: any[], options: DataTransformOptions): any[];
}

// ============================================================================
// File System Types
// ============================================================================

/**
 * JSON file metadata
 */
export interface JsonFileInfo {
  filename: string;
  path: string;
  size: number;
  lastModified: Date;
  isValid: boolean;
}

/**
 * Data file registry
 */
export interface DataFileRegistry {
  customerType: JsonFileInfo;
  accountIndustry: JsonFileInfo;
  team: JsonFileInfo;
  acvRange: JsonFileInfo;
}

// ============================================================================
// Cache Types
// ============================================================================

/**
 * Cache entry structure
 */
export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiresAt: number;
  key: string;
}

/**
 * Cache manager interface
 */
export interface CacheManager {
  get<T>(key: string): CacheEntry<T> | null;
  set<T>(key: string, data: T, ttl?: number): void;
  delete(key: string): boolean;
  clear(): void;
  isExpired(entry: CacheEntry): boolean;
}
