{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\layout\\\\CleanFooter.tsx\",\n  _s = $RefreshSig$();\n/**\n * CleanFooter Component for SkyGeni Dashboard\n * Minimal, professional footer with version info and links\n */\n\nimport React from 'react';\nimport { Box, Typography, IconButton, Tooltip, useTheme, alpha } from '@mui/material';\nimport { GitHub as GitHubIcon, Description as DocumentationIcon } from '@mui/icons-material';\n\n// ============================================================================\n// Types\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// CleanFooter Component\n// ============================================================================\n\nconst CleanFooter = ({\n  lastUpdated,\n  version = 'v1.0.0',\n  githubUrl = 'https://github.com/skygeni/dashboard',\n  docsUrl = 'https://docs.skygeni.com',\n  className\n}) => {\n  _s();\n  const theme = useTheme();\n  const formatTimestamp = timestamp => {\n    if (!timestamp) return 'Never';\n    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n    return date.toLocaleString();\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    component: \"footer\",\n    className: className,\n    sx: {\n      position: 'sticky',\n      bottom: 0,\n      width: '100%',\n      backgroundColor: alpha(theme.palette.background.paper, 0.95),\n      backdropFilter: 'blur(8px)',\n      borderTop: `1px solid ${theme.palette.divider}`,\n      py: 1.5,\n      px: 3,\n      mt: 'auto'\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        flexWrap: 'wrap',\n        gap: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            fontSize: '0.75rem'\n          },\n          children: [\"Last Updated: \", formatTimestamp(lastUpdated)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            fontSize: '0.75rem',\n            display: {\n              xs: 'none',\n              sm: 'block'\n            }\n          },\n          children: version\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: {\n            xs: 'none',\n            md: 'block'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: 'text.secondary',\n            fontSize: '0.75rem',\n            textAlign: 'center'\n          },\n          children: \"Built with React, TypeScript, MUI, D3.js\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"GitHub Repository\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            href: githubUrl,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            size: \"small\",\n            sx: {\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main',\n                transform: 'scale(1.1)'\n              },\n              transition: 'all 0.2s ease-in-out'\n            },\n            children: /*#__PURE__*/_jsxDEV(GitHubIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Documentation\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            href: docsUrl,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            size: \"small\",\n            sx: {\n              color: 'text.secondary',\n              '&:hover': {\n                color: 'primary.main',\n                transform: 'scale(1.1)'\n              },\n              transition: 'all 0.2s ease-in-out'\n            },\n            children: /*#__PURE__*/_jsxDEV(DocumentationIcon, {\n              fontSize: \"small\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n_s(CleanFooter, \"VrMvFCCB9Haniz3VCRPNUiCauHs=\", false, function () {\n  return [useTheme];\n});\n_c = CleanFooter;\nexport default CleanFooter;\nvar _c;\n$RefreshReg$(_c, \"CleanFooter\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "useTheme", "alpha", "GitHub", "GitHubIcon", "Description", "DocumentationIcon", "jsxDEV", "_jsxDEV", "CleanFooter", "lastUpdated", "version", "githubUrl", "docsUrl", "className", "_s", "theme", "formatTimestamp", "timestamp", "date", "Date", "toLocaleString", "component", "sx", "position", "bottom", "width", "backgroundColor", "palette", "background", "paper", "<PERSON><PERSON>ilter", "borderTop", "divider", "py", "px", "mt", "children", "display", "justifyContent", "alignItems", "flexWrap", "gap", "variant", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "xs", "sm", "md", "textAlign", "title", "href", "target", "rel", "size", "transform", "transition", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/layout/CleanFooter.tsx"], "sourcesContent": ["/**\n * CleanFooter Component for SkyGeni Dashboard\n * Minimal, professional footer with version info and links\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Tooltip,\n  useTheme,\n  alpha,\n} from '@mui/material';\nimport {\n  GitHub as GitHubIcon,\n  Description as DocumentationIcon,\n} from '@mui/icons-material';\n\n// ============================================================================\n// Types\n// ============================================================================\n\ninterface CleanFooterProps {\n  lastUpdated?: Date | string | null;\n  version?: string;\n  githubUrl?: string;\n  docsUrl?: string;\n  className?: string;\n}\n\n// ============================================================================\n// CleanFooter Component\n// ============================================================================\n\nconst CleanFooter: React.FC<CleanFooterProps> = ({\n  lastUpdated,\n  version = 'v1.0.0',\n  githubUrl = 'https://github.com/skygeni/dashboard',\n  docsUrl = 'https://docs.skygeni.com',\n  className,\n}) => {\n  const theme = useTheme();\n\n  const formatTimestamp = (timestamp: Date | string | null): string => {\n    if (!timestamp) return 'Never';\n    \n    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n    return date.toLocaleString();\n  };\n\n  return (\n    <Box\n      component=\"footer\"\n      className={className}\n      sx={{\n        position: 'sticky',\n        bottom: 0,\n        width: '100%',\n        backgroundColor: alpha(theme.palette.background.paper, 0.95),\n        backdropFilter: 'blur(8px)',\n        borderTop: `1px solid ${theme.palette.divider}`,\n        py: 1.5,\n        px: 3,\n        mt: 'auto',\n      }}\n    >\n      <Box\n        sx={{\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          flexWrap: 'wrap',\n          gap: 2,\n        }}\n      >\n        {/* Left Section - Last Updated */}\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n            }}\n          >\n            Last Updated: {formatTimestamp(lastUpdated)}\n          </Typography>\n          \n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              display: { xs: 'none', sm: 'block' },\n            }}\n          >\n            {version}\n          </Typography>\n        </Box>\n\n        {/* Center Section - Powered By */}\n        <Box sx={{ display: { xs: 'none', md: 'block' } }}>\n          <Typography\n            variant=\"body2\"\n            sx={{\n              color: 'text.secondary',\n              fontSize: '0.75rem',\n              textAlign: 'center',\n            }}\n          >\n            Built with React, TypeScript, MUI, D3.js\n          </Typography>\n        </Box>\n\n        {/* Right Section - Links */}\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Tooltip title=\"GitHub Repository\">\n            <IconButton\n              href={githubUrl}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              size=\"small\"\n              sx={{\n                color: 'text.secondary',\n                '&:hover': {\n                  color: 'primary.main',\n                  transform: 'scale(1.1)',\n                },\n                transition: 'all 0.2s ease-in-out',\n              }}\n            >\n              <GitHubIcon fontSize=\"small\" />\n            </IconButton>\n          </Tooltip>\n\n          <Tooltip title=\"Documentation\">\n            <IconButton\n              href={docsUrl}\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              size=\"small\"\n              sx={{\n                color: 'text.secondary',\n                '&:hover': {\n                  color: 'primary.main',\n                  transform: 'scale(1.1)',\n                },\n                transition: 'all 0.2s ease-in-out',\n              }}\n            >\n              <DocumentationIcon fontSize=\"small\" />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport default CleanFooter;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,KAAK,QACA,eAAe;AACtB,SACEC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,iBAAiB,QAC3B,qBAAqB;;AAE5B;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA;AACA;AACA;;AAEA,MAAMC,WAAuC,GAAGA,CAAC;EAC/CC,WAAW;EACXC,OAAO,GAAG,QAAQ;EAClBC,SAAS,GAAG,sCAAsC;EAClDC,OAAO,GAAG,0BAA0B;EACpCC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAGf,QAAQ,CAAC,CAAC;EAExB,MAAMgB,eAAe,GAAIC,SAA+B,IAAa;IACnE,IAAI,CAACA,SAAS,EAAE,OAAO,OAAO;IAE9B,MAAMC,IAAI,GAAGD,SAAS,YAAYE,IAAI,GAAGF,SAAS,GAAG,IAAIE,IAAI,CAACF,SAAS,CAAC;IACxE,OAAOC,IAAI,CAACE,cAAc,CAAC,CAAC;EAC9B,CAAC;EAED,oBACEb,OAAA,CAACX,GAAG;IACFyB,SAAS,EAAC,QAAQ;IAClBR,SAAS,EAAEA,SAAU;IACrBS,EAAE,EAAE;MACFC,QAAQ,EAAE,QAAQ;MAClBC,MAAM,EAAE,CAAC;MACTC,KAAK,EAAE,MAAM;MACbC,eAAe,EAAEzB,KAAK,CAACc,KAAK,CAACY,OAAO,CAACC,UAAU,CAACC,KAAK,EAAE,IAAI,CAAC;MAC5DC,cAAc,EAAE,WAAW;MAC3BC,SAAS,EAAE,aAAahB,KAAK,CAACY,OAAO,CAACK,OAAO,EAAE;MAC/CC,EAAE,EAAE,GAAG;MACPC,EAAE,EAAE,CAAC;MACLC,EAAE,EAAE;IACN,CAAE;IAAAC,QAAA,eAEF7B,OAAA,CAACX,GAAG;MACF0B,EAAE,EAAE;QACFe,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,eAAe;QAC/BC,UAAU,EAAE,QAAQ;QACpBC,QAAQ,EAAE,MAAM;QAChBC,GAAG,EAAE;MACP,CAAE;MAAAL,QAAA,gBAGF7B,OAAA,CAACX,GAAG;QAAC0B,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACzD7B,OAAA,CAACV,UAAU;UACT6C,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFqB,KAAK,EAAE,gBAAgB;YACvBC,QAAQ,EAAE;UACZ,CAAE;UAAAR,QAAA,GACH,gBACe,EAACpB,eAAe,CAACP,WAAW,CAAC;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEbzC,OAAA,CAACV,UAAU;UACT6C,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFqB,KAAK,EAAE,gBAAgB;YACvBC,QAAQ,EAAE,SAAS;YACnBP,OAAO,EAAE;cAAEY,EAAE,EAAE,MAAM;cAAEC,EAAE,EAAE;YAAQ;UACrC,CAAE;UAAAd,QAAA,EAED1B;QAAO;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNzC,OAAA,CAACX,GAAG;QAAC0B,EAAE,EAAE;UAAEe,OAAO,EAAE;YAAEY,EAAE,EAAE,MAAM;YAAEE,EAAE,EAAE;UAAQ;QAAE,CAAE;QAAAf,QAAA,eAChD7B,OAAA,CAACV,UAAU;UACT6C,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFqB,KAAK,EAAE,gBAAgB;YACvBC,QAAQ,EAAE,SAAS;YACnBQ,SAAS,EAAE;UACb,CAAE;UAAAhB,QAAA,EACH;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNzC,OAAA,CAACX,GAAG;QAAC0B,EAAE,EAAE;UAAEe,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAEE,GAAG,EAAE;QAAE,CAAE;QAAAL,QAAA,gBACzD7B,OAAA,CAACR,OAAO;UAACsD,KAAK,EAAC,mBAAmB;UAAAjB,QAAA,eAChC7B,OAAA,CAACT,UAAU;YACTwD,IAAI,EAAE3C,SAAU;YAChB4C,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBC,IAAI,EAAC,OAAO;YACZnC,EAAE,EAAE;cACFqB,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBACTA,KAAK,EAAE,cAAc;gBACrBe,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAvB,QAAA,eAEF7B,OAAA,CAACJ,UAAU;cAACyC,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAEVzC,OAAA,CAACR,OAAO;UAACsD,KAAK,EAAC,eAAe;UAAAjB,QAAA,eAC5B7B,OAAA,CAACT,UAAU;YACTwD,IAAI,EAAE1C,OAAQ;YACd2C,MAAM,EAAC,QAAQ;YACfC,GAAG,EAAC,qBAAqB;YACzBC,IAAI,EAAC,OAAO;YACZnC,EAAE,EAAE;cACFqB,KAAK,EAAE,gBAAgB;cACvB,SAAS,EAAE;gBACTA,KAAK,EAAE,cAAc;gBACrBe,SAAS,EAAE;cACb,CAAC;cACDC,UAAU,EAAE;YACd,CAAE;YAAAvB,QAAA,eAEF7B,OAAA,CAACF,iBAAiB;cAACuC,QAAQ,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClC,EAAA,CA1HIN,WAAuC;EAAA,QAO7BR,QAAQ;AAAA;AAAA4D,EAAA,GAPlBpD,WAAuC;AA4H7C,eAAeA,WAAW;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}