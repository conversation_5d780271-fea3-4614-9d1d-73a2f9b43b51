/**
 * AnimatedDonutDemo Component
 * Demonstrates the AnimatedDonutChart with sample data and usage examples
 */

import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Card, 
  CardContent, 
  Grid, 
  Button,
  Paper
} from '@mui/material';
import AnimatedDonutChart, { DonutChartData } from '../charts/AnimatedDonutChart';

// ============================================================================
// Sample Data Sets
// ============================================================================

const sampleDataSets = {
  revenue: [
    { label: 'Product Sales', value: 2500000 },
    { label: 'Services', value: 1800000 },
    { label: 'Subscriptions', value: 1200000 },
    { label: 'Licensing', value: 800000 },
    { label: 'Other', value: 300000 }
  ],
  customers: [
    { label: 'Enterprise', value: 150 },
    { label: 'SMB', value: 320 },
    { label: 'Startup', value: 180 },
    { label: 'Government', value: 45 }
  ],
  regions: [
    { label: 'North America', value: 45 },
    { label: 'Europe', value: 30 },
    { label: 'Asia Pacific', value: 20 },
    { label: 'Latin America', value: 5 }
  ]
};

// ============================================================================
// AnimatedDonutDemo Component
// ============================================================================

interface AnimatedDonutDemoProps {
  onBack?: () => void;
}

const AnimatedDonutDemo: React.FC<AnimatedDonutDemoProps> = ({ onBack }) => {
  const [selectedDataSet, setSelectedDataSet] = useState<keyof typeof sampleDataSets>('revenue');
  const [hoveredSegment, setHoveredSegment] = useState<DonutChartData | null>(null);

  const currentData = sampleDataSets[selectedDataSet];

  const handleSegmentHover = (data: DonutChartData | null) => {
    setHoveredSegment(data);
  };

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        {onBack && (
          <Button
            variant="outlined"
            onClick={onBack}
            sx={{ mr: 2 }}
          >
            ← Back to Dashboard
          </Button>
        )}
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
          Animated Donut Chart Demo
        </Typography>
      </Box>

      {/* Data Set Selector */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Typography variant="h6" gutterBottom>
          Select Data Set:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          {Object.keys(sampleDataSets).map((key) => (
            <Button
              key={key}
              variant={selectedDataSet === key ? 'contained' : 'outlined'}
              onClick={() => setSelectedDataSet(key as keyof typeof sampleDataSets)}
              sx={{ textTransform: 'capitalize' }}
            >
              {key}
            </Button>
          ))}
        </Box>
      </Paper>

      <Grid container spacing={4}>
        {/* Chart Display */}
        <Grid item xs={12} md={8}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ p: 4 }}>
              <Typography variant="h6" gutterBottom>
                {selectedDataSet.charAt(0).toUpperCase() + selectedDataSet.slice(1)} Distribution
              </Typography>
              
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center',
                minHeight: 450
              }}>
                <AnimatedDonutChart
                  data={currentData}
                  width={400}
                  height={400}
                  innerRadius={80}
                  outerRadius={160}
                  animationDuration={1200}
                  onSegmentHover={handleSegmentHover}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Data Details */}
        <Grid item xs={12} md={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Data Details
              </Typography>

              {/* Hovered Segment Info */}
              {hoveredSegment && (
                <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.50', border: '1px solid', borderColor: 'primary.200' }}>
                  <Typography variant="subtitle2" color="primary" gutterBottom>
                    Currently Hovered:
                  </Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {hoveredSegment.label}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Value: {hoveredSegment.value.toLocaleString()}
                  </Typography>
                </Paper>
              )}

              {/* All Data */}
              <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
                All Segments:
              </Typography>
              
              <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
                {currentData.map((item, index) => {
                  const total = currentData.reduce((sum, d) => sum + d.value, 0);
                  const percentage = ((item.value / total) * 100).toFixed(1);
                  
                  return (
                    <Box
                      key={index}
                      sx={{
                        p: 2,
                        mb: 1,
                        border: '1px solid',
                        borderColor: 'grey.200',
                        borderRadius: 1,
                        bgcolor: hoveredSegment?.label === item.label ? 'action.hover' : 'transparent',
                        transition: 'background-color 0.2s'
                      }}
                    >
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {item.label}
                        </Typography>
                        <Typography variant="body2" color="primary" sx={{ fontWeight: 600 }}>
                          {percentage}%
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {item.value.toLocaleString()}
                      </Typography>
                    </Box>
                  );
                })}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Features List */}
      <Paper sx={{ p: 3, mt: 4 }}>
        <Typography variant="h6" gutterBottom>
          Chart Features:
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
              ✓ Sweep Animation
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Arcs animate from 0 to final angles
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
              ✓ Mouse-Following Tooltips
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Clean white tooltips with data
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
              ✓ Hover Effects
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Arcs scale up on hover
            </Typography>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Typography variant="body2" sx={{ fontWeight: 500, color: 'success.main' }}>
              ✓ Center Total
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Bold total value in center
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      {/* Usage Example */}
      <Paper sx={{ p: 3, mt: 4, bgcolor: 'grey.50' }}>
        <Typography variant="h6" gutterBottom>
          Usage Example:
        </Typography>
        <Box component="pre" sx={{ 
          bgcolor: 'grey.900', 
          color: 'grey.100', 
          p: 2, 
          borderRadius: 1, 
          overflow: 'auto',
          fontSize: '0.875rem',
          fontFamily: 'monospace'
        }}>
{`import AnimatedDonutChart from './components/charts/AnimatedDonutChart';

const data = [
  { label: 'Product Sales', value: 2500000 },
  { label: 'Services', value: 1800000 },
  { label: 'Subscriptions', value: 1200000 }
];

<AnimatedDonutChart
  data={data}
  width={400}
  height={400}
  innerRadius={80}
  outerRadius={160}
  animationDuration={1200}
  onSegmentHover={(segment) => console.log(segment)}
/>`}
        </Box>
      </Paper>
    </Box>
  );
};

export default AnimatedDonutDemo;
