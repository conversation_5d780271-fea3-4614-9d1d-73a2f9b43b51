/**
 * SimpleBarChart Component using D3.js
 * Clean, professional bar chart with hover interactions
 */

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Box } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface BarChartData {
  label: string;
  value: number;
  color?: string;
}

interface SimpleBarChartProps {
  data: BarChartData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  colors?: string[];
  xAxisLabel?: string;
  yAxisLabel?: string;
  onBarHover?: (data: BarChartData | null) => void;
}

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_COLORS = [
  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',
  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'
];

const DEFAULT_MARGIN = { top: 20, right: 30, bottom: 60, left: 60 };

// ============================================================================
// SimpleBarChart Component
// ============================================================================

const SimpleBarChart: React.FC<SimpleBarChartProps> = ({
  data,
  width = 400,
  height = 300,
  margin = DEFAULT_MARGIN,
  colors = DEFAULT_COLORS,
  xAxisLabel,
  yAxisLabel,
  onBarHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    content: string;
    x: number;
    y: number;
  }>({
    visible: false,
    content: '',
    x: 0,
    y: 0,
  });

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    // Create scales
    const xScale = d3
      .scaleBand()
      .domain(data.map(d => d.label))
      .range([0, innerWidth])
      .padding(0.2);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(data, d => d.value) || 0])
      .nice()
      .range([innerHeight, 0]);

    // Create axes
    const xAxis = d3.axisBottom(xScale);
    const yAxis = d3.axisLeft(yScale).tickFormat(d3.format('.2s'));

    // Add X axis
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('text-anchor', 'end')
      .attr('dx', '-.8em')
      .attr('dy', '.15em')
      .attr('transform', 'rotate(-45)')
      .style('font-size', '12px');

    // Add Y axis
    g.append('g')
      .attr('class', 'y-axis')
      .call(yAxis)
      .selectAll('text')
      .style('font-size', '12px');

    // Add X axis label
    if (xAxisLabel) {
      g.append('text')
        .attr('class', 'x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + margin.bottom - 5)
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('fill', '#666')
        .text(xAxisLabel);
    }

    // Add Y axis label
    if (yAxisLabel) {
      g.append('text')
        .attr('class', 'y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -margin.left + 15)
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('fill', '#666')
        .text(yAxisLabel);
    }

    // Calculate total for percentages
    const total = data.reduce((sum, d) => sum + d.value, 0);

    // Create bars
    const bars = g
      .selectAll('.bar')
      .data(data)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.label) || 0)
      .attr('width', xScale.bandwidth())
      .attr('y', innerHeight)
      .attr('height', 0)
      .attr('fill', (d, i) => d.color || colors[i % colors.length])
      .style('cursor', 'pointer')
      .on('mouseenter', function(event, d) {
        // Hover effect
        d3.select(this).style('opacity', 0.8);

        // Show tooltip
        const percentage = ((d.value / total) * 100).toFixed(1);
        const content = `${d.label}: ${d.value.toLocaleString()} (${percentage}% of total)`;
        
        setTooltip({
          visible: true,
          content,
          x: event.pageX,
          y: event.pageY,
        });

        onBarHover?.(d);
      })
      .on('mouseleave', function(event, d) {
        // Remove hover effect
        d3.select(this).style('opacity', 1);

        // Hide tooltip
        setTooltip(prev => ({ ...prev, visible: false }));
        onBarHover?.(null);
      })
      .on('mousemove', function(event) {
        setTooltip(prev => ({
          ...prev,
          x: event.pageX,
          y: event.pageY,
        }));
      });

    // Animate bars
    bars
      .transition()
      .duration(800)
      .delay((d, i) => i * 100)
      .attr('y', d => yScale(d.value))
      .attr('height', d => innerHeight - yScale(d.value));

  }, [data, width, height, margin, colors, xAxisLabel, yAxisLabel, onBarHover]);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
      
      {tooltip.visible && (
        <Box
          sx={{
            position: 'fixed',
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '14px',
            pointerEvents: 'none',
            zIndex: 1000,
            transform: 'translate(-50%, -100%)',
          }}
        >
          {tooltip.content}
        </Box>
      )}
    </Box>
  );
};

export default SimpleBarChart;
