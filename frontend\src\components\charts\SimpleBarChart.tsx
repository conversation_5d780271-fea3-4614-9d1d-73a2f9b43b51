/**
 * SimpleBarChart Component using D3.js
 * Clean, professional bar chart with hover interactions
 */

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { Box } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface BarChartData {
  label: string;
  value: number;
  color?: string;
  originalData?: any; // Preserve original data for detailed tooltips
}

interface SimpleBarChartProps {
  data: BarChartData[];
  width?: number;
  height?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  colors?: string[];
  xAxisLabel?: string;
  yAxisLabel?: string;
  onBarHover?: (data: BarChartData | null) => void;
}

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_COLORS = [
  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',
  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'
];

const DEFAULT_MARGIN = { top: 20, right: 30, bottom: 60, left: 60 };

// ============================================================================
// SimpleBarChart Component
// ============================================================================

const SimpleBarChart: React.FC<SimpleBarChartProps> = ({
  data,
  width = 400,
  height = 300,
  margin = DEFAULT_MARGIN,
  colors = DEFAULT_COLORS,
  xAxisLabel,
  yAxisLabel,
  onBarHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  // Note: Using D3.js native tooltips instead of React state for better performance

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Validate and clean data
    const validData = data.filter(d => d && typeof d.value === 'number' && d.value >= 0 && d.label);
    if (validData.length === 0) return;

    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;

    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left}, ${margin.top})`);

    // Create scales
    const xScale = d3
      .scaleBand()
      .domain(validData.map(d => d.label))
      .range([0, innerWidth])
      .padding(0.2);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(validData, d => d.value) || 0])
      .nice()
      .range([innerHeight, 0]);

    // Create axes
    const xAxis = d3.axisBottom(xScale);
    const yAxis = d3.axisLeft(yScale).tickFormat(d3.format('.2s'));

    // Add X axis
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0, ${innerHeight})`)
      .call(xAxis)
      .selectAll('text')
      .style('text-anchor', 'end')
      .attr('dx', '-.8em')
      .attr('dy', '.15em')
      .attr('transform', 'rotate(-45)')
      .style('font-size', '12px');

    // Add Y axis
    g.append('g')
      .attr('class', 'y-axis')
      .call(yAxis)
      .selectAll('text')
      .style('font-size', '12px');

    // Add X axis label
    if (xAxisLabel) {
      g.append('text')
        .attr('class', 'x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', innerWidth / 2)
        .attr('y', innerHeight + margin.bottom - 5)
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('fill', '#666')
        .text(xAxisLabel);
    }

    // Add Y axis label
    if (yAxisLabel) {
      g.append('text')
        .attr('class', 'y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -innerHeight / 2)
        .attr('y', -margin.left + 15)
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('fill', '#666')
        .text(yAxisLabel);
    }

    // Calculate total for percentages
    const total = validData.reduce((sum, d) => sum + d.value, 0);

    // Create bars
    const bars = g
      .selectAll('.bar')
      .data(validData)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.label) || 0)
      .attr('width', xScale.bandwidth())
      .attr('y', innerHeight)
      .attr('height', 0)
      .attr('fill', (d, i) => d.color || colors[i % colors.length])
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        // Get mouse position using d3.pointer for accurate positioning
        const [mouseX, mouseY] = d3.pointer(event, document.body);

        // Hover effect - brighten and slightly scale
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 0.8)
          .style('filter', 'brightness(1.1)')
          .attr('stroke', '#1976d2')
          .attr('stroke-width', 2);

        // Calculate percentage of total
        const percentage = ((d.value / total) * 100).toFixed(1);

        // Create tooltip content based on data type
        let label = d.label;
        let absoluteValue = d.value.toLocaleString();

        // Enhanced content for specific data types
        if (d.originalData) {
          const original = d.originalData;

          if (original.industry) {
            // Account Industries
            label = original.industry;
            absoluteValue = `$${original.revenue.toLocaleString()}`;
          } else if (original.name && original.memberCount !== undefined) {
            // Teams
            label = original.name;
            absoluteValue = `${original.memberCount} members`;
          }
        }

        // Create clean tooltip with white background and shadow
        const tooltip = d3.select('body')
          .selectAll('.d3-tooltip')
          .data([0]);

        const tooltipEnter = tooltip.enter()
          .append('div')
          .attr('class', 'd3-tooltip')
          .style('position', 'absolute')
          .style('background', 'white')
          .style('border', '1px solid #ddd')
          .style('border-radius', '8px')
          .style('padding', '12px')
          .style('font-size', '13px')
          .style('font-family', 'system-ui, -apple-system, sans-serif')
          .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.15)')
          .style('pointer-events', 'none')
          .style('z-index', '1000')
          .style('opacity', 0);

        const tooltipUpdate = tooltipEnter.merge(tooltip);

        // Update tooltip content
        tooltipUpdate.html(`
          <div style="font-weight: 600; margin-bottom: 6px; color: #333;">${label}</div>
          <div style="margin-bottom: 4px; color: #666;">Value: <strong style="color: #1976d2;">${absoluteValue}</strong></div>
          <div style="color: #666;">Percentage: <strong style="color: #1976d2;">${percentage}%</strong></div>
        `);

        // Position and show tooltip
        tooltipUpdate
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px')
          .transition()
          .duration(200)
          .style('opacity', 1);

        onBarHover?.(d);
      })
      .on('mousemove', function(event) {
        // Update tooltip position to follow mouse
        const [mouseX, mouseY] = d3.pointer(event, document.body);

        d3.select('.d3-tooltip')
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px');
      })
      .on('mouseout', function(event, d) {
        // Remove hover effect
        d3.select(this)
          .transition()
          .duration(200)
          .style('opacity', 1)
          .style('filter', 'brightness(1)')
          .attr('stroke', 'none')
          .attr('stroke-width', 0);

        // Hide and remove tooltip
        d3.select('.d3-tooltip')
          .transition()
          .duration(200)
          .style('opacity', 0)
          .remove();

        onBarHover?.(null);
      })
      // mousemove is already handled in the main mouseover event

    // Add value labels on top of bars
    const valueLabels = g
      .selectAll('.value-label')
      .data(validData)
      .enter()
      .append('text')
      .attr('class', 'value-label')
      .attr('x', d => (xScale(d.label) || 0) + xScale.bandwidth() / 2)
      .attr('y', innerHeight)
      .attr('text-anchor', 'middle')
      .style('font-size', '11px')
      .style('font-weight', '700')
      .style('fill', '#1976d2')
      .style('opacity', 0)
      .text(d => {
        // Only show values for specific chart types where it makes sense
        if (d.originalData) {
          const original = d.originalData;

          if (original.revenue !== undefined) {
            // Account Industries - don't show revenue values above bars (too cluttered)
            return '';
          } else if (original.memberCount !== undefined) {
            // Teams - show member count (clean numbers)
            return original.memberCount.toString();
          } else if (original.count !== undefined) {
            // Other data types - show count
            return original.count.toLocaleString();
          }
        }

        // Fallback - don't show generic values for cleaner appearance
        return '';
      });

    // Animate bars
    bars
      .transition()
      .duration(800)
      .delay((d, i) => i * 100)
      .attr('y', d => yScale(d.value))
      .attr('height', d => innerHeight - yScale(d.value))
      .ease(d3.easeBackOut);

    // Animate value labels
    valueLabels
      .transition()
      .duration(600)
      .delay((d, i) => 400 + i * 100)
      .attr('y', d => yScale(d.value) - 12) // More space above bars
      .style('opacity', 1);

  }, [data, width, height, margin, colors, xAxisLabel, yAxisLabel, onBarHover]);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
      
      {/* Tooltips are now handled by D3.js directly */}
    </Box>
  );
};

export default SimpleBarChart;
