{"ast": null, "code": "import Quad from \"./quad.js\";\nexport default function (callback) {\n  var quads = [],\n    next = [],\n    q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child,\n        x0 = q.x0,\n        y0 = q.y0,\n        x1 = q.x1,\n        y1 = q.y1,\n        xm = (x0 + x1) / 2,\n        ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Quad", "callback", "quads", "next", "q", "_root", "push", "_x0", "_y0", "_x1", "_y1", "pop", "node", "length", "child", "x0", "y0", "x1", "y1", "xm", "ym"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-quadtree/src/visitAfter.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], next = [], q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,KAAK,GAAG,EAAE;IAAEC,IAAI,GAAG,EAAE;IAAEC,CAAC;EAC5B,IAAI,IAAI,CAACC,KAAK,EAAEH,KAAK,CAACI,IAAI,CAAC,IAAIN,IAAI,CAAC,IAAI,CAACK,KAAK,EAAE,IAAI,CAACE,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EACxF,OAAON,CAAC,GAAGF,KAAK,CAACS,GAAG,CAAC,CAAC,EAAE;IACtB,IAAIC,IAAI,GAAGR,CAAC,CAACQ,IAAI;IACjB,IAAIA,IAAI,CAACC,MAAM,EAAE;MACf,IAAIC,KAAK;QAAEC,EAAE,GAAGX,CAAC,CAACW,EAAE;QAAEC,EAAE,GAAGZ,CAAC,CAACY,EAAE;QAAEC,EAAE,GAAGb,CAAC,CAACa,EAAE;QAAEC,EAAE,GAAGd,CAAC,CAACc,EAAE;QAAEC,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC;QAAEG,EAAE,GAAG,CAACJ,EAAE,GAAGE,EAAE,IAAI,CAAC;MAC7F,IAAIJ,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACc,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEG,EAAE,EAAEC,EAAE,CAAC,CAAC;MAChE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACc,KAAK,EAAEK,EAAE,EAAEH,EAAE,EAAEC,EAAE,EAAEG,EAAE,CAAC,CAAC;MAChE,IAAIN,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACc,KAAK,EAAEC,EAAE,EAAEK,EAAE,EAAED,EAAE,EAAED,EAAE,CAAC,CAAC;MAChE,IAAIJ,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACI,IAAI,CAAC,IAAIN,IAAI,CAACc,KAAK,EAAEK,EAAE,EAAEC,EAAE,EAAEH,EAAE,EAAEC,EAAE,CAAC,CAAC;IAClE;IACAf,IAAI,CAACG,IAAI,CAACF,CAAC,CAAC;EACd;EACA,OAAOA,CAAC,GAAGD,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAE;IACrBV,QAAQ,CAACG,CAAC,CAACQ,IAAI,EAAER,CAAC,CAACW,EAAE,EAAEX,CAAC,CAACY,EAAE,EAAEZ,CAAC,CAACa,EAAE,EAAEb,CAAC,CAACc,EAAE,CAAC;EAC1C;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}