/**
 * DoughnutChart Component for SkyGeni Dashboard
 * 
 * A responsive D3.js-powered doughnut chart component with:
 * - Interactive hover effects
 * - Smooth animations
 * - Center text display
 * - Legend support
 * - Tooltip support
 * - Responsive design
 * - TypeScript support
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { Box, Typography, useTheme } from '@mui/material';
import {
  DoughnutChartProps,
  DoughnutChartConfig,
  DoughnutChartDataPoint,
  DoughnutChartScales,
  ChartDimensions,
  TooltipData,
  D3Selection,
  D3GSelection
} from './types';

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_CONFIG: <PERSON>hnutChartConfig = {
  width: 400,
  height: 400,
  margin: { top: 20, right: 20, bottom: 20, left: 20 },
  innerRadius: 60,
  outerRadius: 150,
  padAngle: 0.02,
  cornerRadius: 3,
  showLabels: true,
  showLegend: false,
  showTooltip: true,
  centerText: '',
};

// ============================================================================
// DoughnutChart Component
// ============================================================================

const DoughnutChart: React.FC<DoughnutChartProps> = ({
  data,
  config = {},
  animation = { duration: 750, delay: 0 },
  colorScheme,
  onSegmentClick,
  onSegmentHover,
  className,
  title,
}) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltip, setTooltip] = useState<{
    data: TooltipData | null;
    position: { x: number; y: number };
    visible: boolean;
  }>({
    data: null,
    position: { x: 0, y: 0 },
    visible: false,
  });

  // Merge default config with provided config
  const chartConfig: DoughnutChartConfig = { ...DEFAULT_CONFIG, ...config };

  // Calculate chart dimensions
  const dimensions: ChartDimensions = {
    width: chartConfig.width,
    height: chartConfig.height,
    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,
    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom,
  };

  // Calculate center position
  const centerX = dimensions.innerWidth / 2;
  const centerY = dimensions.innerHeight / 2;
  const radius = Math.min(centerX, centerY);

  // Adjust radii based on available space
  const innerRadius = Math.min(chartConfig.innerRadius, radius * 0.4);
  const outerRadius = Math.min(chartConfig.outerRadius, radius * 0.8);

  // Default color scheme
  const colors = colorScheme || {
    primary: [
      theme.palette.primary.main,
      theme.palette.secondary.main,
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
      '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ],
    secondary: [],
    background: theme.palette.background.paper,
    text: theme.palette.text.primary,
    grid: theme.palette.divider,
    axis: theme.palette.text.secondary,
  };

  // ========================================================================
  // Data Processing
  // ========================================================================

  const processedData: DoughnutChartDataPoint[] = React.useMemo(() => {
    const pie = d3.pie<any, any>()
      .value(d => d.value)
      .sort(null)
      .padAngle(chartConfig.padAngle);

    const arcs = pie(data.segments);

    return arcs.map((arc, index) => ({
      ...arc.data,
      startAngle: arc.startAngle,
      endAngle: arc.endAngle,
      index,
      data: arc.data,
      color: arc.data.color || colors.primary[index % colors.primary.length],
    }));
  }, [data.segments, chartConfig.padAngle, colors.primary]);

  // ========================================================================
  // D3 Scales and Generators
  // ========================================================================

  const scales: DoughnutChartScales = React.useMemo(() => {
    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(processedData.map(d => d.label))
      .range(colors.primary);

    const arcGenerator = d3
      .arc<DoughnutChartDataPoint>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius)
      .cornerRadius(chartConfig.cornerRadius);

    const pieGenerator = d3.pie<any, any>()
      .value(d => d.value)
      .sort(null)
      .padAngle(chartConfig.padAngle);

    return { 
      color: colorScale, 
      arc: arcGenerator, 
      pie: pieGenerator 
    };
  }, [processedData, colors.primary, innerRadius, outerRadius, chartConfig.cornerRadius, chartConfig.padAngle]);

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleSegmentMouseEnter = useCallback((event: MouseEvent, d: DoughnutChartDataPoint) => {
    // Prevent event bubbling
    event.stopPropagation();

    const rect = svgRef.current?.getBoundingClientRect();
    if (!rect) return;

    const tooltipData: TooltipData = {
      title: d.label,
      value: d.value,
      percentage: d.percentage,
      color: d.color,
    };

    setTooltip({
      data: tooltipData,
      position: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      },
      visible: true,
    });

    onSegmentHover?.(d, event);
  }, [onSegmentHover]);

  const handleSegmentMouseLeave = useCallback((event: MouseEvent) => {
    // Prevent event bubbling
    event.stopPropagation();

    // Add small delay to prevent flicker
    setTimeout(() => {
      setTooltip(prev => ({ ...prev, visible: false }));
    }, 50);

    onSegmentHover?.(null, event);
  }, [onSegmentHover]);

  const handleSegmentClick = useCallback((event: MouseEvent, d: DoughnutChartDataPoint) => {
    onSegmentClick?.(d, event);
  }, [onSegmentClick]);

  // ========================================================================
  // Chart Rendering
  // ========================================================================

  useEffect(() => {
    if (!svgRef.current || processedData.length === 0) return;

    const svg = d3.select(svgRef.current);

    // Clear previous content
    svg.selectAll('*').remove();

    // Create main group with margins and center translation
    const g = svg
      .append('g')
      .attr('transform', `translate(${chartConfig.margin.left + centerX},${chartConfig.margin.top + centerY})`);

    // Create segments
    const segments = g
      .selectAll('.segment')
      .data(processedData)
      .enter()
      .append('path')
      .attr('class', 'segment')
      .attr('fill', d => d.color)
      .attr('stroke', colors.background)
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseenter', function(event, d) {
        // Simple highlight effect
        d3.select(this)
          .attr('stroke-width', 3)
          .style('opacity', 0.8);

        handleSegmentMouseEnter(event, d);
      })
      .on('mouseleave', function(event, d) {
        // Reset highlight
        d3.select(this)
          .attr('stroke-width', 2)
          .style('opacity', 1);

        handleSegmentMouseLeave(event);
      })
      .on('click', handleSegmentClick);

    // Animate segments
    segments
      .attr('d', scales.arc)
      .style('opacity', 0)
      .transition()
      .duration(animation.duration)
      .delay((d, i) => (animation.delay || 0) + i * 100)
      .ease(d3.easeBackOut.overshoot(1.1))
      .style('opacity', 1)
      .attrTween('d', function(d) {
        const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);
        return function(t) {
          return scales.arc(interpolate(t)) || '';
        };
      });

    // Add labels if enabled
    if (chartConfig.showLabels) {
      const labelArc = d3
        .arc<DoughnutChartDataPoint>()
        .innerRadius(outerRadius + 10)
        .outerRadius(outerRadius + 10);

      const labels = g
        .selectAll('.label')
        .data(processedData)
        .enter()
        .append('text')
        .attr('class', 'label')
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('fill', colors.text)
        .style('font-size', '12px')
        .style('font-weight', 'bold')
        .style('opacity', 0)
        .text(d => `${d.label} (${d.percentage?.toFixed(1)}%)`);

      // Position and animate labels
      labels
        .transition()
        .duration(animation.duration)
        .delay((d, i) => (animation.delay || 0) + i * 100 + 300)
        .attr('transform', d => {
          const centroid = labelArc.centroid(d);
          return `translate(${centroid[0]}, ${centroid[1]})`;
        })
        .style('opacity', 1);
    }

    // Add center text if provided
    if (chartConfig.centerText || data.centerLabel) {
      const centerText = g
        .append('text')
        .attr('class', 'center-text')
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('fill', colors.text)
        .style('font-size', '14px')
        .style('font-weight', '600')
        .style('opacity', 0)
        .text(chartConfig.centerText || data.centerLabel || '');

      centerText
        .transition()
        .duration(animation.duration)
        .delay(animation.delay || 0)
        .style('opacity', 1);
    }

  }, [
    processedData,
    scales,
    dimensions,
    chartConfig,
    colors,
    animation,
    centerX,
    centerY,
    outerRadius,
    data.centerLabel,
    handleSegmentMouseEnter,
    handleSegmentMouseLeave,
    handleSegmentClick,
  ]);

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Box className={className} sx={{ position: 'relative', width: '100%' }}>
      {title && (
        <Typography variant="h6" component="h3" gutterBottom align="center">
          {title}
        </Typography>
      )}

      <Box sx={{ position: 'relative', display: 'inline-block' }}>
        <svg
          ref={svgRef}
          width={dimensions.width}
          height={dimensions.height}
          style={{
            background: colors.background,
            borderRadius: theme.shape.borderRadius,
            boxShadow: theme.shadows[1],
          }}
        />

        {/* Tooltip */}
        {tooltip.visible && tooltip.data && (
          <Box
            ref={tooltipRef}
            sx={{
              position: 'absolute',
              left: tooltip.position.x + 10,
              top: tooltip.position.y - 10,
              background: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              padding: 1,
              boxShadow: theme.shadows[3],
              pointerEvents: 'none',
              zIndex: 1000,
              fontSize: '0.875rem',
            }}
          >
            <Typography variant="body2" fontWeight="bold">
              {tooltip.data.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Value: {tooltip.data.value.toLocaleString()}
            </Typography>
            {tooltip.data.percentage !== undefined && (
              <Typography variant="body2" color="text.secondary">
                Percentage: {tooltip.data.percentage.toFixed(1)}%
              </Typography>
            )}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default DoughnutChart;
