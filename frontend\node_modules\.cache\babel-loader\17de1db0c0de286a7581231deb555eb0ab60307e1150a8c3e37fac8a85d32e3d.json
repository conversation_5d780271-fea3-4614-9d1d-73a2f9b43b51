{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\index.tsx\";\n/**\n * Entry Point for SkyGeni Dashboard React Application\n * \n * This file:\n * - Renders the root App component\n * - Sets up React 18 concurrent features\n * - Provides error handling for the root\n * - Configures performance monitoring\n */\n\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport './styles/globals.css';\n\n// ============================================================================\n// Performance Monitoring\n// ============================================================================\n\n/**\n * Report web vitals for performance monitoring\n * This can be connected to analytics services\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst reportWebVitals = onPerfEntry => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({\n      getCLS,\n      getFID,\n      getFCP,\n      getLCP,\n      getTTFB\n    }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\n// ============================================================================\n// Application Bootstrap\n// ============================================================================\n\n/**\n * Initialize and render the React application\n */\nconst initializeApp = () => {\n  const container = document.getElementById('root');\n  if (!container) {\n    throw new Error('Root container not found. Make sure you have a div with id=\"root\" in your HTML.');\n  }\n\n  // Create React 18 root\n  const root = createRoot(container);\n\n  // Render the application\n  root.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n    children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this));\n\n  // Log successful initialization in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🚀 SkyGeni Dashboard initialized successfully');\n    console.log('📊 Ready to display data visualizations');\n  }\n};\n\n// ============================================================================\n// Error Handling\n// ============================================================================\n\n/**\n * Global error handler for unhandled errors\n */\nwindow.addEventListener('error', event => {\n  console.error('Global Error:', event.error);\n});\n\n/**\n * Global handler for unhandled promise rejections\n */\nwindow.addEventListener('unhandledrejection', event => {\n  console.error('Unhandled Promise Rejection:', event.reason);\n});\n\n// ============================================================================\n// Application Startup\n// ============================================================================\n\ntry {\n  initializeApp();\n\n  // Report web vitals in production\n  if (process.env.NODE_ENV === 'production') {\n    reportWebVitals(console.log);\n  }\n} catch (error) {\n  console.error('Failed to initialize application:', error);\n\n  // Show a basic error message if the app fails to start\n  const container = document.getElementById('root');\n  if (container) {\n    container.innerHTML = `\n      <div style=\"\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        height: 100vh;\n        font-family: Arial, sans-serif;\n        text-align: center;\n        padding: 2rem;\n      \">\n        <h1 style=\"color: #d32f2f; margin-bottom: 1rem;\">\n          Application Failed to Start\n        </h1>\n        <p style=\"color: #666; margin-bottom: 2rem;\">\n          There was an error initializing the SkyGeni Dashboard.\n        </p>\n        <button \n          onclick=\"window.location.reload()\" \n          style=\"\n            background-color: #1976d2;\n            color: white;\n            border: none;\n            padding: 0.75rem 1.5rem;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 1rem;\n          \"\n        >\n          Reload Page\n        </button>\n      </div>\n    `;\n  }\n}", "map": {"version": 3, "names": ["React", "createRoot", "jsxDEV", "_jsxDEV", "reportWebVitals", "onPerfEntry", "Function", "then", "getCLS", "getFID", "getFCP", "getLCP", "getTTFB", "initializeApp", "container", "document", "getElementById", "Error", "root", "render", "StrictMode", "children", "App", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "process", "env", "NODE_ENV", "console", "log", "window", "addEventListener", "event", "error", "reason", "innerHTML"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/index.tsx"], "sourcesContent": ["/**\n * Entry Point for SkyGeni Dashboard React Application\n * \n * This file:\n * - Renders the root App component\n * - Sets up React 18 concurrent features\n * - Provides error handling for the root\n * - Configures performance monitoring\n */\n\nimport React from 'react';\nimport { createRoot } from 'react-dom/client';\nimport CleanApp from './CleanApp';\nimport './styles/globals.css';\n\n// ============================================================================\n// Performance Monitoring\n// ============================================================================\n\n/**\n * Report web vitals for performance monitoring\n * This can be connected to analytics services\n */\nconst reportWebVitals = (onPerfEntry?: (metric: any) => void) => {\n  if (onPerfEntry && onPerfEntry instanceof Function) {\n    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {\n      getCLS(onPerfEntry);\n      getFID(onPerfEntry);\n      getFCP(onPerfEntry);\n      getLCP(onPerfEntry);\n      getTTFB(onPerfEntry);\n    });\n  }\n};\n\n// ============================================================================\n// Application Bootstrap\n// ============================================================================\n\n/**\n * Initialize and render the React application\n */\nconst initializeApp = () => {\n  const container = document.getElementById('root');\n  \n  if (!container) {\n    throw new Error('Root container not found. Make sure you have a div with id=\"root\" in your HTML.');\n  }\n\n  // Create React 18 root\n  const root = createRoot(container);\n\n  // Render the application\n  root.render(\n    <React.StrictMode>\n      <App />\n    </React.StrictMode>\n  );\n\n  // Log successful initialization in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('🚀 SkyGeni Dashboard initialized successfully');\n    console.log('📊 Ready to display data visualizations');\n  }\n};\n\n// ============================================================================\n// Error Handling\n// ============================================================================\n\n/**\n * Global error handler for unhandled errors\n */\nwindow.addEventListener('error', (event) => {\n  console.error('Global Error:', event.error);\n});\n\n/**\n * Global handler for unhandled promise rejections\n */\nwindow.addEventListener('unhandledrejection', (event) => {\n  console.error('Unhandled Promise Rejection:', event.reason);\n});\n\n// ============================================================================\n// Application Startup\n// ============================================================================\n\ntry {\n  initializeApp();\n  \n  // Report web vitals in production\n  if (process.env.NODE_ENV === 'production') {\n    reportWebVitals(console.log);\n  }\n} catch (error) {\n  console.error('Failed to initialize application:', error);\n  \n  // Show a basic error message if the app fails to start\n  const container = document.getElementById('root');\n  if (container) {\n    container.innerHTML = `\n      <div style=\"\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        height: 100vh;\n        font-family: Arial, sans-serif;\n        text-align: center;\n        padding: 2rem;\n      \">\n        <h1 style=\"color: #d32f2f; margin-bottom: 1rem;\">\n          Application Failed to Start\n        </h1>\n        <p style=\"color: #666; margin-bottom: 2rem;\">\n          There was an error initializing the SkyGeni Dashboard.\n        </p>\n        <button \n          onclick=\"window.location.reload()\" \n          style=\"\n            background-color: #1976d2;\n            color: white;\n            border: none;\n            padding: 0.75rem 1.5rem;\n            border-radius: 4px;\n            cursor: pointer;\n            font-size: 1rem;\n          \"\n        >\n          Reload Page\n        </button>\n      </div>\n    `;\n  }\n}\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,kBAAkB;AAE7C,OAAO,sBAAsB;;AAE7B;AACA;AACA;;AAEA;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA;AAIA,MAAMC,eAAe,GAAIC,WAAmC,IAAK;EAC/D,IAAIA,WAAW,IAAIA,WAAW,YAAYC,QAAQ,EAAE;IAClD,MAAM,CAAC,YAAY,CAAC,CAACC,IAAI,CAAC,CAAC;MAAEC,MAAM;MAAEC,MAAM;MAAEC,MAAM;MAAEC,MAAM;MAAEC;IAAQ,CAAC,KAAK;MACzEJ,MAAM,CAACH,WAAW,CAAC;MACnBI,MAAM,CAACJ,WAAW,CAAC;MACnBK,MAAM,CAACL,WAAW,CAAC;MACnBM,MAAM,CAACN,WAAW,CAAC;MACnBO,OAAO,CAACP,WAAW,CAAC;IACtB,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAMQ,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAMC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;EAEjD,IAAI,CAACF,SAAS,EAAE;IACd,MAAM,IAAIG,KAAK,CAAC,iFAAiF,CAAC;EACpG;;EAEA;EACA,MAAMC,IAAI,GAAGjB,UAAU,CAACa,SAAS,CAAC;;EAElC;EACAI,IAAI,CAACC,MAAM,cACThB,OAAA,CAACH,KAAK,CAACoB,UAAU;IAAAC,QAAA,eACflB,OAAA,CAACmB,GAAG;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACS,CACpB,CAAC;;EAED;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1CC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAC5DD,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;EACxD;AACF,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACAC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1CJ,OAAO,CAACK,KAAK,CAAC,eAAe,EAAED,KAAK,CAACC,KAAK,CAAC;AAC7C,CAAC,CAAC;;AAEF;AACA;AACA;AACAH,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAGC,KAAK,IAAK;EACvDJ,OAAO,CAACK,KAAK,CAAC,8BAA8B,EAAED,KAAK,CAACE,MAAM,CAAC;AAC7D,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,IAAI;EACFvB,aAAa,CAAC,CAAC;;EAEf;EACA,IAAIc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCzB,eAAe,CAAC0B,OAAO,CAACC,GAAG,CAAC;EAC9B;AACF,CAAC,CAAC,OAAOI,KAAK,EAAE;EACdL,OAAO,CAACK,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;;EAEzD;EACA,MAAMrB,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC;EACjD,IAAIF,SAAS,EAAE;IACbA,SAAS,CAACuB,SAAS,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}