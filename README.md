# SkyGeni Dashboard

A clean analytics dashboard that turns your JSON data into interactive charts. Built for teams who need quick insights without the complexity.

## What it does

- Visualizes customer data, team performance, and revenue metrics
- Interactive charts that respond to mouse hovers
- Works on desktop and mobile
- Loads your own JSON data files

## Quick Start

**You'll need Node.js 18+ installed.**

1. **Get the code**
   ```bash
   git clone <your-repo-url>
   cd project-skygeni
   ```

2. **Install everything**
   ```bash
   cd backend && npm install
   cd ../frontend && npm install
   ```

3. **Add your data**

   Drop your JSON files in `backend/src/data/`:
   - `CustomerType.json`
   - `AccountIndustry.json`
   - `Team.json`
   - `ACVRange.json`

4. **Start it up**

   Backend (terminal 1):
   ```bash
   cd backend && npm run dev
   ```

   Frontend (terminal 2):
   ```bash
   cd frontend && npm start
   ```

Open http://localhost:3000 and you're done.

## Your Data Format

Each JSON file should be an array of objects. Here's what the dashboard expects:

**CustomerType.json** - Customer segments
```json
[{"type": "Enterprise", "count": 150, "percentage": 45.5}]
```

**AccountIndustry.json** - Industry breakdown
```json
[{"industry": "Technology", "count": 200, "revenue": 5000000}]
```

**Team.json** - Team performance
```json
[{"name": "Sales Alpha", "memberCount": 12, "performance": 85, "department": "Sales"}]
```

**ACVRange.json** - Revenue ranges
```json
[{"range": "$0-$10K", "count": 50, "totalValue": 250000}]
```

## Troubleshooting

**Dashboard shows "Failed to refresh data"?**
- Make sure backend is running on port 5000
- Check that your JSON files are in `backend/src/data/`
- Try restarting both servers

**Port already in use?**
- Backend: Change PORT in `backend/.env`
- Frontend: It'll ask you to use a different port

**Charts not showing?**
- Verify your JSON files have the right structure
- Check browser console for errors

## Tech Stack

- **Frontend**: React + TypeScript + Material-UI + D3.js
- **Backend**: Node.js + Express + TypeScript
- **Data**: JSON files (bring your own)

## What's Inside

```
project-skygeni/
├── backend/          # API server
│   └── src/data/     # Put your JSON files here
└── frontend/         # React dashboard
```

That's it. Simple structure, no overcomplicated setup.

---

*Built for teams who want insights, not headaches.*
