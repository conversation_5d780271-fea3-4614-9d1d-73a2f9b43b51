{"ast": null, "code": "export default function (compare) {\n  return this.eachBefore(function (node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}", "map": {"version": 3, "names": ["compare", "eachBefore", "node", "children", "sort"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-hierarchy/src/hierarchy/sort.js"], "sourcesContent": ["export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n"], "mappings": "AAAA,eAAe,UAASA,OAAO,EAAE;EAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,UAASC,IAAI,EAAE;IACpC,IAAIA,IAAI,CAACC,QAAQ,EAAE;MACjBD,IAAI,CAACC,QAAQ,CAACC,IAAI,CAACJ,OAAO,CAAC;IAC7B;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}