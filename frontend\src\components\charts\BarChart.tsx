/**
 * BarChart Component for SkyGeni Dashboard
 * 
 * A responsive D3.js-powered bar chart component with:
 * - Interactive hover effects
 * - Smooth animations
 * - Customizable styling
 * - Tooltip support
 * - Responsive design
 * - TypeScript support
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import * as d3 from 'd3';
import { Box, Typography, useTheme } from '@mui/material';
import {
  BarChartProps,
  BarChartConfig,
  BarChartDataPoint,
  BarChartScales,
  ChartDimensions,
  TooltipData,
  D3Selection,
  D3GSelection
} from './types';

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_CONFIG: BarChartConfig = {
  width: 600,
  height: 400,
  margin: { top: 20, right: 30, bottom: 60, left: 60 },
  showGrid: true,
  showLabels: true,
  showTooltip: true,
  barPadding: 0.1,
  cornerRadius: 4,
  xAxisLabel: '',
  yAxisLabel: '',
};

// ============================================================================
// BarChart Component
// ============================================================================

const BarChart: React.FC<BarChartProps> = ({
  data,
  config = {},
  animation = { duration: 750, delay: 0 },
  colorScheme,
  onBarClick,
  onBarHover,
  className,
  title,
}) => {
  const theme = useTheme();
  const svgRef = useRef<SVGSVGElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const [tooltip, setTooltip] = useState<{
    data: TooltipData | null;
    position: { x: number; y: number };
    visible: boolean;
  }>({
    data: null,
    position: { x: 0, y: 0 },
    visible: false,
  });

  // Merge default config with provided config
  const chartConfig: BarChartConfig = { ...DEFAULT_CONFIG, ...config };

  // Calculate chart dimensions
  const dimensions: ChartDimensions = {
    width: chartConfig.width,
    height: chartConfig.height,
    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,
    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom,
  };

  // Default color scheme
  const colors = colorScheme || {
    primary: [
      theme.palette.primary.main,
      theme.palette.secondary.main,
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',
      '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ],
    secondary: [],
    background: theme.palette.background.paper,
    text: theme.palette.text.primary,
    grid: theme.palette.divider,
    axis: theme.palette.text.secondary,
  };

  // ========================================================================
  // Data Processing
  // ========================================================================

  const processedData: BarChartDataPoint[] = React.useMemo(() => {
    return data.categories.map((category, index) => ({
      label: category,
      value: data.values[index] || 0,
      color: data.colors?.[index] || colors.primary[index % colors.primary.length],
      x: category,
      y: data.values[index] || 0,
      index,
    }));
  }, [data, colors.primary]);

  // ========================================================================
  // D3 Scales
  // ========================================================================

  const scales: BarChartScales = React.useMemo(() => {
    const xScale = d3
      .scaleBand()
      .domain(processedData.map(d => d.x))
      .range([0, dimensions.innerWidth])
      .padding(chartConfig.barPadding);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(processedData, d => d.y) || 0])
      .nice()
      .range([dimensions.innerHeight, 0]);

    const colorScale = d3
      .scaleOrdinal<string>()
      .domain(processedData.map(d => d.x))
      .range(colors.primary);

    return { x: xScale, y: yScale, color: colorScale };
  }, [processedData, dimensions, chartConfig.barPadding, colors.primary]);

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleBarMouseEnter = useCallback((event: MouseEvent, d: BarChartDataPoint) => {
    // Prevent event bubbling to avoid loops
    event.stopPropagation();

    const rect = svgRef.current?.getBoundingClientRect();
    if (!rect) return;

    // Debounce tooltip updates to prevent rapid state changes
    const tooltipData: TooltipData = {
      title: d.label,
      value: d.value,
      color: d.color,
    };

    setTooltip({
      data: tooltipData,
      position: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      },
      visible: true,
    });

    onBarHover?.(d, event);
  }, [onBarHover]);

  const handleBarMouseLeave = useCallback((event: MouseEvent) => {
    // Prevent event bubbling
    event.stopPropagation();

    // Add small delay to prevent flicker
    setTimeout(() => {
      setTooltip(prev => ({ ...prev, visible: false }));
    }, 50);

    onBarHover?.(null, event);
  }, [onBarHover]);

  const handleBarClick = useCallback((event: MouseEvent, d: BarChartDataPoint) => {
    onBarClick?.(d, event);
  }, [onBarClick]);

  // ========================================================================
  // Chart Rendering
  // ========================================================================

  useEffect(() => {
    if (!svgRef.current || processedData.length === 0) return;

    const svg = d3.select(svgRef.current);
    
    // Clear previous content
    svg.selectAll('*').remove();

    // Create main group with margins
    const g = svg
      .append('g')
      .attr('transform', `translate(${chartConfig.margin.left},${chartConfig.margin.top})`);

    // Add grid lines if enabled
    if (chartConfig.showGrid) {
      // Horizontal grid lines
      g.append('g')
        .attr('class', 'grid')
        .selectAll('line')
        .data(scales.y.ticks())
        .enter()
        .append('line')
        .attr('x1', 0)
        .attr('x2', dimensions.innerWidth)
        .attr('y1', d => scales.y(d))
        .attr('y2', d => scales.y(d))
        .attr('stroke', colors.grid)
        .attr('stroke-width', 1)
        .attr('opacity', 0.3);
    }

    // Add X axis
    g.append('g')
      .attr('class', 'x-axis')
      .attr('transform', `translate(0,${dimensions.innerHeight})`)
      .call(d3.axisBottom(scales.x))
      .selectAll('text')
      .style('fill', colors.axis)
      .style('font-size', '12px');

    // Add Y axis
    g.append('g')
      .attr('class', 'y-axis')
      .call(d3.axisLeft(scales.y))
      .selectAll('text')
      .style('fill', colors.axis)
      .style('font-size', '12px');

    // Add axis labels
    if (chartConfig.xAxisLabel) {
      g.append('text')
        .attr('class', 'x-axis-label')
        .attr('text-anchor', 'middle')
        .attr('x', dimensions.innerWidth / 2)
        .attr('y', dimensions.innerHeight + 40)
        .style('fill', colors.text)
        .style('font-size', '14px')
        .text(chartConfig.xAxisLabel);
    }

    if (chartConfig.yAxisLabel) {
      g.append('text')
        .attr('class', 'y-axis-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -dimensions.innerHeight / 2)
        .attr('y', -40)
        .style('fill', colors.text)
        .style('font-size', '14px')
        .text(chartConfig.yAxisLabel);
    }

    // Add bars
    const bars = g
      .selectAll('.bar')
      .data(processedData)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => scales.x(d.x) || 0)
      .attr('width', scales.x.bandwidth())
      .attr('y', dimensions.innerHeight) // Start from bottom for animation
      .attr('height', 0) // Start with 0 height for animation
      .attr('fill', d => d.color)
      .attr('rx', chartConfig.cornerRadius)
      .attr('ry', chartConfig.cornerRadius)
      .style('cursor', 'pointer')
      .on('mouseenter', function(event, d) {
        // Simple highlight effect
        d3.select(this)
          .attr('opacity', 0.8);

        handleBarMouseEnter(event, d);
      })
      .on('mouseleave', function(event, d) {
        // Reset highlight
        d3.select(this)
          .attr('opacity', 1);

        handleBarMouseLeave(event);
      })
      .on('click', handleBarClick);

    // Animate bars
    bars
      .transition()
      .duration(animation.duration)
      .delay((d, i) => (animation.delay || 0) + i * 50)
      .ease(d3.easeBackOut.overshoot(1.1))
      .attr('y', d => scales.y(d.y))
      .attr('height', d => dimensions.innerHeight - scales.y(d.y));

    // Add value labels if enabled
    if (chartConfig.showLabels) {
      const labels = g
        .selectAll('.bar-label')
        .data(processedData)
        .enter()
        .append('text')
        .attr('class', 'bar-label')
        .attr('text-anchor', 'middle')
        .attr('x', d => (scales.x(d.x) || 0) + scales.x.bandwidth() / 2)
        .attr('y', dimensions.innerHeight) // Start from bottom
        .style('fill', colors.text)
        .style('font-size', '12px')
        .style('font-weight', 'bold')
        .style('opacity', 0)
        .text(d => d.value.toLocaleString());

      // Animate labels
      labels
        .transition()
        .duration(animation.duration)
        .delay((d, i) => (animation.delay || 0) + i * 50 + 200)
        .attr('y', d => scales.y(d.y) - 5)
        .style('opacity', 1);
    }

  }, [
    processedData,
    scales,
    dimensions,
    chartConfig,
    colors,
    animation,
    handleBarMouseEnter,
    handleBarMouseLeave,
    handleBarClick,
  ]);

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Box className={className} sx={{ position: 'relative', width: '100%' }}>
      {title && (
        <Typography variant="h6" component="h3" gutterBottom align="center">
          {title}
        </Typography>
      )}
      
      <Box sx={{ position: 'relative', display: 'inline-block' }}>
        <svg
          ref={svgRef}
          width={dimensions.width}
          height={dimensions.height}
          style={{
            background: colors.background,
            borderRadius: theme.shape.borderRadius,
            boxShadow: theme.shadows[1],
          }}
        />
        
        {/* Tooltip */}
        {tooltip.visible && tooltip.data && (
          <Box
            ref={tooltipRef}
            sx={{
              position: 'absolute',
              left: tooltip.position.x + 10,
              top: tooltip.position.y - 10,
              background: theme.palette.background.paper,
              border: `1px solid ${theme.palette.divider}`,
              borderRadius: 1,
              padding: 1,
              boxShadow: theme.shadows[3],
              pointerEvents: 'none',
              zIndex: 1000,
              fontSize: '0.875rem',
            }}
          >
            <Typography variant="body2" fontWeight="bold">
              {tooltip.data.title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Value: {tooltip.data.value.toLocaleString()}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default BarChart;
