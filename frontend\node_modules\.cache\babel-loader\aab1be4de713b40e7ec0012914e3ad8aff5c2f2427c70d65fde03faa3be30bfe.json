{"ast": null, "code": "import { get, set, init } from \"./schedule.js\";\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function (t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\nfunction onFunction(id, name, listener) {\n  var on0,\n    on1,\n    sit = start(name) ? init : set;\n  return function () {\n    var schedule = sit(this, id),\n      on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n    schedule.on = on1;\n  };\n}\nexport default function (name, listener) {\n  var id = this._id;\n  return arguments.length < 2 ? get(this.node(), id).on.on(name) : this.each(onFunction(id, name, listener));\n}", "map": {"version": 3, "names": ["get", "set", "init", "start", "name", "trim", "split", "every", "t", "i", "indexOf", "slice", "onFunction", "id", "listener", "on0", "on1", "sit", "schedule", "on", "copy", "_id", "arguments", "length", "node", "each"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-transition/src/transition/on.js"], "sourcesContent": ["import {get, set, init} from \"./schedule.js\";\n\nfunction start(name) {\n  return (name + \"\").trim().split(/^|\\s+/).every(function(t) {\n    var i = t.indexOf(\".\");\n    if (i >= 0) t = t.slice(0, i);\n    return !t || t === \"start\";\n  });\n}\n\nfunction onFunction(id, name, listener) {\n  var on0, on1, sit = start(name) ? init : set;\n  return function() {\n    var schedule = sit(this, id),\n        on = schedule.on;\n\n    // If this node shared a dispatch with the previous node,\n    // just assign the updated shared dispatch and we’re done!\n    // Otherwise, copy-on-write.\n    if (on !== on0) (on1 = (on0 = on).copy()).on(name, listener);\n\n    schedule.on = on1;\n  };\n}\n\nexport default function(name, listener) {\n  var id = this._id;\n\n  return arguments.length < 2\n      ? get(this.node(), id).on.on(name)\n      : this.each(onFunction(id, name, listener));\n}\n"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,GAAG,EAAEC,IAAI,QAAO,eAAe;AAE5C,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO,CAACA,IAAI,GAAG,EAAE,EAAEC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,OAAO,CAAC,CAACC,KAAK,CAAC,UAASC,CAAC,EAAE;IACzD,IAAIC,CAAC,GAAGD,CAAC,CAACE,OAAO,CAAC,GAAG,CAAC;IACtB,IAAID,CAAC,IAAI,CAAC,EAAED,CAAC,GAAGA,CAAC,CAACG,KAAK,CAAC,CAAC,EAAEF,CAAC,CAAC;IAC7B,OAAO,CAACD,CAAC,IAAIA,CAAC,KAAK,OAAO;EAC5B,CAAC,CAAC;AACJ;AAEA,SAASI,UAAUA,CAACC,EAAE,EAAET,IAAI,EAAEU,QAAQ,EAAE;EACtC,IAAIC,GAAG;IAAEC,GAAG;IAAEC,GAAG,GAAGd,KAAK,CAACC,IAAI,CAAC,GAAGF,IAAI,GAAGD,GAAG;EAC5C,OAAO,YAAW;IAChB,IAAIiB,QAAQ,GAAGD,GAAG,CAAC,IAAI,EAAEJ,EAAE,CAAC;MACxBM,EAAE,GAAGD,QAAQ,CAACC,EAAE;;IAEpB;IACA;IACA;IACA,IAAIA,EAAE,KAAKJ,GAAG,EAAE,CAACC,GAAG,GAAG,CAACD,GAAG,GAAGI,EAAE,EAAEC,IAAI,CAAC,CAAC,EAAED,EAAE,CAACf,IAAI,EAAEU,QAAQ,CAAC;IAE5DI,QAAQ,CAACC,EAAE,GAAGH,GAAG;EACnB,CAAC;AACH;AAEA,eAAe,UAASZ,IAAI,EAAEU,QAAQ,EAAE;EACtC,IAAID,EAAE,GAAG,IAAI,CAACQ,GAAG;EAEjB,OAAOC,SAAS,CAACC,MAAM,GAAG,CAAC,GACrBvB,GAAG,CAAC,IAAI,CAACwB,IAAI,CAAC,CAAC,EAAEX,EAAE,CAAC,CAACM,EAAE,CAACA,EAAE,CAACf,IAAI,CAAC,GAChC,IAAI,CAACqB,IAAI,CAACb,UAAU,CAACC,EAAE,EAAET,IAAI,EAAEU,QAAQ,CAAC,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}