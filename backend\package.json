{"name": "skygeni-dashboard-backend", "version": "1.0.0", "description": "Backend API for SkyGeni Dashboard - serves JSON data for visualization", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["dashboard", "api", "typescript", "express", "data-visualization"], "author": "SkyGeni Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}