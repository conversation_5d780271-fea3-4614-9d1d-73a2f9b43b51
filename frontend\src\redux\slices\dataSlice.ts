/**
 * Data Slice for SkyGeni Dashboard
 * 
 * Manages all data-related state including:
 * - Dashboard data fetching and caching
 * - Individual data type management
 * - Loading states and error handling
 * - Data filtering and sorting
 * - Cache management
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '../../api';
import {
  DataState,
  DataType,
  DataFilters,
  SetDataFilterPayload,
  FetchDashboardDataPayload
} from './types';
import {
  DashboardData,
  CustomerType,
  AccountIndustry,
  Team,
  ACVRange,
  LoadingState
} from '../../types';

// ============================================================================
// Initial State
// ============================================================================

const initialState: DataState = {
  // Main data
  dashboardData: null,
  customerTypes: [],
  accountIndustries: [],
  teams: [],
  acvRanges: [],
  
  // Loading and error states
  loading: 'idle',
  error: null,
  
  // Metadata
  lastFetched: null,
  cacheExpiry: null,
  
  // UI state
  selectedDataType: null,
  filters: {
    searchTerm: '',
    sortBy: 'name',
    sortOrder: 'asc',
    showEmpty: true,
  },
};

// ============================================================================
// Async Thunks
// ============================================================================

/**
 * Fetch complete dashboard data
 */
export const fetchDashboardData = createAsyncThunk(
  'data/fetchDashboardData',
  async (payload: FetchDashboardDataPayload = {}, { rejectWithValue }) => {
    try {
      console.log('🔄 Fetching dashboard data...');
      const data = await apiService.getDashboardData();
      console.log('✅ Dashboard data fetched successfully');
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch dashboard data:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch dashboard data');
    }
  }
);

/**
 * Fetch customer types data
 */
export const fetchCustomerTypes = createAsyncThunk(
  'data/fetchCustomerTypes',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🔄 Fetching customer types...');
      const data = await apiService.getCustomerTypes();
      console.log('✅ Customer types fetched successfully');
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch customer types:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch customer types');
    }
  }
);

/**
 * Fetch account industries data
 */
export const fetchAccountIndustries = createAsyncThunk(
  'data/fetchAccountIndustries',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🔄 Fetching account industries...');
      const data = await apiService.getAccountIndustries();
      console.log('✅ Account industries fetched successfully');
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch account industries:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch account industries');
    }
  }
);

/**
 * Fetch teams data
 */
export const fetchTeams = createAsyncThunk(
  'data/fetchTeams',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🔄 Fetching teams...');
      const data = await apiService.getTeams();
      console.log('✅ Teams fetched successfully');
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch teams:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch teams');
    }
  }
);

/**
 * Fetch ACV ranges data
 */
export const fetchACVRanges = createAsyncThunk(
  'data/fetchACVRanges',
  async (_, { rejectWithValue }) => {
    try {
      console.log('🔄 Fetching ACV ranges...');
      const data = await apiService.getACVRanges();
      console.log('✅ ACV ranges fetched successfully');
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch ACV ranges:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch ACV ranges');
    }
  }
);

/**
 * Refresh all data
 */
export const refreshAllData = createAsyncThunk(
  'data/refreshAllData',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      console.log('🔄 Refreshing all data...');
      
      // Fetch dashboard data which includes everything
      const result = await dispatch(fetchDashboardData({ forceRefresh: true }));
      
      if (fetchDashboardData.fulfilled.match(result)) {
        console.log('✅ All data refreshed successfully');
        return result.payload;
      } else {
        throw new Error('Failed to refresh data');
      }
    } catch (error) {
      console.error('❌ Failed to refresh all data:', error);
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh all data');
    }
  }
);

// ============================================================================
// Data Slice
// ============================================================================

const dataSlice = createSlice({
  name: 'data',
  initialState,
  reducers: {
    /**
     * Clear all data and reset to initial state
     */
    clearData: (state) => {
      state.dashboardData = null;
      state.customerTypes = [];
      state.accountIndustries = [];
      state.teams = [];
      state.acvRanges = [];
      state.error = null;
      state.lastFetched = null;
      state.cacheExpiry = null;
      console.log('🗑️ Data cleared');
    },

    /**
     * Clear error state
     */
    clearError: (state) => {
      state.error = null;
    },

    /**
     * Set selected data type for UI
     */
    setSelectedDataType: (state, action: PayloadAction<DataType | null>) => {
      state.selectedDataType = action.payload;
    },

    /**
     * Update data filters
     */
    setDataFilter: (state, action: PayloadAction<SetDataFilterPayload>) => {
      const { filterType, value } = action.payload;
      state.filters[filterType] = value;
    },

    /**
     * Reset filters to default
     */
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },

    /**
     * Set loading state manually (for UI feedback)
     */
    setLoading: (state, action: PayloadAction<LoadingState>) => {
      state.loading = action.payload;
    },

    /**
     * Update cache expiry
     */
    updateCacheExpiry: (state, action: PayloadAction<string>) => {
      state.cacheExpiry = action.payload;
    },
  },
  extraReducers: (builder) => {
    // ========================================================================
    // Dashboard Data Handlers
    // ========================================================================
    builder
      .addCase(fetchDashboardData.pending, (state) => {
        state.loading = 'pending';
        state.error = null;
      })
      .addCase(fetchDashboardData.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.error = null;
        state.dashboardData = action.payload;
        
        // Update individual data arrays
        state.customerTypes = action.payload.customerTypes;
        state.accountIndustries = action.payload.accountIndustries;
        state.teams = action.payload.teams;
        state.acvRanges = action.payload.acvRanges;
        
        // Update metadata
        state.lastFetched = new Date().toISOString();
        state.cacheExpiry = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5 minutes
      })
      .addCase(fetchDashboardData.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.payload as string;
      });

    // ========================================================================
    // Individual Data Type Handlers
    // ========================================================================
    
    // Customer Types
    builder
      .addCase(fetchCustomerTypes.fulfilled, (state, action) => {
        state.customerTypes = action.payload;
        if (state.dashboardData) {
          state.dashboardData.customerTypes = action.payload;
        }
      })
      .addCase(fetchCustomerTypes.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Account Industries
    builder
      .addCase(fetchAccountIndustries.fulfilled, (state, action) => {
        state.accountIndustries = action.payload;
        if (state.dashboardData) {
          state.dashboardData.accountIndustries = action.payload;
        }
      })
      .addCase(fetchAccountIndustries.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Teams
    builder
      .addCase(fetchTeams.fulfilled, (state, action) => {
        state.teams = action.payload;
        if (state.dashboardData) {
          state.dashboardData.teams = action.payload;
        }
      })
      .addCase(fetchTeams.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // ACV Ranges
    builder
      .addCase(fetchACVRanges.fulfilled, (state, action) => {
        state.acvRanges = action.payload;
        if (state.dashboardData) {
          state.dashboardData.acvRanges = action.payload;
        }
      })
      .addCase(fetchACVRanges.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // ========================================================================
    // Refresh All Data Handlers
    // ========================================================================
    builder
      .addCase(refreshAllData.pending, (state) => {
        state.loading = 'pending';
        state.error = null;
      })
      .addCase(refreshAllData.fulfilled, (state, action) => {
        state.loading = 'succeeded';
        state.error = null;
        // Data is already updated by the fetchDashboardData call
      })
      .addCase(refreshAllData.rejected, (state, action) => {
        state.loading = 'failed';
        state.error = action.payload as string;
      });
  },
});

// ============================================================================
// Export Actions and Reducer
// ============================================================================

export const {
  clearData,
  clearError,
  setSelectedDataType,
  setDataFilter,
  resetFilters,
  setLoading,
  updateCacheExpiry,
} = dataSlice.actions;

export default dataSlice.reducer;
