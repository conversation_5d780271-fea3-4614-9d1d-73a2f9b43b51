/**
 * Clean Entry Point for SkyGeni Dashboard
 * Simple, professional React app initialization
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import CleanApp from './CleanApp';

// ============================================================================
// Application Bootstrap
// ============================================================================

const container = document.getElementById('root');

if (!container) {
  throw new Error('Root container not found');
}

const root = createRoot(container);

root.render(
  <React.StrictMode>
    <CleanApp />
  </React.StrictMode>
);

console.log('🚀 SkyGeni Dashboard initialized successfully');
