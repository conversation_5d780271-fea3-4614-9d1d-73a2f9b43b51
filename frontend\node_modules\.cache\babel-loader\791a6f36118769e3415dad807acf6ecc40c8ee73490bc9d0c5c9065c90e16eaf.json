{"ast": null, "code": "export default function () {\n  return Array.from(this);\n}", "map": {"version": 3, "names": ["Array", "from"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/nodes.js"], "sourcesContent": ["export default function() {\n  return Array.from(this);\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,OAAOA,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}