/**
 * AnimatedDonutChart Component
 * Responsive donut chart with sweep animation, tooltips, and hover effects
 */

import React, { useRef, useEffect } from 'react';
import * as d3 from 'd3';
import { Box } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

export interface DonutChartData {
  label: string;
  value: number;
}

export interface AnimatedDonutChartProps {
  data: DonutChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  animationDuration?: number;
  onSegmentHover?: (data: DonutChartData | null) => void;
}

// ============================================================================
// Default Values
// ============================================================================

const DEFAULT_MARGIN = { top: 20, right: 20, bottom: 20, left: 20 };
const DEFAULT_COLORS = d3.schemeCategory10;

// ============================================================================
// AnimatedDonutChart Component
// ============================================================================

const AnimatedDonutChart: React.FC<AnimatedDonutChartProps> = ({
  data,
  width = 400,
  height = 400,
  innerRadius = 80,
  outerRadius = 160,
  margin = DEFAULT_MARGIN,
  animationDuration = 1000,
  onSegmentHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    d3.select(svgRef.current).selectAll('*').remove();

    // Calculate dimensions
    const innerWidth = width - margin.left - margin.right;
    const innerHeight = height - margin.top - margin.bottom;
    const centerX = innerWidth / 2;
    const centerY = innerHeight / 2;

    // Calculate total value
    const total = data.reduce((sum, d) => sum + d.value, 0);

    // Validate data
    const validData = data.filter(d => d.value > 0 && d.label);
    if (!validData.length) return;

    // Create SVG container
    const svg = d3.select(svgRef.current);
    
    // Create main group
    const g = svg
      .append('g')
      .attr('transform', `translate(${margin.left + centerX}, ${margin.top + centerY})`);

    // Create pie generator
    const pie = d3.pie<DonutChartData>()
      .value(d => d.value)
      .sort(null)
      .padAngle(0.02); // Small padding between arcs

    // Create arc generators
    const arc = d3.arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius)
      .cornerRadius(3); // Rounded corners

    const hoverArc = d3.arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius + 8); // Slightly larger on hover

    // Generate pie data
    const pieData = pie(validData);

    // Create arcs with sweep animation
    const arcs = g
      .selectAll('.arc')
      .data(pieData)
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add arc paths with sweep animation
    const paths = arcs
      .append('path')
      .attr('fill', (d, i) => DEFAULT_COLORS[i % DEFAULT_COLORS.length])
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))')
      .each(function(d) {
        // Store original angles for animation
        const current = { startAngle: d.startAngle, endAngle: d.startAngle };
        d3.select(this).datum(current);
      })
      .attr('d', arc as any);

    // Animate sweep effect
    paths
      .transition()
      .duration(animationDuration)
      .delay((d, i) => i * 100) // Stagger animation
      .ease(d3.easeElastic.period(0.4))
      .attrTween('d', function(d) {
        const interpolate = d3.interpolate(
          { startAngle: d.startAngle, endAngle: d.startAngle },
          { startAngle: d.startAngle, endAngle: d.endAngle }
        );
        return function(t) {
          const current = interpolate(t);
          return arc(current as any) || '';
        };
      });

    // Add hover interactions
    paths
      .on('mouseover', function(event, d) {
        // Get mouse position
        const [mouseX, mouseY] = d3.pointer(event, document.body);
        
        // Scale up the arc
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', hoverArc as any)
          .style('filter', 'drop-shadow(0 4px 8px rgba(0,0,0,0.2))');

        // Calculate percentage
        const percentage = ((d.data.value / total) * 100).toFixed(1);

        // Create tooltip
        const tooltip = d3.select('body')
          .selectAll('.donut-tooltip')
          .data([0]);

        const tooltipEnter = tooltip.enter()
          .append('div')
          .attr('class', 'donut-tooltip')
          .style('position', 'absolute')
          .style('background', 'white')
          .style('border', '1px solid #ddd')
          .style('border-radius', '8px')
          .style('padding', '12px 16px')
          .style('font-size', '14px')
          .style('font-family', 'system-ui, -apple-system, sans-serif')
          .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.15)')
          .style('pointer-events', 'none')
          .style('z-index', '1000')
          .style('opacity', 0);

        const tooltipUpdate = tooltipEnter.merge(tooltip);

        // Update tooltip content
        tooltipUpdate.html(`
          <div style="font-weight: 600; margin-bottom: 8px; color: #333; font-size: 15px;">${d.data.label}</div>
          <div style="margin-bottom: 4px; color: #666;">Value: <strong style="color: #1976d2;">${d.data.value.toLocaleString()}</strong></div>
          <div style="color: #666;">Percentage: <strong style="color: #1976d2;">${percentage}%</strong></div>
        `);

        // Position and show tooltip
        tooltipUpdate
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px')
          .transition()
          .duration(200)
          .style('opacity', 1);

        onSegmentHover?.(d.data);
      })
      .on('mousemove', function(event) {
        // Update tooltip position
        const [mouseX, mouseY] = d3.pointer(event, document.body);
        
        d3.select('.donut-tooltip')
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px');
      })
      .on('mouseout', function(event, d) {
        // Scale back to normal
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', arc as any)
          .style('filter', 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))');

        // Hide tooltip
        d3.select('.donut-tooltip')
          .transition()
          .duration(200)
          .style('opacity', 0)
          .remove();

        onSegmentHover?.(null);
      });

    // Add center text with total value
    const centerGroup = g.append('g').attr('class', 'center-text');

    // Total value
    centerGroup
      .append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '-0.2em')
      .style('font-size', '28px')
      .style('font-weight', '700')
      .style('fill', '#333')
      .style('font-family', 'system-ui, -apple-system, sans-serif')
      .text(total.toLocaleString())
      .style('opacity', 0)
      .transition()
      .delay(animationDuration / 2)
      .duration(500)
      .style('opacity', 1);

    // Label
    centerGroup
      .append('text')
      .attr('text-anchor', 'middle')
      .attr('dy', '1.2em')
      .style('font-size', '14px')
      .style('font-weight', '500')
      .style('fill', '#666')
      .style('font-family', 'system-ui, -apple-system, sans-serif')
      .text('Total')
      .style('opacity', 0)
      .transition()
      .delay(animationDuration / 2 + 200)
      .duration(500)
      .style('opacity', 1);

    // Add percentage labels on arcs (for larger segments)
    const labelArcs = g
      .selectAll('.label-arc')
      .data(pieData.filter(d => (d.data.value / total) > 0.05)) // Only show labels for segments > 5%
      .enter()
      .append('g')
      .attr('class', 'label-arc');

    labelArcs
      .append('text')
      .attr('transform', d => {
        const centroid = arc.centroid(d);
        const factor = 1.2; // Move text outward
        return `translate(${centroid[0] * factor}, ${centroid[1] * factor})`;
      })
      .attr('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('font-weight', '600')
      .style('fill', 'white')
      .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.7)')
      .style('pointer-events', 'none')
      .text(d => {
        const percentage = ((d.data.value / total) * 100);
        return `${percentage.toFixed(1)}%`;
      })
      .style('opacity', 0)
      .transition()
      .delay(animationDuration + 300)
      .duration(500)
      .style('opacity', 1);

  }, [data, width, height, innerRadius, outerRadius, margin, animationDuration]);

  return (
    <Box sx={{ 
      position: 'relative', 
      display: 'inline-block',
      '& .arc': {
        transition: 'all 0.2s ease-in-out',
      }
    }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ 
          overflow: 'visible',
          background: 'transparent'
        }}
      />
    </Box>
  );
};

export default AnimatedDonutChart;
