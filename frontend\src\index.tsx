//Entry Point for SkyGeni Dashboard React Application


import React from 'react';
import { createRoot } from 'react-dom/client';
import CleanApp from './CleanApp';
import './styles/globals.css';


// Performance Monitoring


/**
 * Report web vitals for performance monitoring
 * This can be connected to analytics services
 */
const reportWebVitals = (onPerfEntry?: (metric: any) => void) => {
  if (onPerfEntry && onPerfEntry instanceof Function) {
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(onPerfEntry);
      getFID(onPerfEntry);
      getFCP(onPerfEntry);
      getLCP(onPerfEntry);
      getTTFB(onPerfEntry);
    });
  }
};


// Application Bootstrap


/**
 * Initialize and render the React application
 */
const initializeApp = () => {
  const container = document.getElementById('root');
  
  if (!container) {
    throw new Error('Root container not found. Make sure you have a div with id="root" in your HTML.');
  }

  // Create React 18 root
  const root = createRoot(container);

  // Render the application
  root.render(
    <React.StrictMode>
      <CleanApp />
    </React.StrictMode>
  );

  // Log successful initialization in development
  if (process.env.NODE_ENV === 'development') {
    console.log('🚀 SkyGeni Dashboard initialized successfully');
    console.log('📊 Ready to display data visualizations');
  }
};


// Error Handling


/**
 * Global error handler for unhandled errors
 */
window.addEventListener('error', (event) => {
  console.error('Global Error:', event.error);
});

/**
 * Global handler for unhandled promise rejections
 */
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason);
});


// Application Startup


try {
  initializeApp();
  
  // Report web vitals in production
  if (process.env.NODE_ENV === 'production') {
    reportWebVitals(console.log);
  }
} catch (error) {
  console.error('Failed to initialize application:', error);
  
  // Show a basic error message if the app fails to start
  const container = document.getElementById('root');
  if (container) {
    container.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 2rem;
      ">
        <h1 style="color: #d32f2f; margin-bottom: 1rem;">
          Application Failed to Start
        </h1>
        <p style="color: #666; margin-bottom: 2rem;">
          There was an error initializing the SkyGeni Dashboard.
        </p>
        <button 
          onclick="window.location.reload()" 
          style="
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
          "
        >
          Reload Page
        </button>
      </div>
    `;
  }
}
