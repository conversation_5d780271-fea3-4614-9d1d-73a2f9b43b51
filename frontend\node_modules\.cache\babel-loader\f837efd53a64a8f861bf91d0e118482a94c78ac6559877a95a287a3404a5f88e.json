{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport { Box, Grid, Typography, Paper, Alert, Fade, useTheme } from '@mui/material';\nimport { CustomerTypeCard, AccountIndustryCard, TeamCard, ACVRangeCard } from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0 // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.summary)) return null;\n    const {\n      summary\n    } = dashboardData;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 1,\n      sx: {\n        p: 2,\n        mb: 3,\n        border: `1px solid ${theme.palette.divider}`,\n        borderRadius: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: 700,\n          sx: {\n            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent'\n          },\n          children: \"\\uD83D\\uDCCA Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), lastFetched && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ml: 'auto',\n            textAlign: 'right'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Last updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: 500,\n            children: new Date(lastFetched).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: {\n          xs: 2,\n          sm: 3,\n          md: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: summary.totalCustomers.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Total Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'success.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: [\"$\", summary.totalRevenue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83C\\uDFE2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'info.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: summary.totalTeams\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Active Teams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'warning.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: [\"$\", Math.round(summary.averageACV).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Average ACV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Loader, {\n          size: \"large\",\n          message: \"Loading dashboard data...\",\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          error: error,\n          title: \"Failed to Load Dashboard\",\n          showRetryButton: true,\n          onRetry: refetch,\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 289,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 288,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          action: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refetch,\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 15\n          }, this),\n          children: \"No dashboard data available. Please check your data sources.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"SkyGeni Dashboard\",\n    children: /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 500,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            gutterBottom: true,\n            fontWeight: 600,\n            color: \"text.primary\",\n            sx: {\n              mb: 0.5\n            },\n            children: \"Data Analytics Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Customer insights and performance metrics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryStats, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: {\n            xs: 2,\n            sm: 3,\n            md: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 600,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 376,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 700,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 385,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 800,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(TeamCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 394,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 393,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 391,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 900,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 400,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 344,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"pF5peWU5QlZmCSoxyU0BtdoBz10=\", false, function () {\n  return [useTheme, useData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Typography", "Paper", "<PERSON><PERSON>", "Fade", "useTheme", "CustomerTypeCard", "AccountIndustryCard", "TeamCard", "ACVRangeCard", "Layout", "Loader", "ErrorMessage", "useData", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "theme", "dashboardData", "isLoading", "isError", "error", "isEmpty", "refetch", "lastFetched", "autoFetch", "refreshInterval", "SummaryStats", "summary", "elevation", "sx", "p", "mb", "border", "palette", "divider", "borderRadius", "children", "display", "alignItems", "variant", "fontWeight", "background", "primary", "main", "dark", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ml", "textAlign", "color", "Date", "toLocaleString", "container", "spacing", "xs", "sm", "md", "item", "transition", "transform", "boxShadow", "fontSize", "totalCustomers", "textTransform", "letterSpacing", "totalRevenue", "totalTeams", "Math", "round", "averageACV", "title", "justifyContent", "minHeight", "size", "message", "centered", "showRetryButton", "onRetry", "severity", "action", "onClick", "in", "timeout", "component", "gutterBottom", "lg", "xl", "height", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Paper,\n  Alert,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  CustomerTypeCard,\n  AccountIndustryCard,\n  TeamCard,\n  ACVRangeCard,\n} from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\n\nconst Dashboard: React.FC = () => {\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched,\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0, // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats: React.FC = () => {\n    if (!dashboardData?.summary) return null;\n\n    const { summary } = dashboardData;\n\n    return (\n      <Paper\n        elevation={1}\n        sx={{\n          p: 2,\n          mb: 3,\n          border: `1px solid ${theme.palette.divider}`,\n          borderRadius: 1,\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n          <Typography variant=\"h4\" fontWeight={700} sx={{\n            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n          }}>\n            📊 Dashboard Overview\n          </Typography>\n          {lastFetched && (\n            <Box sx={{ ml: 'auto', textAlign: 'right' }}>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Last updated\n              </Typography>\n              <Typography variant=\"body2\" fontWeight={500}>\n                {new Date(lastFetched).toLocaleString()}\n              </Typography>\n            </Box>\n          )}\n        </Box>\n\n        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                👥\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'primary.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                {summary.totalCustomers.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Total Customers\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                💰\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'success.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                ${summary.totalRevenue.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Total Revenue\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                🏢\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'info.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                {summary.totalTeams}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Active Teams\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                📈\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'warning.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                ${Math.round(summary.averageACV).toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Average ACV\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Loader\n            size=\"large\"\n            message=\"Loading dashboard data...\"\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <ErrorMessage\n            error={error}\n            title=\"Failed to Load Dashboard\"\n            showRetryButton={true}\n            onRetry={refetch}\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Alert\n            severity=\"info\"\n            action={\n              <button onClick={refetch}>\n                Retry\n              </button>\n            }\n          >\n            No dashboard data available. Please check your data sources.\n          </Alert>\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return (\n    <Layout title=\"SkyGeni Dashboard\">\n      <Fade in={true} timeout={500}>\n        <Box>\n          {/* Page Header */}\n          <Box sx={{ mb: 3 }}>\n            <Typography\n              variant=\"h5\"\n              component=\"h1\"\n              gutterBottom\n              fontWeight={600}\n              color=\"text.primary\"\n              sx={{ mb: 0.5 }}\n            >\n              Data Analytics Dashboard\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n            >\n              Customer insights and performance metrics\n            </Typography>\n          </Box>\n\n          {/* Summary Statistics */}\n          <SummaryStats />\n\n          {/* Data Cards Grid */}\n          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n            {/* Customer Types Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={600}>\n                <Box sx={{ height: '100%' }}>\n                  <CustomerTypeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Account Industries Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={700}>\n                <Box sx={{ height: '100%' }}>\n                  <AccountIndustryCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Teams Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={800}>\n                <Box sx={{ height: '100%' }}>\n                  <TeamCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* ACV Ranges Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={900}>\n                <Box sx={{ height: '100%' }}>\n                  <ACVRangeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n          </Grid>\n\n          {/* Additional Information */}\n          <Box sx={{ mt: 4, textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\n            </Typography>\n          </Box>\n        </Box>\n      </Fade>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJc,aAAa;IACbC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC,GAAGZ,OAAO,CAAC;IACVa,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,CAAC,CAAE;EACtB,CAAC,CAAC;;EAEF;EACA;EACA;;EAEA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACT,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEU,OAAO,GAAE,OAAO,IAAI;IAExC,MAAM;MAAEA;IAAQ,CAAC,GAAGV,aAAa;IAEjC,oBACEJ,OAAA,CAACb,KAAK;MACJ4B,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACFC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,aAAahB,KAAK,CAACiB,OAAO,CAACC,OAAO,EAAE;QAC5CC,YAAY,EAAE;MAChB,CAAE;MAAAC,QAAA,gBAEFvB,OAAA,CAAChB,GAAG;QAACgC,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEP,EAAE,EAAE;QAAE,CAAE;QAAAK,QAAA,gBACxDvB,OAAA,CAACd,UAAU;UAACwC,OAAO,EAAC,IAAI;UAACC,UAAU,EAAE,GAAI;UAACX,EAAE,EAAE;YAC5CY,UAAU,EAAE,2BAA2BzB,KAAK,CAACiB,OAAO,CAACS,OAAO,CAACC,IAAI,KAAK3B,KAAK,CAACiB,OAAO,CAACS,OAAO,CAACE,IAAI,GAAG;YACnGC,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE;UACvB,CAAE;UAAAX,QAAA,EAAC;QAEH;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZ5B,WAAW,iBACVV,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEuB,EAAE,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAAjB,QAAA,gBAC1CvB,OAAA,CAACd,UAAU;YAACwC,OAAO,EAAC,SAAS;YAACe,KAAK,EAAC,gBAAgB;YAAAlB,QAAA,EAAC;UAErD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;YAACwC,OAAO,EAAC,OAAO;YAACC,UAAU,EAAE,GAAI;YAAAJ,QAAA,EACzC,IAAImB,IAAI,CAAChC,WAAW,CAAC,CAACiC,cAAc,CAAC;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENtC,OAAA,CAACf,IAAI;QAAC2D,SAAS;QAACC,OAAO,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAzB,QAAA,gBAC/CvB,OAAA,CAACf,IAAI;UAACgE,IAAI;UAACH,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFwB,SAAS,EAAE,QAAQ;cACnBvB,CAAC,EAAE;gBAAE6B,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACnBzB,YAAY,EAAE,CAAC;cACfM,UAAU,EAAE,0BAA0B;cACtCT,MAAM,EAAE,+BAA+B;cACvC+B,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAA7B,QAAA,gBAEFvB,OAAA,CAACd,UAAU;cAACwC,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAEnC,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,EAAC;YAE1D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,IAAI;cACZV,EAAE,EAAE;gBACFyB,KAAK,EAAE,cAAc;gBACrBd,UAAU,EAAE,GAAG;gBACf0B,QAAQ,EAAE;kBAAEP,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACtC7B,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,EAEDT,OAAO,CAACwC,cAAc,CAACX,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,OAAO;cACfe,KAAK,EAAC,gBAAgB;cACtBd,UAAU,EAAE,GAAI;cAChBX,EAAE,EAAE;gBAAEuC,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAAjC,QAAA,EAC5D;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPtC,OAAA,CAACf,IAAI;UAACgE,IAAI;UAACH,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFwB,SAAS,EAAE,QAAQ;cACnBvB,CAAC,EAAE;gBAAE6B,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACnBzB,YAAY,EAAE,CAAC;cACfM,UAAU,EAAE,0BAA0B;cACtCT,MAAM,EAAE,+BAA+B;cACvC+B,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAA7B,QAAA,gBAEFvB,OAAA,CAACd,UAAU;cAACwC,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAEnC,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,EAAC;YAE1D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,IAAI;cACZV,EAAE,EAAE;gBACFyB,KAAK,EAAE,cAAc;gBACrBd,UAAU,EAAE,GAAG;gBACf0B,QAAQ,EAAE;kBAAEP,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACtC7B,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,GACH,GACE,EAACT,OAAO,CAAC2C,YAAY,CAACd,cAAc,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,OAAO;cACfe,KAAK,EAAC,gBAAgB;cACtBd,UAAU,EAAE,GAAI;cAChBX,EAAE,EAAE;gBAAEuC,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAAjC,QAAA,EAC5D;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPtC,OAAA,CAACf,IAAI;UAACgE,IAAI;UAACH,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFwB,SAAS,EAAE,QAAQ;cACnBvB,CAAC,EAAE;gBAAE6B,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACnBzB,YAAY,EAAE,CAAC;cACfM,UAAU,EAAE,0BAA0B;cACtCT,MAAM,EAAE,+BAA+B;cACvC+B,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAA7B,QAAA,gBAEFvB,OAAA,CAACd,UAAU;cAACwC,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAEnC,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,EAAC;YAE1D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,IAAI;cACZV,EAAE,EAAE;gBACFyB,KAAK,EAAE,WAAW;gBAClBd,UAAU,EAAE,GAAG;gBACf0B,QAAQ,EAAE;kBAAEP,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACtC7B,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,EAEDT,OAAO,CAAC4C;YAAU;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,OAAO;cACfe,KAAK,EAAC,gBAAgB;cACtBd,UAAU,EAAE,GAAI;cAChBX,EAAE,EAAE;gBAAEuC,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAAjC,QAAA,EAC5D;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPtC,OAAA,CAACf,IAAI;UAACgE,IAAI;UAACH,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxB,QAAA,eACtBvB,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFwB,SAAS,EAAE,QAAQ;cACnBvB,CAAC,EAAE;gBAAE6B,EAAE,EAAE,CAAC;gBAAEC,EAAE,EAAE;cAAE,CAAC;cACnBzB,YAAY,EAAE,CAAC;cACfM,UAAU,EAAE,0BAA0B;cACtCT,MAAM,EAAE,+BAA+B;cACvC+B,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAA7B,QAAA,gBAEFvB,OAAA,CAACd,UAAU;cAACwC,OAAO,EAAC,IAAI;cAACV,EAAE,EAAE;gBAAEqC,QAAQ,EAAE,MAAM;gBAAEnC,EAAE,EAAE;cAAE,CAAE;cAAAK,QAAA,EAAC;YAE1D;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,IAAI;cACZV,EAAE,EAAE;gBACFyB,KAAK,EAAE,cAAc;gBACrBd,UAAU,EAAE,GAAG;gBACf0B,QAAQ,EAAE;kBAAEP,EAAE,EAAE,QAAQ;kBAAEC,EAAE,EAAE;gBAAO,CAAC;gBACtC7B,EAAE,EAAE;cACN,CAAE;cAAAK,QAAA,GACH,GACE,EAACoC,IAAI,CAACC,KAAK,CAAC9C,OAAO,CAAC+C,UAAU,CAAC,CAAClB,cAAc,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACbtC,OAAA,CAACd,UAAU;cACTwC,OAAO,EAAC,OAAO;cACfe,KAAK,EAAC,gBAAgB;cACtBd,UAAU,EAAE,GAAI;cAChBX,EAAE,EAAE;gBAAEuC,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAAjC,QAAA,EAC5D;YAED;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA;EACA;;EAEA,IAAIjC,SAAS,EAAE;IACb,oBACEL,OAAA,CAACL,MAAM;MAACmE,KAAK,EAAC,mBAAmB;MAAAvC,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfuC,cAAc,EAAE,QAAQ;UACxBtC,UAAU,EAAE,QAAQ;UACpBuC,SAAS,EAAE;QACb,CAAE;QAAAzC,QAAA,eAEFvB,OAAA,CAACJ,MAAM;UACLqE,IAAI,EAAC,OAAO;UACZC,OAAO,EAAC,2BAA2B;UACnCC,QAAQ,EAAE;QAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAIhC,OAAO,EAAE;IACX,oBACEN,OAAA,CAACL,MAAM;MAACmE,KAAK,EAAC,mBAAmB;MAAAvC,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfuC,cAAc,EAAE,QAAQ;UACxBtC,UAAU,EAAE,QAAQ;UACpBuC,SAAS,EAAE;QACb,CAAE;QAAAzC,QAAA,eAEFvB,OAAA,CAACH,YAAY;UACXU,KAAK,EAAEA,KAAM;UACbuD,KAAK,EAAC,0BAA0B;UAChCM,eAAe,EAAE,IAAK;UACtBC,OAAO,EAAE5D,OAAQ;UACjB0D,QAAQ,EAAE;QAAK;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAI9B,OAAO,EAAE;IACX,oBACER,OAAA,CAACL,MAAM;MAACmE,KAAK,EAAC,mBAAmB;MAAAvC,QAAA,eAC/BvB,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFQ,OAAO,EAAE,MAAM;UACfuC,cAAc,EAAE,QAAQ;UACxBtC,UAAU,EAAE,QAAQ;UACpBuC,SAAS,EAAE;QACb,CAAE;QAAAzC,QAAA,eAEFvB,OAAA,CAACZ,KAAK;UACJkF,QAAQ,EAAC,MAAM;UACfC,MAAM,eACJvE,OAAA;YAAQwE,OAAO,EAAE/D,OAAQ;YAAAc,QAAA,EAAC;UAE1B;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAf,QAAA,EACF;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,oBACEtC,OAAA,CAACL,MAAM;IAACmE,KAAK,EAAC,mBAAmB;IAAAvC,QAAA,eAC/BvB,OAAA,CAACX,IAAI;MAACoF,EAAE,EAAE,IAAK;MAACC,OAAO,EAAE,GAAI;MAAAnD,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;QAAAuC,QAAA,gBAEFvB,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAK,QAAA,gBACjBvB,OAAA,CAACd,UAAU;YACTwC,OAAO,EAAC,IAAI;YACZiD,SAAS,EAAC,IAAI;YACdC,YAAY;YACZjD,UAAU,EAAE,GAAI;YAChBc,KAAK,EAAC,cAAc;YACpBzB,EAAE,EAAE;cAAEE,EAAE,EAAE;YAAI,CAAE;YAAAK,QAAA,EACjB;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtC,OAAA,CAACd,UAAU;YACTwC,OAAO,EAAC,OAAO;YACfe,KAAK,EAAC,gBAAgB;YAAAlB,QAAA,EACvB;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNtC,OAAA,CAACa,YAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhBtC,OAAA,CAACf,IAAI;UAAC2D,SAAS;UAACC,OAAO,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAzB,QAAA,gBAE/CvB,OAAA,CAACf,IAAI;YAACgE,IAAI;YAACH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC8B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAvD,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACoF,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAnD,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAE+D,MAAM,EAAE;gBAAO,CAAE;gBAAAxD,QAAA,eAC1BvB,OAAA,CAACT,gBAAgB;kBAACwB,SAAS,EAAE;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPtC,OAAA,CAACf,IAAI;YAACgE,IAAI;YAACH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC8B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAvD,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACoF,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAnD,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAE+D,MAAM,EAAE;gBAAO,CAAE;gBAAAxD,QAAA,eAC1BvB,OAAA,CAACR,mBAAmB;kBAACuB,SAAS,EAAE;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPtC,OAAA,CAACf,IAAI;YAACgE,IAAI;YAACH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC8B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAvD,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACoF,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAnD,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAE+D,MAAM,EAAE;gBAAO,CAAE;gBAAAxD,QAAA,eAC1BvB,OAAA,CAACP,QAAQ;kBAACsB,SAAS,EAAE;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPtC,OAAA,CAACf,IAAI;YAACgE,IAAI;YAACH,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC8B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAvD,QAAA,eACrCvB,OAAA,CAACX,IAAI;cAACoF,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAnD,QAAA,eAC3BvB,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAE+D,MAAM,EAAE;gBAAO,CAAE;gBAAAxD,QAAA,eAC1BvB,OAAA,CAACN,YAAY;kBAACqB,SAAS,EAAE;gBAAE;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPtC,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEgE,EAAE,EAAE,CAAC;YAAExC,SAAS,EAAE;UAAS,CAAE;UAAAjB,QAAA,eACtCvB,OAAA,CAACd,UAAU;YAACwC,OAAO,EAAC,OAAO;YAACe,KAAK,EAAC,gBAAgB;YAAAlB,QAAA,EAAC;UAEnD;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAACpC,EAAA,CA9XID,SAAmB;EAAA,QACTX,QAAQ,EASlBQ,OAAO;AAAA;AAAAmF,EAAA,GAVPhF,SAAmB;AAgYzB,eAAeA,SAAS;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}