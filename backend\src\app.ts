/**
 * Express Application Setup for SkyGeni Dashboard Backend
 * 
 * This file configures the Express application with:
 * - Security middleware (helmet, cors)
 * - Logging middleware (morgan)
 * - Compression middleware
 * - JSON parsing
 * - Route handlers
 * - Error handling
 */

import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import dotenv from 'dotenv';

// Import route handlers
import dataRoutes from './routes/dataRoutes';
import { ApiError } from './types';

// Load environment variables
dotenv.config();

// ============================================================================
// Application Configuration
// ============================================================================

const app: Application = express();

// ============================================================================
// Security Middleware
// ============================================================================

/**
 * Helmet helps secure Express apps by setting various HTTP headers
 */
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false, // Allow embedding for development
}));

/**
 * CORS configuration for frontend access
 */
app.use(cors({
  origin: [
    'http://localhost:3000',  // React development server
    'http://localhost:3001',  // Alternative React port
    'http://127.0.0.1:3000',
    'http://127.0.0.1:3001',
    // Add production URLs here when deploying
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));

// ============================================================================
// General Middleware
// ============================================================================

/**
 * Compression middleware to gzip responses
 */
app.use(compression());

/**
 * HTTP request logging
 */
app.use(morgan(process.env.NODE_ENV === 'production' ? 'combined' : 'dev'));

/**
 * Body parsing middleware
 */
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// ============================================================================
// Request Logging and Timing
// ============================================================================

/**
 * Add request timing and logging
 */
app.use((req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log incoming request
  console.log(`🔄 ${req.method} ${req.path} - ${new Date().toISOString()}`);
  
  // Add response time header
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`✅ ${req.method} ${req.path} - ${res.statusCode} - ${duration}ms`);
  });
  
  next();
});

// ============================================================================
// API Routes
// ============================================================================

/**
 * Health check endpoint for the entire application
 */
app.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'SkyGeni Dashboard API is healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
    version: '1.0.0'
  });
});

/**
 * API information endpoint
 */
app.get('/api', (req: Request, res: Response) => {
  res.json({
    success: true,
    message: 'SkyGeni Dashboard API',
    version: '1.0.0',
    endpoints: {
      health: '/health',
      data: {
        dashboard: '/api/data/dashboard',
        customerTypes: '/api/data/customer-types',
        accountIndustries: '/api/data/account-industries',
        teams: '/api/data/teams',
        acvRanges: '/api/data/acv-ranges',
        charts: '/api/data/charts/:type?dataType=:dataType'
      }
    },
    documentation: 'See README.md for detailed API documentation'
  });
});

/**
 * Mount data routes
 */
app.use('/api/data', dataRoutes);

// ============================================================================
// Error Handling Middleware
// ============================================================================

/**
 * 404 handler for undefined routes
 */
app.use('*', (req: Request, res: Response) => {
  const error: ApiError = {
    success: false,
    error: 'NOT_FOUND',
    message: `Route ${req.method} ${req.originalUrl} not found`,
    statusCode: 404,
    timestamp: new Date().toISOString()
  };
  
  console.log(`❌ 404 - ${req.method} ${req.originalUrl}`);
  res.status(404).json(error);
});

/**
 * Global error handler
 */
app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
  console.error('❌ Unhandled application error:', error);
  
  const apiError: ApiError = {
    success: false,
    error: 'INTERNAL_SERVER_ERROR',
    message: process.env.NODE_ENV === 'production' 
      ? 'An internal server error occurred' 
      : error.message,
    statusCode: 500,
    timestamp: new Date().toISOString()
  };
  
  res.status(500).json(apiError);
});

// ============================================================================
// Graceful Shutdown Handling
// ============================================================================

/**
 * Handle graceful shutdown
 */
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received. Shutting down gracefully...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received. Shutting down gracefully...');
  process.exit(0);
});

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  console.error('💥 Uncaught Exception:', error);
  process.exit(1);
});

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
