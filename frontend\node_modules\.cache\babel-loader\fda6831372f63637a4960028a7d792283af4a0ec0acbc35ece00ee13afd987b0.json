{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport { Box, Grid, Typography, Paper, Alert, Fade, useTheme } from '@mui/material';\nimport { CustomerTypeCard, AccountIndustryCard, TeamCard, ACVRangeCard } from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0 // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.summary)) return null;\n    const {\n      summary\n    } = dashboardData;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 2,\n      sx: {\n        p: 3,\n        mb: 3,\n        background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,\n        border: `1px solid ${theme.palette.divider}`\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        gutterBottom: true,\n        fontWeight: 600,\n        children: \"Dashboard Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              fontWeight: \"bold\",\n              children: summary.totalCustomers.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 81,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"secondary\",\n              fontWeight: \"bold\",\n              children: [\"$\", summary.totalRevenue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"success.main\",\n              fontWeight: \"bold\",\n              children: summary.totalTeams\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Active Teams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            textAlign: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"warning.main\",\n              fontWeight: \"bold\",\n              children: [\"$\", summary.averageACV.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Average ACV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Loader, {\n          size: \"large\",\n          message: \"Loading dashboard data...\",\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          error: error,\n          title: \"Failed to Load Dashboard\",\n          showRetryButton: true,\n          onRetry: refetch,\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          action: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refetch,\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 15\n          }, this),\n          children: \"No dashboard data available. Please check your data sources.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"SkyGeni Dashboard\",\n    children: /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 500,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            component: \"h1\",\n            gutterBottom: true,\n            fontWeight: 600,\n            children: \"Data Analytics Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body1\",\n            color: \"text.secondary\",\n            children: \"Comprehensive view of customer types, account industries, teams, and ACV ranges\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), lastFetched && /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            sx: {\n              mt: 1,\n              display: 'block'\n            },\n            children: [\"Last updated: \", new Date(lastFetched).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryStats, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: {\n            xs: 2,\n            sm: 3,\n            md: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 600,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 700,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 800,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(TeamCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 900,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 211,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"pF5peWU5QlZmCSoxyU0BtdoBz10=\", false, function () {\n  return [useTheme, useData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Typography", "Paper", "<PERSON><PERSON>", "Fade", "useTheme", "CustomerTypeCard", "AccountIndustryCard", "TeamCard", "ACVRangeCard", "Layout", "Loader", "ErrorMessage", "useData", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "theme", "dashboardData", "isLoading", "isError", "error", "isEmpty", "refetch", "lastFetched", "autoFetch", "refreshInterval", "SummaryStats", "summary", "elevation", "sx", "p", "mb", "background", "palette", "primary", "main", "secondary", "border", "divider", "children", "variant", "gutterBottom", "fontWeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "container", "spacing", "item", "xs", "sm", "textAlign", "color", "totalCustomers", "toLocaleString", "totalRevenue", "totalTeams", "averageACV", "title", "display", "justifyContent", "alignItems", "minHeight", "size", "message", "centered", "showRetryButton", "onRetry", "severity", "action", "onClick", "in", "timeout", "component", "mt", "Date", "md", "lg", "xl", "height", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Paper,\n  Alert,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  CustomerTypeCard,\n  AccountIndustryCard,\n  TeamCard,\n  ACVRangeCard,\n} from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\n\nconst Dashboard: React.FC = () => {\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched,\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0, // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats: React.FC = () => {\n    if (!dashboardData?.summary) return null;\n\n    const { summary } = dashboardData;\n\n    return (\n      <Paper\n        elevation={2}\n        sx={{\n          p: 3,\n          mb: 3,\n          background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,\n          border: `1px solid ${theme.palette.divider}`,\n        }}\n      >\n        <Typography variant=\"h5\" gutterBottom fontWeight={600}>\n          Dashboard Overview\n        </Typography>\n        \n        <Grid container spacing={3}>\n          <Grid item xs={6} sm={3}>\n            <Box textAlign=\"center\">\n              <Typography variant=\"h4\" color=\"primary\" fontWeight=\"bold\">\n                {summary.totalCustomers.toLocaleString()}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Total Customers\n              </Typography>\n            </Box>\n          </Grid>\n          \n          <Grid item xs={6} sm={3}>\n            <Box textAlign=\"center\">\n              <Typography variant=\"h4\" color=\"secondary\" fontWeight=\"bold\">\n                ${summary.totalRevenue.toLocaleString()}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Total Revenue\n              </Typography>\n            </Box>\n          </Grid>\n          \n          <Grid item xs={6} sm={3}>\n            <Box textAlign=\"center\">\n              <Typography variant=\"h4\" color=\"success.main\" fontWeight=\"bold\">\n                {summary.totalTeams}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Active Teams\n              </Typography>\n            </Box>\n          </Grid>\n          \n          <Grid item xs={6} sm={3}>\n            <Box textAlign=\"center\">\n              <Typography variant=\"h4\" color=\"warning.main\" fontWeight=\"bold\">\n                ${summary.averageACV.toLocaleString()}\n              </Typography>\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Average ACV\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Loader\n            size=\"large\"\n            message=\"Loading dashboard data...\"\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <ErrorMessage\n            error={error}\n            title=\"Failed to Load Dashboard\"\n            showRetryButton={true}\n            onRetry={refetch}\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Alert\n            severity=\"info\"\n            action={\n              <button onClick={refetch}>\n                Retry\n              </button>\n            }\n          >\n            No dashboard data available. Please check your data sources.\n          </Alert>\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return (\n    <Layout title=\"SkyGeni Dashboard\">\n      <Fade in={true} timeout={500}>\n        <Box>\n          {/* Page Header */}\n          <Box sx={{ mb: 3 }}>\n            <Typography variant=\"h4\" component=\"h1\" gutterBottom fontWeight={600}>\n              Data Analytics Dashboard\n            </Typography>\n            <Typography variant=\"body1\" color=\"text.secondary\">\n              Comprehensive view of customer types, account industries, teams, and ACV ranges\n            </Typography>\n            {lastFetched && (\n              <Typography variant=\"caption\" color=\"text.secondary\" sx={{ mt: 1, display: 'block' }}>\n                Last updated: {new Date(lastFetched).toLocaleString()}\n              </Typography>\n            )}\n          </Box>\n\n          {/* Summary Statistics */}\n          <SummaryStats />\n\n          {/* Data Cards Grid */}\n          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n            {/* Customer Types Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={600}>\n                <Box sx={{ height: '100%' }}>\n                  <CustomerTypeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Account Industries Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={700}>\n                <Box sx={{ height: '100%' }}>\n                  <AccountIndustryCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Teams Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={800}>\n                <Box sx={{ height: '100%' }}>\n                  <TeamCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* ACV Ranges Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={900}>\n                <Box sx={{ height: '100%' }}>\n                  <ACVRangeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n          </Grid>\n\n          {/* Additional Information */}\n          <Box sx={{ mt: 4, textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\n            </Typography>\n          </Box>\n        </Box>\n      </Fade>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJc,aAAa;IACbC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC,GAAGZ,OAAO,CAAC;IACVa,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,CAAC,CAAE;EACtB,CAAC,CAAC;;EAEF;EACA;EACA;;EAEA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACT,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEU,OAAO,GAAE,OAAO,IAAI;IAExC,MAAM;MAAEA;IAAQ,CAAC,GAAGV,aAAa;IAEjC,oBACEJ,OAAA,CAACb,KAAK;MACJ4B,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACFC,CAAC,EAAE,CAAC;QACJC,EAAE,EAAE,CAAC;QACLC,UAAU,EAAE,2BAA2BhB,KAAK,CAACiB,OAAO,CAACC,OAAO,CAACC,IAAI,OAAOnB,KAAK,CAACiB,OAAO,CAACG,SAAS,CAACD,IAAI,KAAK;QACzGE,MAAM,EAAE,aAAarB,KAAK,CAACiB,OAAO,CAACK,OAAO;MAC5C,CAAE;MAAAC,QAAA,gBAEF1B,OAAA,CAACd,UAAU;QAACyC,OAAO,EAAC,IAAI;QAACC,YAAY;QAACC,UAAU,EAAE,GAAI;QAAAH,QAAA,EAAC;MAEvD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjC,OAAA,CAACf,IAAI;QAACiD,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAT,QAAA,gBACzB1B,OAAA,CAACf,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACtB1B,OAAA,CAAChB,GAAG;YAACuD,SAAS,EAAC,QAAQ;YAAAb,QAAA,gBACrB1B,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,IAAI;cAACa,KAAK,EAAC,SAAS;cAACX,UAAU,EAAC,MAAM;cAAAH,QAAA,EACvDZ,OAAO,CAAC2B,cAAc,CAACC,cAAc,CAAC;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbjC,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,OAAO;cAACa,KAAK,EAAC,gBAAgB;cAAAd,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACtB1B,OAAA,CAAChB,GAAG;YAACuD,SAAS,EAAC,QAAQ;YAAAb,QAAA,gBACrB1B,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,IAAI;cAACa,KAAK,EAAC,WAAW;cAACX,UAAU,EAAC,MAAM;cAAAH,QAAA,GAAC,GAC1D,EAACZ,OAAO,CAAC6B,YAAY,CAACD,cAAc,CAAC,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbjC,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,OAAO;cAACa,KAAK,EAAC,gBAAgB;cAAAd,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACtB1B,OAAA,CAAChB,GAAG;YAACuD,SAAS,EAAC,QAAQ;YAAAb,QAAA,gBACrB1B,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,IAAI;cAACa,KAAK,EAAC,cAAc;cAACX,UAAU,EAAC,MAAM;cAAAH,QAAA,EAC5DZ,OAAO,CAAC8B;YAAU;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbjC,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,OAAO;cAACa,KAAK,EAAC,gBAAgB;cAAAd,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjC,OAAA,CAACf,IAAI;UAACmD,IAAI;UAACC,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAZ,QAAA,eACtB1B,OAAA,CAAChB,GAAG;YAACuD,SAAS,EAAC,QAAQ;YAAAb,QAAA,gBACrB1B,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,IAAI;cAACa,KAAK,EAAC,cAAc;cAACX,UAAU,EAAC,MAAM;cAAAH,QAAA,GAAC,GAC7D,EAACZ,OAAO,CAAC+B,UAAU,CAACH,cAAc,CAAC,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACbjC,OAAA,CAACd,UAAU;cAACyC,OAAO,EAAC,OAAO;cAACa,KAAK,EAAC,gBAAgB;cAAAd,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA;EACA;;EAEA,IAAI5B,SAAS,EAAE;IACb,oBACEL,OAAA,CAACL,MAAM;MAACmD,KAAK,EAAC,mBAAmB;MAAApB,QAAA,eAC/B1B,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACF+B,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eAEF1B,OAAA,CAACJ,MAAM;UACLuD,IAAI,EAAC,OAAO;UACZC,OAAO,EAAC,2BAA2B;UACnCC,QAAQ,EAAE;QAAK;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAI3B,OAAO,EAAE;IACX,oBACEN,OAAA,CAACL,MAAM;MAACmD,KAAK,EAAC,mBAAmB;MAAApB,QAAA,eAC/B1B,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACF+B,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eAEF1B,OAAA,CAACH,YAAY;UACXU,KAAK,EAAEA,KAAM;UACbuC,KAAK,EAAC,0BAA0B;UAChCQ,eAAe,EAAE,IAAK;UACtBC,OAAO,EAAE9C,OAAQ;UACjB4C,QAAQ,EAAE;QAAK;UAAAvB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAIzB,OAAO,EAAE;IACX,oBACER,OAAA,CAACL,MAAM;MAACmD,KAAK,EAAC,mBAAmB;MAAApB,QAAA,eAC/B1B,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACF+B,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE,QAAQ;UACpBC,SAAS,EAAE;QACb,CAAE;QAAAxB,QAAA,eAEF1B,OAAA,CAACZ,KAAK;UACJoE,QAAQ,EAAC,MAAM;UACfC,MAAM,eACJzD,OAAA;YAAQ0D,OAAO,EAAEjD,OAAQ;YAAAiB,QAAA,EAAC;UAE1B;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAP,QAAA,EACF;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,oBACEjC,OAAA,CAACL,MAAM;IAACmD,KAAK,EAAC,mBAAmB;IAAApB,QAAA,eAC/B1B,OAAA,CAACX,IAAI;MAACsE,EAAE,EAAE,IAAK;MAACC,OAAO,EAAE,GAAI;MAAAlC,QAAA,eAC3B1B,OAAA,CAAChB,GAAG;QAAA0C,QAAA,gBAEF1B,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAQ,QAAA,gBACjB1B,OAAA,CAACd,UAAU;YAACyC,OAAO,EAAC,IAAI;YAACkC,SAAS,EAAC,IAAI;YAACjC,YAAY;YAACC,UAAU,EAAE,GAAI;YAAAH,QAAA,EAAC;UAEtE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjC,OAAA,CAACd,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAAd,QAAA,EAAC;UAEnD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,EACZvB,WAAW,iBACVV,OAAA,CAACd,UAAU;YAACyC,OAAO,EAAC,SAAS;YAACa,KAAK,EAAC,gBAAgB;YAACxB,EAAE,EAAE;cAAE8C,EAAE,EAAE,CAAC;cAAEf,OAAO,EAAE;YAAQ,CAAE;YAAArB,QAAA,GAAC,gBACtE,EAAC,IAAIqC,IAAI,CAACrD,WAAW,CAAC,CAACgC,cAAc,CAAC,CAAC;UAAA;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNjC,OAAA,CAACa,YAAY;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhBjC,OAAA,CAACf,IAAI;UAACiD,SAAS;UAACC,OAAO,EAAE;YAAEE,EAAE,EAAE,CAAC;YAAEC,EAAE,EAAE,CAAC;YAAE0B,EAAE,EAAE;UAAE,CAAE;UAAAtC,QAAA,gBAE/C1B,OAAA,CAACf,IAAI;YAACmD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC2B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxC,QAAA,eACrC1B,OAAA,CAACX,IAAI;cAACsE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAlC,QAAA,eAC3B1B,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmD,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,eAC1B1B,OAAA,CAACT,gBAAgB;kBAACwB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACmD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC2B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxC,QAAA,eACrC1B,OAAA,CAACX,IAAI;cAACsE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAlC,QAAA,eAC3B1B,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmD,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,eAC1B1B,OAAA,CAACR,mBAAmB;kBAACuB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACmD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC2B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxC,QAAA,eACrC1B,OAAA,CAACX,IAAI;cAACsE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAlC,QAAA,eAC3B1B,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmD,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,eAC1B1B,OAAA,CAACP,QAAQ;kBAACsB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPjC,OAAA,CAACf,IAAI;YAACmD,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAC2B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAxC,QAAA,eACrC1B,OAAA,CAACX,IAAI;cAACsE,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAAlC,QAAA,eAC3B1B,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmD,MAAM,EAAE;gBAAO,CAAE;gBAAAzC,QAAA,eAC1B1B,OAAA,CAACN,YAAY;kBAACqB,SAAS,EAAE;gBAAE;kBAAAe,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPjC,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAE8C,EAAE,EAAE,CAAC;YAAEvB,SAAS,EAAE;UAAS,CAAE;UAAAb,QAAA,eACtC1B,OAAA,CAACd,UAAU;YAACyC,OAAO,EAAC,OAAO;YAACa,KAAK,EAAC,gBAAgB;YAAAd,QAAA,EAAC;UAEnD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAAC/B,EAAA,CApPID,SAAmB;EAAA,QACTX,QAAQ,EASlBQ,OAAO;AAAA;AAAAsE,EAAA,GAVPnE,SAAmB;AAsPzB,eAAeA,SAAS;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}