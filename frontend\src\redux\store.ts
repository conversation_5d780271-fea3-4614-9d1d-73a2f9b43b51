/**
 * Redux Store Configuration for SkyGeni Dashboard
 * 
 * This file sets up the Redux store with:
 * - Redux Toolkit for modern Redux patterns
 * - TypeScript support
 * - DevTools integration
 * - Middleware configuration
 * - Root state type exports
 */

import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import dataSlice from './slices/dataSlice';

// ============================================================================
// Store Configuration
// ============================================================================

/**
 * Configure the Redux store with all slices and middleware
 */
export const store = configureStore({
  reducer: {
    data: dataSlice,
    // Add more slices here as the application grows
    // ui: uiSlice,
    // auth: authSlice,
  },
  
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      // Configure serializable check for better performance
      serializableCheck: {
        ignoredActions: [
          // Ignore these action types in serializable check
          'persist/PERSIST',
          'persist/REHYDRATE',
        ],
        ignoredPaths: [
          // Ignore these paths in state for serializable check
          'data.lastFetched',
        ],
      },
      
      // Enable immutability check in development
      immutableCheck: {
        warnAfter: 128, // Warn if immutability check takes longer than 128ms
      },
    }),
  
  // Enable Redux DevTools in development
  devTools: process.env.NODE_ENV !== 'production',
  
  // Preloaded state (useful for SSR or initial state)
  preloadedState: undefined,
});

// ============================================================================
// Type Definitions
// ============================================================================

/**
 * Root state type - inferred from the store
 */
export type RootState = ReturnType<typeof store.getState>;

/**
 * App dispatch type - includes thunk types
 */
export type AppDispatch = typeof store.dispatch;

// ============================================================================
// Typed Hooks
// ============================================================================

/**
 * Typed version of useDispatch hook
 * Use this instead of plain useDispatch to get proper TypeScript support
 */
export const useAppDispatch = () => useDispatch<AppDispatch>();

/**
 * Typed version of useSelector hook
 * Use this instead of plain useSelector to get proper TypeScript support
 */
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// ============================================================================
// Store Utilities
// ============================================================================

/**
 * Get the current state of the store
 * Useful for debugging or testing
 */
export const getCurrentState = (): RootState => store.getState();

/**
 * Subscribe to store changes
 * Returns an unsubscribe function
 */
export const subscribeToStore = (listener: () => void): (() => void) => {
  return store.subscribe(listener);
};

// ============================================================================
// Development Utilities
// ============================================================================

if (process.env.NODE_ENV === 'development') {
  // Make store available globally for debugging
  (window as any).__REDUX_STORE__ = store;

  // Disable store logging to prevent infinite loops
  // store.subscribe(() => {
  //   const state = store.getState();
  //   console.log('🔄 Store State Updated:', {
  //     data: {
  //       loading: state.data.loading,
  //       error: state.data.error,
  //       hasData: !!state.data.dashboardData,
  //       lastFetched: state.data.lastFetched,
  //     },
  //   });
  // });
}

// ============================================================================
// Store Persistence (Optional)
// ============================================================================

/**
 * Save state to localStorage
 * This is a simple implementation - consider using redux-persist for production
 */
export const saveStateToLocalStorage = (state: RootState): void => {
  try {
    const serializedState = JSON.stringify({
      // Only save specific parts of the state
      data: {
        lastFetched: state.data.lastFetched,
        // Don't save actual data to avoid stale data issues
      },
    });
    localStorage.setItem('skygeni-dashboard-state', serializedState);
  } catch (error) {
    console.warn('⚠️ Could not save state to localStorage:', error);
  }
};

/**
 * Load state from localStorage
 */
export const loadStateFromLocalStorage = (): Partial<RootState> | undefined => {
  try {
    const serializedState = localStorage.getItem('skygeni-dashboard-state');
    if (serializedState === null) {
      return undefined;
    }
    return JSON.parse(serializedState);
  } catch (error) {
    console.warn('⚠️ Could not load state from localStorage:', error);
    return undefined;
  }
};

/**
 * Clear persisted state
 */
export const clearPersistedState = (): void => {
  try {
    localStorage.removeItem('skygeni-dashboard-state');
    console.log('✅ Persisted state cleared');
  } catch (error) {
    console.warn('⚠️ Could not clear persisted state:', error);
  }
};

// ============================================================================
// Store Event Listeners
// ============================================================================

/**
 * Listen for specific actions and perform side effects
 * Disabled to prevent infinite loops
 */
// store.subscribe(() => {
//   const state = store.getState();
//
//   // Auto-save state to localStorage when data is fetched
//   if (state.data.loading === 'succeeded' && state.data.dashboardData) {
//     saveStateToLocalStorage(state);
//   }
//
//   // Clear error after successful data fetch
//   if (state.data.loading === 'succeeded' && state.data.error) {
//     // This would be handled by the reducer, but we can add additional logic here
//   }
// });

// ============================================================================
// Export Default Store
// ============================================================================

export default store;
