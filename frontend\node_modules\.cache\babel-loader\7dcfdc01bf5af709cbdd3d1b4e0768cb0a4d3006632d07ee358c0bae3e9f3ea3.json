{"ast": null, "code": "// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\nexport default function (a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}", "map": {"version": 3, "names": ["a", "b", "c"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-polygon/src/cross.js"], "sourcesContent": ["// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\nexport default function(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,UAASA,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC/B,OAAO,CAACD,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAACC,CAAC,CAAC,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAC,CAAC,KAAKE,CAAC,CAAC,CAAC,CAAC,GAAGF,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}