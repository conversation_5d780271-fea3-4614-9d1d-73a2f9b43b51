{"ast": null, "code": "import creator from \"../creator.js\";\nexport default function (name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function () {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}", "map": {"version": 3, "names": ["creator", "name", "create", "select", "append<PERSON><PERSON><PERSON>", "apply", "arguments"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/append.js"], "sourcesContent": ["import creator from \"../creator.js\";\n\nexport default function(name) {\n  var create = typeof name === \"function\" ? name : creator(name);\n  return this.select(function() {\n    return this.appendChild(create.apply(this, arguments));\n  });\n}\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AAEnC,eAAe,UAASC,IAAI,EAAE;EAC5B,IAAIC,MAAM,GAAG,OAAOD,IAAI,KAAK,UAAU,GAAGA,IAAI,GAAGD,OAAO,CAACC,IAAI,CAAC;EAC9D,OAAO,IAAI,CAACE,MAAM,CAAC,YAAW;IAC5B,OAAO,IAAI,CAACC,WAAW,CAACF,MAAM,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACxD,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}