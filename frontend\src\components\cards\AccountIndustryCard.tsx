/**
 * AccountIndustryCard Component for SkyGeni Dashboard
 * Specialized card for account industry data visualization
 */

import React from 'react';
import DataCard from './DataCard';
import { useAccountIndustries } from '../../hooks/useData';
import { AccountIndustry } from '../../types';

interface AccountIndustryCardProps {
  className?: string;
  elevation?: number;
  data?: AccountIndustry[];
  loading?: boolean;
  error?: string;
  chartType?: 'bar' | 'doughnut';
}

const AccountIndustryCard: React.FC<AccountIndustryCardProps> = ({
  className,
  elevation = 2,
  data: overrideData,
  loading: overrideLoading,
  error: overrideError,
  chartType = 'bar',
}) => {
  const {
    accountIndustries,
    loading: hookLoading,
    error: hookError,
    isError,
    refetch,
  } = useAccountIndustries();

  const data = overrideData || accountIndustries;
  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading;
  const error = overrideError || (isError ? hookError : undefined);

  const processedData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data.map(industry => ({
      ...industry,
      name: industry.industry,
      label: industry.industry,
      value: industry.count,
      displayText: `${industry.industry} (${industry.count.toLocaleString()})`,
      revenueText: industry.revenue 
        ? `$${industry.revenue.toLocaleString()}` 
        : '',
    }));
  }, [data]);

  return (
    <DataCard
      title="Account Industries"
      data={processedData}
      chartType={chartType}
      loading={loading}
      error={error}
      className={className}
      elevation={elevation}
    />
  );
};

export const AccountIndustryBarCard: React.FC<Omit<AccountIndustryCardProps, 'chartType'>> = (props) => (
  <AccountIndustryCard {...props} chartType="bar" />
);

export const AccountIndustryDoughnutCard: React.FC<Omit<AccountIndustryCardProps, 'chartType'>> = (props) => (
  <AccountIndustryCard {...props} chartType="doughnut" />
);

export default AccountIndustryCard;
