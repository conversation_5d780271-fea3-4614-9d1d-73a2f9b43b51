/**
 * Layout Component for SkyGeni Dashboard
 * 
 * Main layout wrapper that provides:
 * - Consistent page structure
 * - Header and footer integration
 * - Responsive design
 * - Theme support
 * - Error boundaries
 * - Loading states
 */

import React from 'react';
import {
  Box,
  Container,
  CssBaseline,
  useTheme,
  alpha,
} from '@mui/material';
import Header from './Header';
import Footer from './Footer';
import { LayoutProps } from '../../types';
import { useData } from '../../hooks/useData';

// ============================================================================
// Layout Component
// ============================================================================

const Layout: React.FC<LayoutProps> = ({
  children,
  title,
  showHeader = true,
  showFooter = true,
}) => {
  const theme = useTheme();
  const { lastFetched } = useData({ autoFetch: false });

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: theme.palette.background.default,
        backgroundImage: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.02)} 0%, ${alpha(theme.palette.secondary.main, 0.02)} 100%)`,
      }}
    >
      {/* CSS Baseline for consistent styling */}
      <CssBaseline />

      {/* Header */}
      {showHeader && (
        <Header title={title} />
      )}

      {/* Main Content Area */}
      <Box
        component="main"
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          py: { xs: 2, md: 3 },
          minHeight: showHeader && showFooter ? 'calc(100vh - 128px)' : '100vh',
        }}
      >
        <Container
          maxWidth="xl"
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            px: { xs: 2, sm: 3 },
          }}
        >
          {children}
        </Container>
      </Box>

      {/* Footer */}
      {showFooter && (
        <Footer 
          showLastUpdated={true}
          lastUpdated={lastFetched || undefined}
        />
      )}
    </Box>
  );
};

// ============================================================================
// Layout Variants
// ============================================================================

/**
 * Full-width layout without container constraints
 */
export const FullWidthLayout: React.FC<LayoutProps> = ({
  children,
  title,
  showHeader = true,
  showFooter = true,
}) => {
  const theme = useTheme();
  const { lastFetched } = useData({ autoFetch: false });

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: theme.palette.background.default,
      }}
    >
      <CssBaseline />

      {showHeader && <Header title={title} />}

      <Box
        component="main"
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          py: { xs: 2, md: 3 },
          px: { xs: 2, sm: 3 },
        }}
      >
        {children}
      </Box>

      {showFooter && (
        <Footer 
          showLastUpdated={true}
          lastUpdated={lastFetched || undefined}
        />
      )}
    </Box>
  );
};

/**
 * Minimal layout without header and footer
 */
export const MinimalLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: theme.palette.background.default,
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <CssBaseline />
      <Container
        maxWidth="xl"
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          py: 3,
        }}
      >
        {children}
      </Container>
    </Box>
  );
};

/**
 * Centered layout for forms and simple pages
 */
export const CenteredLayout: React.FC<{ children: React.ReactNode; maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' }> = ({ 
  children, 
  maxWidth = 'md' 
}) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        backgroundColor: theme.palette.background.default,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        py: 3,
      }}
    >
      <CssBaseline />
      <Container
        maxWidth={maxWidth}
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        {children}
      </Container>
    </Box>
  );
};

export default Layout;
