{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$();\n/**\n * useData Hook for SkyGeni Dashboard\n * \n * Custom hook that provides:\n * - Easy access to dashboard data\n * - Loading and error states\n * - Data fetching functions\n * - Automatic data refresh\n * - Cache management\n * - Type-safe data access\n */\n\nimport { useEffect, useCallback, useMemo } from 'react';\nimport { useAppDispatch, useAppSelector } from '../redux/store';\nimport { fetchDashboardData, fetchCustomerTypes, fetchAccountIndustries, fetchTeams, fetchACVRanges, refreshAllData, clearData, clearError, setSelectedDataType, setDataFilter, resetFilters } from '../redux/slices/dataSlice';\n\n// ============================================================================\n// Hook Configuration\n// ============================================================================\n\nconst DEFAULT_OPTIONS = {\n  autoFetch: true,\n  refreshInterval: 0,\n  // Disabled by default\n  useCache: true,\n  dataType: undefined\n};\n\n// ============================================================================\n// Main Hook\n// ============================================================================\n\n/**\n * Custom hook for managing dashboard data\n */\nexport const useData = (options = {}) => {\n  _s();\n  const opts = {\n    ...DEFAULT_OPTIONS,\n    ...options\n  };\n  const dispatch = useAppDispatch();\n\n  // ========================================================================\n  // Selectors\n  // ========================================================================\n\n  const dashboardData = useAppSelector(state => state.data.dashboardData);\n  const customerTypes = useAppSelector(state => state.data.customerTypes);\n  const accountIndustries = useAppSelector(state => state.data.accountIndustries);\n  const teams = useAppSelector(state => state.data.teams);\n  const acvRanges = useAppSelector(state => state.data.acvRanges);\n  const loading = useAppSelector(state => state.data.loading);\n  const error = useAppSelector(state => state.data.error);\n  const lastFetched = useAppSelector(state => state.data.lastFetched);\n  const cacheExpiry = useAppSelector(state => state.data.cacheExpiry);\n  const filters = useAppSelector(state => state.data.filters);\n  const selectedDataType = useAppSelector(state => state.data.selectedDataType);\n\n  // ========================================================================\n  // Computed Values\n  // ========================================================================\n\n  const isLoading = useMemo(() => loading === 'pending', [loading]);\n  const isError = useMemo(() => loading === 'failed' || !!error, [loading, error]);\n  const isSuccess = useMemo(() => loading === 'succeeded' && !!dashboardData, [loading, dashboardData]);\n  const isEmpty = useMemo(() => !dashboardData && !isLoading, [dashboardData, isLoading]);\n\n  /**\n   * Check if cache is still valid\n   */\n  const isCacheValid = useMemo(() => {\n    if (!opts.useCache || !lastFetched || !cacheExpiry) return false;\n    return new Date() < new Date(cacheExpiry);\n  }, [opts.useCache, lastFetched, cacheExpiry]);\n\n  /**\n   * Check if data needs refresh\n   */\n  const needsRefresh = useMemo(() => {\n    if (!opts.useCache) return true;\n    if (!dashboardData) return true;\n    if (!isCacheValid) return true;\n    return false;\n  }, [opts.useCache, dashboardData, isCacheValid]);\n\n  // ========================================================================\n  // Data Fetching Functions\n  // ========================================================================\n\n  /**\n   * Fetch all dashboard data\n   */\n  const fetchData = useCallback(async (forceRefresh = false) => {\n    if (!forceRefresh && !needsRefresh && opts.useCache) {\n      console.log('📦 Using cached data');\n      return;\n    }\n    try {\n      await dispatch(fetchDashboardData({\n        forceRefresh\n      })).unwrap();\n    } catch (error) {\n      console.error('❌ Failed to fetch dashboard data:', error);\n      throw error;\n    }\n  }, [dispatch, opts.useCache]); // Remove needsRefresh from dependencies to prevent infinite loop\n\n  /**\n   * Fetch specific data type\n   */\n  const fetchSpecificData = useCallback(async dataType => {\n    try {\n      switch (dataType) {\n        case 'customerTypes':\n          await dispatch(fetchCustomerTypes()).unwrap();\n          break;\n        case 'accountIndustries':\n          await dispatch(fetchAccountIndustries()).unwrap();\n          break;\n        case 'teams':\n          await dispatch(fetchTeams()).unwrap();\n          break;\n        case 'acvRanges':\n          await dispatch(fetchACVRanges()).unwrap();\n          break;\n        default:\n          throw new Error(`Unknown data type: ${dataType}`);\n      }\n    } catch (error) {\n      console.error(`❌ Failed to fetch ${dataType}:`, error);\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\n   * Refresh all data\n   */\n  const refetch = useCallback(async () => {\n    try {\n      await dispatch(refreshAllData()).unwrap();\n    } catch (error) {\n      console.error('❌ Failed to refresh data:', error);\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\n   * Clear all data\n   */\n  const clearAllData = useCallback(() => {\n    dispatch(clearData());\n  }, [dispatch]);\n\n  /**\n   * Clear error state\n   */\n  const clearErrorState = useCallback(() => {\n    dispatch(clearError());\n  }, [dispatch]);\n\n  // ========================================================================\n  // Filter and Selection Functions\n  // ========================================================================\n\n  /**\n   * Update data filters\n   */\n  const updateFilter = useCallback((filterType, value) => {\n    dispatch(setDataFilter({\n      filterType,\n      value\n    }));\n  }, [dispatch]);\n\n  /**\n   * Reset all filters\n   */\n  const resetAllFilters = useCallback(() => {\n    dispatch(resetFilters());\n  }, [dispatch]);\n\n  /**\n   * Set selected data type\n   */\n  const selectDataType = useCallback(dataType => {\n    dispatch(setSelectedDataType(dataType));\n  }, [dispatch]);\n\n  // ========================================================================\n  // Filtered Data\n  // ========================================================================\n\n  /**\n   * Get filtered data based on current filters\n   */\n  const getFilteredData = useCallback((data, searchFields = ['name', 'type', 'industry']) => {\n    let filtered = [...data];\n\n    // Apply search filter\n    if (filters.searchTerm) {\n      const searchTerm = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => searchFields.some(field => {\n        var _item$field;\n        return (_item$field = item[field]) === null || _item$field === void 0 ? void 0 : _item$field.toString().toLowerCase().includes(searchTerm);\n      }));\n    }\n\n    // Apply sorting\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        const aVal = a[filters.sortBy];\n        const bVal = b[filters.sortBy];\n        if (typeof aVal === 'string' && typeof bVal === 'string') {\n          return filters.sortOrder === 'asc' ? aVal.localeCompare(bVal) : bVal.localeCompare(aVal);\n        }\n        if (typeof aVal === 'number' && typeof bVal === 'number') {\n          return filters.sortOrder === 'asc' ? aVal - bVal : bVal - aVal;\n        }\n        return 0;\n      });\n    }\n\n    // Filter empty values if needed\n    if (!filters.showEmpty) {\n      filtered = filtered.filter(item => Object.values(item).some(value => value !== null && value !== undefined && value !== ''));\n    }\n    return filtered;\n  }, [filters]);\n\n  // ========================================================================\n  // Effects\n  // ========================================================================\n\n  /**\n   * Auto-fetch data on mount\n   */\n  useEffect(() => {\n    if (opts.autoFetch && needsRefresh) {\n      fetchData();\n    }\n  }, [opts.autoFetch]); // Remove fetchData and needsRefresh to prevent infinite loop\n\n  /**\n   * Set up refresh interval\n   */\n  useEffect(() => {\n    if (!opts.refreshInterval || opts.refreshInterval <= 0) return;\n    const interval = setInterval(() => {\n      if (!isLoading) {\n        console.log('🔄 Auto-refreshing data...');\n        fetchData(true);\n      }\n    }, opts.refreshInterval);\n    return () => clearInterval(interval);\n  }, [opts.refreshInterval, isLoading, fetchData]);\n\n  /**\n   * Focus on specific data type if provided\n   */\n  useEffect(() => {\n    if (opts.dataType && opts.dataType !== selectedDataType) {\n      selectDataType(opts.dataType);\n    }\n  }, [opts.dataType, selectedDataType, selectDataType]);\n\n  // ========================================================================\n  // Return Hook Interface\n  // ========================================================================\n\n  return {\n    // Data\n    dashboardData,\n    customerTypes: getFilteredData(customerTypes, ['type']),\n    accountIndustries: getFilteredData(accountIndustries, ['industry']),\n    teams: getFilteredData(teams, ['name', 'department']),\n    acvRanges: getFilteredData(acvRanges, ['range']),\n    // State\n    loading,\n    error,\n    isLoading,\n    isError,\n    isSuccess,\n    isEmpty,\n    lastFetched,\n    isCacheValid,\n    needsRefresh,\n    // Actions\n    refetch,\n    fetchData,\n    fetchSpecificData,\n    clearAllData,\n    clearErrorState,\n    // Filters and Selection\n    filters,\n    selectedDataType,\n    updateFilter,\n    resetAllFilters,\n    selectDataType,\n    getFilteredData\n  };\n};\n\n// ============================================================================\n// Specialized Hooks\n// ============================================================================\n\n/**\n * Hook specifically for customer types data\n */\n_s(useData, \"ofprq3MUJ1zVUarzbEXCmMMUIgQ=\", false, function () {\n  return [useAppDispatch, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector, useAppSelector];\n});\nexport const useCustomerTypes = () => {\n  _s2();\n  return useData({\n    dataType: 'customerTypes'\n  });\n};\n\n/**\n * Hook specifically for account industries data\n */\n_s2(useCustomerTypes, \"7nuxJLEyQO4rIsd+AJDJHMkCeg4=\", false, function () {\n  return [useData];\n});\nexport const useAccountIndustries = () => {\n  _s3();\n  return useData({\n    dataType: 'accountIndustries'\n  });\n};\n\n/**\n * Hook specifically for teams data\n */\n_s3(useAccountIndustries, \"7nuxJLEyQO4rIsd+AJDJHMkCeg4=\", false, function () {\n  return [useData];\n});\nexport const useTeams = () => {\n  _s4();\n  return useData({\n    dataType: 'teams'\n  });\n};\n\n/**\n * Hook specifically for ACV ranges data\n */\n_s4(useTeams, \"7nuxJLEyQO4rIsd+AJDJHMkCeg4=\", false, function () {\n  return [useData];\n});\nexport const useACVRanges = () => {\n  _s5();\n  return useData({\n    dataType: 'acvRanges'\n  });\n};\n\n/**\n * Hook with auto-refresh enabled\n */\n_s5(useACVRanges, \"7nuxJLEyQO4rIsd+AJDJHMkCeg4=\", false, function () {\n  return [useData];\n});\nexport const useDataWithRefresh = (intervalMs = 30000) => {\n  _s6();\n  return useData({\n    refreshInterval: intervalMs\n  });\n};\n_s6(useDataWithRefresh, \"7nuxJLEyQO4rIsd+AJDJHMkCeg4=\", false, function () {\n  return [useData];\n});\nexport default useData;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useMemo", "useAppDispatch", "useAppSelector", "fetchDashboardData", "fetchCustomerTypes", "fetchAccountIndustries", "fetchTeams", "fetchACVRanges", "refreshAllData", "clearData", "clearError", "setSelectedDataType", "setData<PERSON><PERSON><PERSON>", "resetFilters", "DEFAULT_OPTIONS", "autoFetch", "refreshInterval", "useCache", "dataType", "undefined", "useData", "options", "_s", "opts", "dispatch", "dashboardData", "state", "data", "customerTypes", "accountIndustries", "teams", "acvRanges", "loading", "error", "lastFetched", "cacheExpiry", "filters", "selectedDataType", "isLoading", "isError", "isSuccess", "isEmpty", "isCache<PERSON><PERSON>d", "Date", "needsRefresh", "fetchData", "forceRefresh", "console", "log", "unwrap", "fetchSpecificData", "Error", "refetch", "clearAllData", "clearErrorState", "updateFilter", "filterType", "value", "resetAllFilters", "selectDataType", "getFilteredData", "searchFields", "filtered", "searchTerm", "toLowerCase", "filter", "item", "some", "field", "_item$field", "toString", "includes", "sortBy", "sort", "a", "b", "aVal", "bVal", "sortOrder", "localeCompare", "showEmpty", "Object", "values", "interval", "setInterval", "clearInterval", "useCustomerTypes", "_s2", "useAccountIndustries", "_s3", "useTeams", "_s4", "useACVRanges", "_s5", "useDataWithRefresh", "intervalMs", "_s6"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/hooks/useData.ts"], "sourcesContent": ["/**\n * useData Hook for SkyGeni Dashboard\n * \n * Custom hook that provides:\n * - Easy access to dashboard data\n * - Loading and error states\n * - Data fetching functions\n * - Automatic data refresh\n * - Cache management\n * - Type-safe data access\n */\n\nimport { useEffect, useCallback, useMemo } from 'react';\nimport { useAppDispatch, useAppSelector } from '../redux/store';\nimport {\n  fetchDashboardData,\n  fetchCustomerTypes,\n  fetchAccountIndustries,\n  fetchTeams,\n  fetchACVRanges,\n  refreshAllData,\n  clearData,\n  clearError,\n  setSelectedDataType,\n  setDataFilter,\n  resetFilters,\n} from '../redux/slices/dataSlice';\nimport {\n  UseDataReturn,\n  DataType,\n  DataFilters,\n  LoadingState,\n  DashboardData,\n  CustomerType,\n  AccountIndustry,\n  Team,\n  ACVRange,\n} from '../types';\n\n// ============================================================================\n// Hook Configuration\n// ============================================================================\n\ninterface UseDataOptions {\n  /**\n   * Automatically fetch data on mount\n   */\n  autoFetch?: boolean;\n  \n  /**\n   * Refresh interval in milliseconds (0 to disable)\n   */\n  refreshInterval?: number;\n  \n  /**\n   * Enable cache (use cached data if available)\n   */\n  useCache?: boolean;\n  \n  /**\n   * Specific data type to focus on\n   */\n  dataType?: DataType;\n}\n\nconst DEFAULT_OPTIONS: UseDataOptions = {\n  autoFetch: true,\n  refreshInterval: 0, // Disabled by default\n  useCache: true,\n  dataType: undefined,\n};\n\n// ============================================================================\n// Main Hook\n// ============================================================================\n\n/**\n * Custom hook for managing dashboard data\n */\nexport const useData = (options: UseDataOptions = {}): UseDataReturn => {\n  const opts = { ...DEFAULT_OPTIONS, ...options };\n  const dispatch = useAppDispatch();\n  \n  // ========================================================================\n  // Selectors\n  // ========================================================================\n  \n  const dashboardData = useAppSelector((state) => state.data.dashboardData);\n  const customerTypes = useAppSelector((state) => state.data.customerTypes);\n  const accountIndustries = useAppSelector((state) => state.data.accountIndustries);\n  const teams = useAppSelector((state) => state.data.teams);\n  const acvRanges = useAppSelector((state) => state.data.acvRanges);\n  const loading = useAppSelector((state) => state.data.loading);\n  const error = useAppSelector((state) => state.data.error);\n  const lastFetched = useAppSelector((state) => state.data.lastFetched);\n  const cacheExpiry = useAppSelector((state) => state.data.cacheExpiry);\n  const filters = useAppSelector((state) => state.data.filters);\n  const selectedDataType = useAppSelector((state) => state.data.selectedDataType);\n\n  // ========================================================================\n  // Computed Values\n  // ========================================================================\n  \n  const isLoading = useMemo(() => loading === 'pending', [loading]);\n  const isError = useMemo(() => loading === 'failed' || !!error, [loading, error]);\n  const isSuccess = useMemo(() => loading === 'succeeded' && !!dashboardData, [loading, dashboardData]);\n  const isEmpty = useMemo(() => !dashboardData && !isLoading, [dashboardData, isLoading]);\n  \n  /**\n   * Check if cache is still valid\n   */\n  const isCacheValid = useMemo(() => {\n    if (!opts.useCache || !lastFetched || !cacheExpiry) return false;\n    return new Date() < new Date(cacheExpiry);\n  }, [opts.useCache, lastFetched, cacheExpiry]);\n\n  /**\n   * Check if data needs refresh\n   */\n  const needsRefresh = useMemo(() => {\n    if (!opts.useCache) return true;\n    if (!dashboardData) return true;\n    if (!isCacheValid) return true;\n    return false;\n  }, [opts.useCache, dashboardData, isCacheValid]);\n\n  // ========================================================================\n  // Data Fetching Functions\n  // ========================================================================\n  \n  /**\n   * Fetch all dashboard data\n   */\n  const fetchData = useCallback(async (forceRefresh = false) => {\n    if (!forceRefresh && !needsRefresh && opts.useCache) {\n      console.log('📦 Using cached data');\n      return;\n    }\n\n    try {\n      await dispatch(fetchDashboardData({ forceRefresh })).unwrap();\n    } catch (error) {\n      console.error('❌ Failed to fetch dashboard data:', error);\n      throw error;\n    }\n  }, [dispatch, opts.useCache]); // Remove needsRefresh from dependencies to prevent infinite loop\n\n  /**\n   * Fetch specific data type\n   */\n  const fetchSpecificData = useCallback(async (dataType: DataType) => {\n    try {\n      switch (dataType) {\n        case 'customerTypes':\n          await dispatch(fetchCustomerTypes()).unwrap();\n          break;\n        case 'accountIndustries':\n          await dispatch(fetchAccountIndustries()).unwrap();\n          break;\n        case 'teams':\n          await dispatch(fetchTeams()).unwrap();\n          break;\n        case 'acvRanges':\n          await dispatch(fetchACVRanges()).unwrap();\n          break;\n        default:\n          throw new Error(`Unknown data type: ${dataType}`);\n      }\n    } catch (error) {\n      console.error(`❌ Failed to fetch ${dataType}:`, error);\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\n   * Refresh all data\n   */\n  const refetch = useCallback(async () => {\n    try {\n      await dispatch(refreshAllData()).unwrap();\n    } catch (error) {\n      console.error('❌ Failed to refresh data:', error);\n      throw error;\n    }\n  }, [dispatch]);\n\n  /**\n   * Clear all data\n   */\n  const clearAllData = useCallback(() => {\n    dispatch(clearData());\n  }, [dispatch]);\n\n  /**\n   * Clear error state\n   */\n  const clearErrorState = useCallback(() => {\n    dispatch(clearError());\n  }, [dispatch]);\n\n  // ========================================================================\n  // Filter and Selection Functions\n  // ========================================================================\n  \n  /**\n   * Update data filters\n   */\n  const updateFilter = useCallback((filterType: keyof DataFilters, value: any) => {\n    dispatch(setDataFilter({ filterType, value }));\n  }, [dispatch]);\n\n  /**\n   * Reset all filters\n   */\n  const resetAllFilters = useCallback(() => {\n    dispatch(resetFilters());\n  }, [dispatch]);\n\n  /**\n   * Set selected data type\n   */\n  const selectDataType = useCallback((dataType: DataType | null) => {\n    dispatch(setSelectedDataType(dataType));\n  }, [dispatch]);\n\n  // ========================================================================\n  // Filtered Data\n  // ========================================================================\n  \n  /**\n   * Get filtered data based on current filters\n   */\n  const getFilteredData = useCallback((data: any[], searchFields: string[] = ['name', 'type', 'industry']) => {\n    let filtered = [...data];\n    \n    // Apply search filter\n    if (filters.searchTerm) {\n      const searchTerm = filters.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        searchFields.some(field =>\n          item[field]?.toString().toLowerCase().includes(searchTerm)\n        )\n      );\n    }\n    \n    // Apply sorting\n    if (filters.sortBy) {\n      filtered.sort((a, b) => {\n        const aVal = a[filters.sortBy];\n        const bVal = b[filters.sortBy];\n        \n        if (typeof aVal === 'string' && typeof bVal === 'string') {\n          return filters.sortOrder === 'asc' \n            ? aVal.localeCompare(bVal)\n            : bVal.localeCompare(aVal);\n        }\n        \n        if (typeof aVal === 'number' && typeof bVal === 'number') {\n          return filters.sortOrder === 'asc' ? aVal - bVal : bVal - aVal;\n        }\n        \n        return 0;\n      });\n    }\n    \n    // Filter empty values if needed\n    if (!filters.showEmpty) {\n      filtered = filtered.filter(item => \n        Object.values(item).some(value => \n          value !== null && value !== undefined && value !== ''\n        )\n      );\n    }\n    \n    return filtered;\n  }, [filters]);\n\n  // ========================================================================\n  // Effects\n  // ========================================================================\n  \n  /**\n   * Auto-fetch data on mount\n   */\n  useEffect(() => {\n    if (opts.autoFetch && needsRefresh) {\n      fetchData();\n    }\n  }, [opts.autoFetch]); // Remove fetchData and needsRefresh to prevent infinite loop\n\n  /**\n   * Set up refresh interval\n   */\n  useEffect(() => {\n    if (!opts.refreshInterval || opts.refreshInterval <= 0) return;\n    \n    const interval = setInterval(() => {\n      if (!isLoading) {\n        console.log('🔄 Auto-refreshing data...');\n        fetchData(true);\n      }\n    }, opts.refreshInterval);\n    \n    return () => clearInterval(interval);\n  }, [opts.refreshInterval, isLoading, fetchData]);\n\n  /**\n   * Focus on specific data type if provided\n   */\n  useEffect(() => {\n    if (opts.dataType && opts.dataType !== selectedDataType) {\n      selectDataType(opts.dataType);\n    }\n  }, [opts.dataType, selectedDataType, selectDataType]);\n\n  // ========================================================================\n  // Return Hook Interface\n  // ========================================================================\n  \n  return {\n    // Data\n    dashboardData,\n    customerTypes: getFilteredData(customerTypes, ['type']),\n    accountIndustries: getFilteredData(accountIndustries, ['industry']),\n    teams: getFilteredData(teams, ['name', 'department']),\n    acvRanges: getFilteredData(acvRanges, ['range']),\n    \n    // State\n    loading,\n    error,\n    isLoading,\n    isError,\n    isSuccess,\n    isEmpty,\n    lastFetched,\n    isCacheValid,\n    needsRefresh,\n    \n    // Actions\n    refetch,\n    fetchData,\n    fetchSpecificData,\n    clearAllData,\n    clearErrorState,\n    \n    // Filters and Selection\n    filters,\n    selectedDataType,\n    updateFilter,\n    resetAllFilters,\n    selectDataType,\n    getFilteredData,\n  };\n};\n\n// ============================================================================\n// Specialized Hooks\n// ============================================================================\n\n/**\n * Hook specifically for customer types data\n */\nexport const useCustomerTypes = () => {\n  return useData({ dataType: 'customerTypes' });\n};\n\n/**\n * Hook specifically for account industries data\n */\nexport const useAccountIndustries = () => {\n  return useData({ dataType: 'accountIndustries' });\n};\n\n/**\n * Hook specifically for teams data\n */\nexport const useTeams = () => {\n  return useData({ dataType: 'teams' });\n};\n\n/**\n * Hook specifically for ACV ranges data\n */\nexport const useACVRanges = () => {\n  return useData({ dataType: 'acvRanges' });\n};\n\n/**\n * Hook with auto-refresh enabled\n */\nexport const useDataWithRefresh = (intervalMs: number = 30000) => {\n  return useData({ refreshInterval: intervalMs });\n};\n\nexport default useData;\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACvD,SAASC,cAAc,EAAEC,cAAc,QAAQ,gBAAgB;AAC/D,SACEC,kBAAkB,EAClBC,kBAAkB,EAClBC,sBAAsB,EACtBC,UAAU,EACVC,cAAc,EACdC,cAAc,EACdC,SAAS,EACTC,UAAU,EACVC,mBAAmB,EACnBC,aAAa,EACbC,YAAY,QACP,2BAA2B;;AAalC;AACA;AACA;;AAwBA,MAAMC,eAA+B,GAAG;EACtCC,SAAS,EAAE,IAAI;EACfC,eAAe,EAAE,CAAC;EAAE;EACpBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAEC;AACZ,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMC,OAAO,GAAGA,CAACC,OAAuB,GAAG,CAAC,CAAC,KAAoB;EAAAC,EAAA;EACtE,MAAMC,IAAI,GAAG;IAAE,GAAGT,eAAe;IAAE,GAAGO;EAAQ,CAAC;EAC/C,MAAMG,QAAQ,GAAGvB,cAAc,CAAC,CAAC;;EAEjC;EACA;EACA;;EAEA,MAAMwB,aAAa,GAAGvB,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACF,aAAa,CAAC;EACzE,MAAMG,aAAa,GAAG1B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACC,aAAa,CAAC;EACzE,MAAMC,iBAAiB,GAAG3B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACE,iBAAiB,CAAC;EACjF,MAAMC,KAAK,GAAG5B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACG,KAAK,CAAC;EACzD,MAAMC,SAAS,GAAG7B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACI,SAAS,CAAC;EACjE,MAAMC,OAAO,GAAG9B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACK,OAAO,CAAC;EAC7D,MAAMC,KAAK,GAAG/B,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACM,KAAK,CAAC;EACzD,MAAMC,WAAW,GAAGhC,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACO,WAAW,CAAC;EACrE,MAAMC,WAAW,GAAGjC,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACQ,WAAW,CAAC;EACrE,MAAMC,OAAO,GAAGlC,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACS,OAAO,CAAC;EAC7D,MAAMC,gBAAgB,GAAGnC,cAAc,CAAEwB,KAAK,IAAKA,KAAK,CAACC,IAAI,CAACU,gBAAgB,CAAC;;EAE/E;EACA;EACA;;EAEA,MAAMC,SAAS,GAAGtC,OAAO,CAAC,MAAMgC,OAAO,KAAK,SAAS,EAAE,CAACA,OAAO,CAAC,CAAC;EACjE,MAAMO,OAAO,GAAGvC,OAAO,CAAC,MAAMgC,OAAO,KAAK,QAAQ,IAAI,CAAC,CAACC,KAAK,EAAE,CAACD,OAAO,EAAEC,KAAK,CAAC,CAAC;EAChF,MAAMO,SAAS,GAAGxC,OAAO,CAAC,MAAMgC,OAAO,KAAK,WAAW,IAAI,CAAC,CAACP,aAAa,EAAE,CAACO,OAAO,EAAEP,aAAa,CAAC,CAAC;EACrG,MAAMgB,OAAO,GAAGzC,OAAO,CAAC,MAAM,CAACyB,aAAa,IAAI,CAACa,SAAS,EAAE,CAACb,aAAa,EAAEa,SAAS,CAAC,CAAC;;EAEvF;AACF;AACA;EACE,MAAMI,YAAY,GAAG1C,OAAO,CAAC,MAAM;IACjC,IAAI,CAACuB,IAAI,CAACN,QAAQ,IAAI,CAACiB,WAAW,IAAI,CAACC,WAAW,EAAE,OAAO,KAAK;IAChE,OAAO,IAAIQ,IAAI,CAAC,CAAC,GAAG,IAAIA,IAAI,CAACR,WAAW,CAAC;EAC3C,CAAC,EAAE,CAACZ,IAAI,CAACN,QAAQ,EAAEiB,WAAW,EAAEC,WAAW,CAAC,CAAC;;EAE7C;AACF;AACA;EACE,MAAMS,YAAY,GAAG5C,OAAO,CAAC,MAAM;IACjC,IAAI,CAACuB,IAAI,CAACN,QAAQ,EAAE,OAAO,IAAI;IAC/B,IAAI,CAACQ,aAAa,EAAE,OAAO,IAAI;IAC/B,IAAI,CAACiB,YAAY,EAAE,OAAO,IAAI;IAC9B,OAAO,KAAK;EACd,CAAC,EAAE,CAACnB,IAAI,CAACN,QAAQ,EAAEQ,aAAa,EAAEiB,YAAY,CAAC,CAAC;;EAEhD;EACA;EACA;;EAEA;AACF;AACA;EACE,MAAMG,SAAS,GAAG9C,WAAW,CAAC,OAAO+C,YAAY,GAAG,KAAK,KAAK;IAC5D,IAAI,CAACA,YAAY,IAAI,CAACF,YAAY,IAAIrB,IAAI,CAACN,QAAQ,EAAE;MACnD8B,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACnC;IACF;IAEA,IAAI;MACF,MAAMxB,QAAQ,CAACrB,kBAAkB,CAAC;QAAE2C;MAAa,CAAC,CAAC,CAAC,CAACG,MAAM,CAAC,CAAC;IAC/D,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MACzD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACT,QAAQ,EAAED,IAAI,CAACN,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE/B;AACF;AACA;EACE,MAAMiC,iBAAiB,GAAGnD,WAAW,CAAC,MAAOmB,QAAkB,IAAK;IAClE,IAAI;MACF,QAAQA,QAAQ;QACd,KAAK,eAAe;UAClB,MAAMM,QAAQ,CAACpB,kBAAkB,CAAC,CAAC,CAAC,CAAC6C,MAAM,CAAC,CAAC;UAC7C;QACF,KAAK,mBAAmB;UACtB,MAAMzB,QAAQ,CAACnB,sBAAsB,CAAC,CAAC,CAAC,CAAC4C,MAAM,CAAC,CAAC;UACjD;QACF,KAAK,OAAO;UACV,MAAMzB,QAAQ,CAAClB,UAAU,CAAC,CAAC,CAAC,CAAC2C,MAAM,CAAC,CAAC;UACrC;QACF,KAAK,WAAW;UACd,MAAMzB,QAAQ,CAACjB,cAAc,CAAC,CAAC,CAAC,CAAC0C,MAAM,CAAC,CAAC;UACzC;QACF;UACE,MAAM,IAAIE,KAAK,CAAC,sBAAsBjC,QAAQ,EAAE,CAAC;MACrD;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,qBAAqBf,QAAQ,GAAG,EAAEe,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM4B,OAAO,GAAGrD,WAAW,CAAC,YAAY;IACtC,IAAI;MACF,MAAMyB,QAAQ,CAAChB,cAAc,CAAC,CAAC,CAAC,CAACyC,MAAM,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdc,OAAO,CAACd,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM6B,YAAY,GAAGtD,WAAW,CAAC,MAAM;IACrCyB,QAAQ,CAACf,SAAS,CAAC,CAAC,CAAC;EACvB,CAAC,EAAE,CAACe,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAM8B,eAAe,GAAGvD,WAAW,CAAC,MAAM;IACxCyB,QAAQ,CAACd,UAAU,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACc,QAAQ,CAAC,CAAC;;EAEd;EACA;EACA;;EAEA;AACF;AACA;EACE,MAAM+B,YAAY,GAAGxD,WAAW,CAAC,CAACyD,UAA6B,EAAEC,KAAU,KAAK;IAC9EjC,QAAQ,CAACZ,aAAa,CAAC;MAAE4C,UAAU;MAAEC;IAAM,CAAC,CAAC,CAAC;EAChD,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMkC,eAAe,GAAG3D,WAAW,CAAC,MAAM;IACxCyB,QAAQ,CAACX,YAAY,CAAC,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACW,QAAQ,CAAC,CAAC;;EAEd;AACF;AACA;EACE,MAAMmC,cAAc,GAAG5D,WAAW,CAAEmB,QAAyB,IAAK;IAChEM,QAAQ,CAACb,mBAAmB,CAACO,QAAQ,CAAC,CAAC;EACzC,CAAC,EAAE,CAACM,QAAQ,CAAC,CAAC;;EAEd;EACA;EACA;;EAEA;AACF;AACA;EACE,MAAMoC,eAAe,GAAG7D,WAAW,CAAC,CAAC4B,IAAW,EAAEkC,YAAsB,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,UAAU,CAAC,KAAK;IAC1G,IAAIC,QAAQ,GAAG,CAAC,GAAGnC,IAAI,CAAC;;IAExB;IACA,IAAIS,OAAO,CAAC2B,UAAU,EAAE;MACtB,MAAMA,UAAU,GAAG3B,OAAO,CAAC2B,UAAU,CAACC,WAAW,CAAC,CAAC;MACnDF,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,IAAI,IAC7BL,YAAY,CAACM,IAAI,CAACC,KAAK;QAAA,IAAAC,WAAA;QAAA,QAAAA,WAAA,GACrBH,IAAI,CAACE,KAAK,CAAC,cAAAC,WAAA,uBAAXA,WAAA,CAAaC,QAAQ,CAAC,CAAC,CAACN,WAAW,CAAC,CAAC,CAACO,QAAQ,CAACR,UAAU,CAAC;MAAA,CAC5D,CACF,CAAC;IACH;;IAEA;IACA,IAAI3B,OAAO,CAACoC,MAAM,EAAE;MAClBV,QAAQ,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QACtB,MAAMC,IAAI,GAAGF,CAAC,CAACtC,OAAO,CAACoC,MAAM,CAAC;QAC9B,MAAMK,IAAI,GAAGF,CAAC,CAACvC,OAAO,CAACoC,MAAM,CAAC;QAE9B,IAAI,OAAOI,IAAI,KAAK,QAAQ,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;UACxD,OAAOzC,OAAO,CAAC0C,SAAS,KAAK,KAAK,GAC9BF,IAAI,CAACG,aAAa,CAACF,IAAI,CAAC,GACxBA,IAAI,CAACE,aAAa,CAACH,IAAI,CAAC;QAC9B;QAEA,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;UACxD,OAAOzC,OAAO,CAAC0C,SAAS,KAAK,KAAK,GAAGF,IAAI,GAAGC,IAAI,GAAGA,IAAI,GAAGD,IAAI;QAChE;QAEA,OAAO,CAAC;MACV,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAACxC,OAAO,CAAC4C,SAAS,EAAE;MACtBlB,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACC,IAAI,IAC7Be,MAAM,CAACC,MAAM,CAAChB,IAAI,CAAC,CAACC,IAAI,CAACV,KAAK,IAC5BA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKtC,SAAS,IAAIsC,KAAK,KAAK,EACrD,CACF,CAAC;IACH;IAEA,OAAOK,QAAQ;EACjB,CAAC,EAAE,CAAC1B,OAAO,CAAC,CAAC;;EAEb;EACA;EACA;;EAEA;AACF;AACA;EACEtC,SAAS,CAAC,MAAM;IACd,IAAIyB,IAAI,CAACR,SAAS,IAAI6B,YAAY,EAAE;MAClCC,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACtB,IAAI,CAACR,SAAS,CAAC,CAAC,CAAC,CAAC;;EAEtB;AACF;AACA;EACEjB,SAAS,CAAC,MAAM;IACd,IAAI,CAACyB,IAAI,CAACP,eAAe,IAAIO,IAAI,CAACP,eAAe,IAAI,CAAC,EAAE;IAExD,MAAMmE,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjC,IAAI,CAAC9C,SAAS,EAAE;QACdS,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzCH,SAAS,CAAC,IAAI,CAAC;MACjB;IACF,CAAC,EAAEtB,IAAI,CAACP,eAAe,CAAC;IAExB,OAAO,MAAMqE,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAAC5D,IAAI,CAACP,eAAe,EAAEsB,SAAS,EAAEO,SAAS,CAAC,CAAC;;EAEhD;AACF;AACA;EACE/C,SAAS,CAAC,MAAM;IACd,IAAIyB,IAAI,CAACL,QAAQ,IAAIK,IAAI,CAACL,QAAQ,KAAKmB,gBAAgB,EAAE;MACvDsB,cAAc,CAACpC,IAAI,CAACL,QAAQ,CAAC;IAC/B;EACF,CAAC,EAAE,CAACK,IAAI,CAACL,QAAQ,EAAEmB,gBAAgB,EAAEsB,cAAc,CAAC,CAAC;;EAErD;EACA;EACA;;EAEA,OAAO;IACL;IACAlC,aAAa;IACbG,aAAa,EAAEgC,eAAe,CAAChC,aAAa,EAAE,CAAC,MAAM,CAAC,CAAC;IACvDC,iBAAiB,EAAE+B,eAAe,CAAC/B,iBAAiB,EAAE,CAAC,UAAU,CAAC,CAAC;IACnEC,KAAK,EAAE8B,eAAe,CAAC9B,KAAK,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACrDC,SAAS,EAAE6B,eAAe,CAAC7B,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC;IAEhD;IACAC,OAAO;IACPC,KAAK;IACLK,SAAS;IACTC,OAAO;IACPC,SAAS;IACTC,OAAO;IACPP,WAAW;IACXQ,YAAY;IACZE,YAAY;IAEZ;IACAQ,OAAO;IACPP,SAAS;IACTK,iBAAiB;IACjBG,YAAY;IACZC,eAAe;IAEf;IACAlB,OAAO;IACPC,gBAAgB;IAChBkB,YAAY;IACZG,eAAe;IACfC,cAAc;IACdC;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AAFAtC,EAAA,CAxRaF,OAAO;EAAA,QAEDnB,cAAc,EAMTC,cAAc,EACdA,cAAc,EACVA,cAAc,EAC1BA,cAAc,EACVA,cAAc,EAChBA,cAAc,EAChBA,cAAc,EACRA,cAAc,EACdA,cAAc,EAClBA,cAAc,EACLA,cAAc;AAAA;AAyQzC,OAAO,MAAMoF,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,OAAOnE,OAAO,CAAC;IAAEF,QAAQ,EAAE;EAAgB,CAAC,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AAFAqE,GAAA,CAJaD,gBAAgB;EAAA,QACpBlE,OAAO;AAAA;AAMhB,OAAO,MAAMoE,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxC,OAAOrE,OAAO,CAAC;IAAEF,QAAQ,EAAE;EAAoB,CAAC,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AAFAuE,GAAA,CAJaD,oBAAoB;EAAA,QACxBpE,OAAO;AAAA;AAMhB,OAAO,MAAMsE,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,OAAOvE,OAAO,CAAC;IAAEF,QAAQ,EAAE;EAAQ,CAAC,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AAFAyE,GAAA,CAJaD,QAAQ;EAAA,QACZtE,OAAO;AAAA;AAMhB,OAAO,MAAMwE,YAAY,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAChC,OAAOzE,OAAO,CAAC;IAAEF,QAAQ,EAAE;EAAY,CAAC,CAAC;AAC3C,CAAC;;AAED;AACA;AACA;AAFA2E,GAAA,CAJaD,YAAY;EAAA,QAChBxE,OAAO;AAAA;AAMhB,OAAO,MAAM0E,kBAAkB,GAAGA,CAACC,UAAkB,GAAG,KAAK,KAAK;EAAAC,GAAA;EAChE,OAAO5E,OAAO,CAAC;IAAEJ,eAAe,EAAE+E;EAAW,CAAC,CAAC;AACjD,CAAC;AAACC,GAAA,CAFWF,kBAAkB;EAAA,QACtB1E,OAAO;AAAA;AAGhB,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}