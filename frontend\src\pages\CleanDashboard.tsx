// Main dashboard page - shows all the charts and handles data loading

import React, { useState, useEffect } from 'react';
import {
  Container,
  Grid,
  Box,
  Typography,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import { fetchDashboardData, DashboardData } from '../services/api';
import Card<PERSON>hart from '../components/cards/CardChart';
import CleanHeader from '../components/layout/CleanHeader';
import CleanFooter from '../components/layout/CleanFooter';



const CleanDashboard: React.FC = () => {
  const theme = useTheme();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Load data from API

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const data = await fetchDashboardData();
      setDashboardData(data);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
      console.error('Dashboard data loading error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on mount
  useEffect(() => {
    loadDashboardData();
  }, []);



  const handleRefresh = async () => {
    await loadDashboardData();
  };



  const renderError = () => (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Alert 
        severity="error" 
        sx={{ 
          borderRadius: 2,
          '& .MuiAlert-message': {
            width: '100%',
          },
        }}
      >
        <Typography variant="h6" gutterBottom>
          Failed to load dashboard data
        </Typography>
        <Typography variant="body2">
          {error || 'An unexpected error occurred. Please try refreshing the page.'}
        </Typography>
      </Alert>
    </Container>
  );

  const renderDashboard = () => (
    <Container maxWidth="lg" sx={{ py: { xs: 2, sm: 4 }, px: { xs: 2, sm: 3 } }}>

      <Fade in timeout={600}>
        <Box sx={{ mb: { xs: 3, sm: 5 }, textAlign: 'center' }}>
          <Typography
            variant="h5"
            component="h2"
            sx={{
              fontWeight: 600,
              color: 'text.primary',
              mb: 1,
              fontSize: { xs: '1.25rem', sm: '1.5rem' },
            }}
          >
            Analytics Dashboard
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{
              maxWidth: 600,
              mx: 'auto',
              fontSize: { xs: '0.9rem', sm: '1rem' },
            }}
          >
            Real-time insights and analytics for your business data
          </Typography>
        </Box>
      </Fade>

      <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
        <Grid item xs={12} md={6}>
          <CardChart
            title="Customer Type"
            data={dashboardData?.customerTypes || []}
            chartType="donut"
            loading={isLoading}
            error={error}
            totalLabel="Total Customers"
            onRefresh={handleRefresh}
          />
        </Grid>


        <Grid item xs={12} md={6}>
          <CardChart
            title="Account Industries"
            data={dashboardData?.accountIndustries || []}
            chartType="bar"
            loading={isLoading}
            error={error}
            totalLabel="Total Revenue"
            onRefresh={handleRefresh}
          />
        </Grid>


        <Grid item xs={12} md={6}>
          <CardChart
            title="Team"
            data={dashboardData?.teams || []}
            chartType="bar"
            loading={isLoading}
            error={error}
            totalLabel="Total Members"
            onRefresh={handleRefresh}
          />
        </Grid>

        {/* ACV Ranges */}
        <Grid item xs={12} md={6}>
          <CardChart
            title="ACV Range"
            data={dashboardData?.acvRanges || []}
            chartType="donut"
            loading={isLoading}
            error={error}
            totalLabel="Total Value"
            onRefresh={handleRefresh}
          />
        </Grid>
      </Grid>
    </Container>
  );





  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
        backgroundColor: theme.palette.background.default,
      }}
    >

      <CleanHeader
        title="SkyGenI Dashboard"
        lastUpdated={lastUpdated}
        isLoading={isLoading}
        onRefresh={handleRefresh}
      />


      <Box component="main" sx={{ flexGrow: 1 }}>
        {error && !isLoading ? renderError() : renderDashboard()}
      </Box>


      <CleanFooter
        lastUpdated={lastUpdated}
        version="v1.0.0"
      />
    </Box>
  );
};

export default CleanDashboard;
