{"ast": null, "code": "import { sqrt } from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n  Y0 = 0,\n  Z0 = 0,\n  X1 = 0,\n  Y1 = 0,\n  Z1 = 0,\n  X2 = 0,\n  Y2 = 0,\n  Z2 = 0,\n  x00,\n  y00,\n  x0,\n  y0;\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function () {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function () {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function () {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2] : Z1 ? [X1 / Z1, Y1 / Z1] : Z0 ? [X0 / Z0, Y0 / Z0] : [NaN, NaN];\n    X0 = Y0 = Z0 = X1 = Y1 = Z1 = X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidPointLine(x, y) {\n  var dx = x - x0,\n    dy = y - y0,\n    z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n    dy = y - y0,\n    z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\nexport default centroidStream;", "map": {"version": 3, "names": ["sqrt", "X0", "Y0", "Z0", "X1", "Y1", "Z1", "X2", "Y2", "Z2", "x00", "y00", "x0", "y0", "centroidStream", "point", "centroidPoint", "lineStart", "centroidLineStart", "lineEnd", "centroidLineEnd", "polygonStart", "centroidRingStart", "centroidRingEnd", "polygonEnd", "result", "centroid", "NaN", "x", "y", "centroidPointFirstLine", "centroidPointLine", "dx", "dy", "z", "centroidPointFirstRing", "centroidPointRing"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-geo/src/path/centroid.js"], "sourcesContent": ["import {sqrt} from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = sqrt(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nexport default centroidStream;\n"], "mappings": "AAAA,SAAQA,IAAI,QAAO,YAAY;;AAE/B;;AAEA,IAAIC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,EAAE,GAAG,CAAC;EACNC,GAAG;EACHC,GAAG;EACHC,EAAE;EACFC,EAAE;AAEN,IAAIC,cAAc,GAAG;EACnBC,KAAK,EAAEC,aAAa;EACpBC,SAAS,EAAEC,iBAAiB;EAC5BC,OAAO,EAAEC,eAAe;EACxBC,YAAY,EAAE,SAAAA,CAAA,EAAW;IACvBP,cAAc,CAACG,SAAS,GAAGK,iBAAiB;IAC5CR,cAAc,CAACK,OAAO,GAAGI,eAAe;EAC1C,CAAC;EACDC,UAAU,EAAE,SAAAA,CAAA,EAAW;IACrBV,cAAc,CAACC,KAAK,GAAGC,aAAa;IACpCF,cAAc,CAACG,SAAS,GAAGC,iBAAiB;IAC5CJ,cAAc,CAACK,OAAO,GAAGC,eAAe;EAC1C,CAAC;EACDK,MAAM,EAAE,SAAAA,CAAA,EAAW;IACjB,IAAIC,QAAQ,GAAGjB,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,EAAED,EAAE,GAAGC,EAAE,CAAC,GAChCH,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,EAAED,EAAE,GAAGC,EAAE,CAAC,GACvBH,EAAE,GAAG,CAACF,EAAE,GAAGE,EAAE,EAAED,EAAE,GAAGC,EAAE,CAAC,GACvB,CAACwB,GAAG,EAAEA,GAAG,CAAC;IAChB1B,EAAE,GAAGC,EAAE,GAAGC,EAAE,GACZC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GACZC,EAAE,GAAGC,EAAE,GAAGC,EAAE,GAAG,CAAC;IAChB,OAAOiB,QAAQ;EACjB;AACF,CAAC;AAED,SAASV,aAAaA,CAACY,CAAC,EAAEC,CAAC,EAAE;EAC3B5B,EAAE,IAAI2B,CAAC;EACP1B,EAAE,IAAI2B,CAAC;EACP,EAAE1B,EAAE;AACN;AAEA,SAASe,iBAAiBA,CAAA,EAAG;EAC3BJ,cAAc,CAACC,KAAK,GAAGe,sBAAsB;AAC/C;AAEA,SAASA,sBAAsBA,CAACF,CAAC,EAAEC,CAAC,EAAE;EACpCf,cAAc,CAACC,KAAK,GAAGgB,iBAAiB;EACxCf,aAAa,CAACJ,EAAE,GAAGgB,CAAC,EAAEf,EAAE,GAAGgB,CAAC,CAAC;AAC/B;AAEA,SAASE,iBAAiBA,CAACH,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIG,EAAE,GAAGJ,CAAC,GAAGhB,EAAE;IAAEqB,EAAE,GAAGJ,CAAC,GAAGhB,EAAE;IAAEqB,CAAC,GAAGlC,IAAI,CAACgC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EACzD7B,EAAE,IAAI8B,CAAC,IAAItB,EAAE,GAAGgB,CAAC,CAAC,GAAG,CAAC;EACtBvB,EAAE,IAAI6B,CAAC,IAAIrB,EAAE,GAAGgB,CAAC,CAAC,GAAG,CAAC;EACtBvB,EAAE,IAAI4B,CAAC;EACPlB,aAAa,CAACJ,EAAE,GAAGgB,CAAC,EAAEf,EAAE,GAAGgB,CAAC,CAAC;AAC/B;AAEA,SAAST,eAAeA,CAAA,EAAG;EACzBN,cAAc,CAACC,KAAK,GAAGC,aAAa;AACtC;AAEA,SAASM,iBAAiBA,CAAA,EAAG;EAC3BR,cAAc,CAACC,KAAK,GAAGoB,sBAAsB;AAC/C;AAEA,SAASZ,eAAeA,CAAA,EAAG;EACzBa,iBAAiB,CAAC1B,GAAG,EAAEC,GAAG,CAAC;AAC7B;AAEA,SAASwB,sBAAsBA,CAACP,CAAC,EAAEC,CAAC,EAAE;EACpCf,cAAc,CAACC,KAAK,GAAGqB,iBAAiB;EACxCpB,aAAa,CAACN,GAAG,GAAGE,EAAE,GAAGgB,CAAC,EAAEjB,GAAG,GAAGE,EAAE,GAAGgB,CAAC,CAAC;AAC3C;AAEA,SAASO,iBAAiBA,CAACR,CAAC,EAAEC,CAAC,EAAE;EAC/B,IAAIG,EAAE,GAAGJ,CAAC,GAAGhB,EAAE;IACXqB,EAAE,GAAGJ,CAAC,GAAGhB,EAAE;IACXqB,CAAC,GAAGlC,IAAI,CAACgC,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE,CAAC;EAE/B7B,EAAE,IAAI8B,CAAC,IAAItB,EAAE,GAAGgB,CAAC,CAAC,GAAG,CAAC;EACtBvB,EAAE,IAAI6B,CAAC,IAAIrB,EAAE,GAAGgB,CAAC,CAAC,GAAG,CAAC;EACtBvB,EAAE,IAAI4B,CAAC;EAEPA,CAAC,GAAGrB,EAAE,GAAGe,CAAC,GAAGhB,EAAE,GAAGiB,CAAC;EACnBtB,EAAE,IAAI2B,CAAC,IAAItB,EAAE,GAAGgB,CAAC,CAAC;EAClBpB,EAAE,IAAI0B,CAAC,IAAIrB,EAAE,GAAGgB,CAAC,CAAC;EAClBpB,EAAE,IAAIyB,CAAC,GAAG,CAAC;EACXlB,aAAa,CAACJ,EAAE,GAAGgB,CAAC,EAAEf,EAAE,GAAGgB,CAAC,CAAC;AAC/B;AAEA,eAAef,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}