/**
 * DataCard Component for SkyGeni Dashboard
 * 
 * A reusable card component that displays data with charts:
 * - Material-UI Card with elevation
 * - Integrated chart display (Bar or Doughnut)
 * - Loading and error states
 * - Responsive design
 * - Action buttons
 * - Data summary display
 */

import React from 'react';
import {
  Card,
  CardHeader,
  CardContent,
  Typography,
  Box,
  IconButton,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  MoreVert as MoreVertIcon,
} from '@mui/icons-material';
import BarChart from '../charts/BarChart';
import DoughnutChart from '../charts/DoughnutChart';
import Shimmer from '../common/Shimmer';
import ErrorMessage from '../common/ErrorMessage';
import { DataCardProps } from '../../types';
import { BarChartData, DoughnutChartData } from '../charts/types';

// ============================================================================
// Helper Functions
// ============================================================================

/**
 * Transform generic data to chart format
 */
const transformDataForChart = (data: any[], chartType: 'bar' | 'doughnut') => {
  if (!data || data.length === 0) {
    return chartType === 'bar' 
      ? { categories: [], values: [], colors: [] }
      : { segments: [], total: 0 };
  }

  if (chartType === 'bar') {
    return {
      categories: data.map(item => item.type || item.name || item.label || 'Unknown'),
      values: data.map(item => item.count || item.value || 0),
      colors: data.map((_, index) => `hsl(${(index * 360) / data.length}, 70%, 50%)`),
    } as BarChartData;
  } else {
    const segments = data.map((item, index) => ({
      label: item.type || item.name || item.label || 'Unknown',
      value: item.count || item.value || 0,
      percentage: item.percentage || 0,
      color: `hsl(${(index * 360) / data.length}, 70%, 50%)`,
    }));
    
    const total = segments.reduce((sum, segment) => sum + segment.value, 0);
    
    return {
      segments,
      total,
      centerLabel: `Total: ${total.toLocaleString()}`,
    } as DoughnutChartData;
  }
};

/**
 * Calculate summary statistics
 */
const calculateSummary = (data: any[]) => {
  if (!data || data.length === 0) {
    return { total: 0, count: 0, average: 0 };
  }

  const total = data.reduce((sum, item) => sum + (item.count || item.value || 0), 0);
  const count = data.length;
  const average = count > 0 ? total / count : 0;

  return { total, count, average };
};

// ============================================================================
// DataCard Component
// ============================================================================

const DataCard: React.FC<DataCardProps> = ({
  title,
  data,
  chartType,
  loading = false,
  error,
  className,
  elevation = 2,
}) => {
  // const theme = useTheme();
  const [refreshing, setRefreshing] = React.useState(false);

  // Calculate summary statistics
  const summary = React.useMemo(() => calculateSummary(data), [data]);

  // Transform data for chart
  const chartData = React.useMemo(() => 
    transformDataForChart(data, chartType), 
    [data, chartType]
  );

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleRefresh = async () => {
    setRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshing(false);
    }, 1000);
  };

  const handleChartClick = (chartData: any, event: MouseEvent) => {
    console.log('Chart clicked:', chartData);
  };

  const handleChartHover = (chartData: any, event: MouseEvent) => {
    // Handle chart hover if needed
  };

  // ========================================================================
  // Render Chart
  // ========================================================================

  const renderChart = () => {
    if (loading || refreshing) {
      return (
        <Box sx={{ p: 2 }}>
          <Shimmer variant="chart" height={280} />
        </Box>
      );
    }

    if (error) {
      return (
        <Box sx={{ p: 2 }}>
          <ErrorMessage
            error={error}
            showRetryButton={true}
            onRetry={handleRefresh}
            centered={true}
          />
        </Box>
      );
    }

    if (!data || data.length === 0) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            py: 4,
            color: 'text.secondary'
          }}
        >
          <Typography variant="body2">No data available</Typography>
        </Box>
      );
    }

    const commonProps = {
      onBarClick: chartType === 'bar' ? handleChartClick : undefined,
      onBarHover: chartType === 'bar' ? handleChartHover : undefined,
      onSegmentClick: chartType === 'doughnut' ? handleChartClick : undefined,
      onSegmentHover: chartType === 'doughnut' ? handleChartHover : undefined,
    };

    if (chartType === 'bar') {
      return (
        <BarChart
          data={chartData as BarChartData}
          config={{
            width: 350,
            height: 250,
            margin: { top: 20, right: 20, bottom: 50, left: 50 },
            showGrid: true,
            showLabels: false,
            xAxisLabel: title.includes('Industry') ? 'Industry' : title.includes('Team') ? 'Teams' : 'Category',
            yAxisLabel: title.includes('Industry') ? 'Revenue ($)' : title.includes('Team') ? 'Members' : 'Count',
          }}
          {...commonProps}
        />
      );
    } else {
      return (
        <DoughnutChart
          data={chartData as DoughnutChartData}
          config={{
            width: 300,
            height: 250,
            margin: { top: 10, right: 10, bottom: 10, left: 10 },
            innerRadius: 50,
            outerRadius: 80,
            showLabels: false,
          }}
          {...commonProps}
        />
      );
    }
  };

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Card
      className={className}
      elevation={elevation}
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid rgba(0, 0, 0, 0.1)',
        borderRadius: 1,
      }}
    >
      {/* Card Header */}
      <CardHeader
        sx={{ pb: 1 }}
        title={
          <Typography
            variant="h6"
            component="h2"
            sx={{
              fontWeight: 600,
              fontSize: '1rem',
              color: 'text.primary',
            }}
          >
            {title}
          </Typography>
        }
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              size="small"
              onClick={handleRefresh}
              disabled={Boolean(loading) || Boolean(refreshing)}
              aria-label="Refresh data"
            >
              <RefreshIcon
                sx={{
                  animation: (Boolean(loading) || Boolean(refreshing)) ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  },
                }} 
              />
            </IconButton>
            <IconButton size="small" aria-label="More options">
              <MoreVertIcon />
            </IconButton>
          </Box>
        }
        sx={{ pb: 1 }}
      />



      {/* Chart Content */}
      <CardContent 
        sx={{ 
          flex: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          p: 1,
          '&:last-child': { pb: 1 },
        }}
      >
        {renderChart()}
      </CardContent>


    </Card>
  );
};

export default DataCard;
