{"ast": null, "code": "import { degrees, pi, radians } from \"../math.js\";\nimport { projectionMutator } from \"./index.js\";\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n    phi1 = pi / 3,\n    m = projectionMutator(projectAt),\n    p = m(phi0, phi1);\n  p.parallels = function (_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n  return p;\n}", "map": {"version": 3, "names": ["degrees", "pi", "radians", "projectionMutator", "conicProjection", "projectAt", "phi0", "phi1", "m", "p", "parallels", "_", "arguments", "length"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-geo/src/projection/conic.js"], "sourcesContent": ["import {degrees, pi, radians} from \"../math.js\";\nimport {projectionMutator} from \"./index.js\";\n\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = pi / 3,\n      m = projectionMutator(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n\n  return p;\n}\n"], "mappings": "AAAA,SAAQA,OAAO,EAAEC,EAAE,EAAEC,OAAO,QAAO,YAAY;AAC/C,SAAQC,iBAAiB,QAAO,YAAY;AAE5C,OAAO,SAASC,eAAeA,CAACC,SAAS,EAAE;EACzC,IAAIC,IAAI,GAAG,CAAC;IACRC,IAAI,GAAGN,EAAE,GAAG,CAAC;IACbO,CAAC,GAAGL,iBAAiB,CAACE,SAAS,CAAC;IAChCI,CAAC,GAAGD,CAAC,CAACF,IAAI,EAAEC,IAAI,CAAC;EAErBE,CAAC,CAACC,SAAS,GAAG,UAASC,CAAC,EAAE;IACxB,OAAOC,SAAS,CAACC,MAAM,GAAGL,CAAC,CAACF,IAAI,GAAGK,CAAC,CAAC,CAAC,CAAC,GAAGT,OAAO,EAAEK,IAAI,GAAGI,CAAC,CAAC,CAAC,CAAC,GAAGT,OAAO,CAAC,GAAG,CAACI,IAAI,GAAGN,OAAO,EAAEO,IAAI,GAAGP,OAAO,CAAC;EAC9G,CAAC;EAED,OAAOS,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}