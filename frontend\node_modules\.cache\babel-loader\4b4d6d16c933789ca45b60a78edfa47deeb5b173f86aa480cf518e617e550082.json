{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\CleanApp.tsx\";\n/**\n * CleanApp Component for SkyGeni Dashboard\n * Professional React app with Material-UI theme and clean architecture\n */\n\nimport React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box } from '@mui/material';\nimport CleanDashboard from './pages/CleanDashboard';\n\n// ============================================================================\n// Theme Configuration\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0'\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#f5336b',\n      dark: '#9a0036'\n    },\n    background: {\n      default: '#f8fafc',\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#718096'\n    }\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700\n    },\n    h2: {\n      fontWeight: 600\n    },\n    h3: {\n      fontWeight: 600\n    },\n    h4: {\n      fontWeight: 600\n    },\n    h5: {\n      fontWeight: 600\n    },\n    h6: {\n      fontWeight: 600\n    }\n  },\n  shape: {\n    borderRadius: 12\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)'\n          },\n          transition: 'all 0.3s ease-in-out'\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600\n        }\n      }\n    }\n  }\n});\n\n// ============================================================================\n// CleanApp Component\n// ============================================================================\n\nconst CleanApp = () => {\n  return /*#__PURE__*/_jsxDEV(ThemeProvider, {\n    theme: theme,\n    children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        minHeight: '100vh',\n        backgroundColor: theme.palette.background.default\n      },\n      children: /*#__PURE__*/_jsxDEV(CleanDashboard, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_c = CleanApp;\nexport default CleanApp;\nvar _c;\n$RefreshReg$(_c, \"CleanApp\");", "map": {"version": 3, "names": ["React", "ThemeProvider", "createTheme", "CssBaseline", "Box", "CleanDashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "mode", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "typography", "fontFamily", "h1", "fontWeight", "h2", "h3", "h4", "h5", "h6", "shape", "borderRadius", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "transition", "MuiB<PERSON>on", "textTransform", "CleanApp", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sx", "minHeight", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/CleanApp.tsx"], "sourcesContent": ["/**\n * CleanApp Component for SkyGeni Dashboard\n * Professional React app with Material-UI theme and clean architecture\n */\n\nimport React from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline, Box } from '@mui/material';\nimport CleanDashboard from './pages/CleanDashboard';\n\n// ============================================================================\n// Theme Configuration\n// ============================================================================\n\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#1976d2',\n      light: '#42a5f5',\n      dark: '#1565c0',\n    },\n    secondary: {\n      main: '#dc004e',\n      light: '#f5336b',\n      dark: '#9a0036',\n    },\n    background: {\n      default: '#f8fafc',\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#1a202c',\n      secondary: '#718096',\n    },\n  },\n  typography: {\n    fontFamily: '\"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: { fontWeight: 700 },\n    h2: { fontWeight: 600 },\n    h3: { fontWeight: 600 },\n    h4: { fontWeight: 600 },\n    h5: { fontWeight: 600 },\n    h6: { fontWeight: 600 },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n          },\n          transition: 'all 0.3s ease-in-out',\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 600,\n        },\n      },\n    },\n  },\n});\n\n// ============================================================================\n// CleanApp Component\n// ============================================================================\n\nconst CleanApp: React.FC = () => {\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Box\n        sx={{\n          minHeight: '100vh',\n          backgroundColor: theme.palette.background.default,\n        }}\n      >\n        <CleanDashboard />\n      </Box>\n    </ThemeProvider>\n  );\n};\n\nexport default CleanApp;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,EAAEC,GAAG,QAAQ,eAAe;AAChD,OAAOC,cAAc,MAAM,wBAAwB;;AAEnD;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,KAAK,GAAGN,WAAW,CAAC;EACxBO,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAClBI,SAAS,EAAE;IACb;EACF,CAAC;EACDK,UAAU,EAAE;IACVC,UAAU,EAAE,4CAA4C;IACxDC,EAAE,EAAE;MAAEC,UAAU,EAAE;IAAI,CAAC;IACvBC,EAAE,EAAE;MAAED,UAAU,EAAE;IAAI,CAAC;IACvBE,EAAE,EAAE;MAAEF,UAAU,EAAE;IAAI,CAAC;IACvBG,EAAE,EAAE;MAAEH,UAAU,EAAE;IAAI,CAAC;IACvBI,EAAE,EAAE;MAAEJ,UAAU,EAAE;IAAI,CAAC;IACvBK,EAAE,EAAE;MAAEL,UAAU,EAAE;IAAI;EACxB,CAAC;EACDM,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE,2BAA2B;UACtC,SAAS,EAAE;YACTA,SAAS,EAAE;UACb,CAAC;UACDC,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTJ,cAAc,EAAE;QACdC,IAAI,EAAE;UACJI,aAAa,EAAE,MAAM;UACrBf,UAAU,EAAE;QACd;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,MAAMgB,QAAkB,GAAGA,CAAA,KAAM;EAC/B,oBACEhC,OAAA,CAACN,aAAa;IAACO,KAAK,EAAEA,KAAM;IAAAgC,QAAA,gBAC1BjC,OAAA,CAACJ,WAAW;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACfrC,OAAA,CAACH,GAAG;MACFyC,EAAE,EAAE;QACFC,SAAS,EAAE,OAAO;QAClBC,eAAe,EAAEvC,KAAK,CAACC,OAAO,CAACO,UAAU,CAACC;MAC5C,CAAE;MAAAuB,QAAA,eAEFjC,OAAA,CAACF,cAAc;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACI,EAAA,GAdIT,QAAkB;AAgBxB,eAAeA,QAAQ;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}