// API routes for dashboard data - handles all the /api/data/* endpoints

import { Router, Request, Response, NextFunction } from 'express';
import { dataService, prepareChartData } from '../data';
import { ApiResponse, ApiError } from '../types';

const router = Router();

// Helper functions for API responses

// Standard success response format
const createSuccessResponse = <T>(data: T, message?: string): ApiResponse<T> => ({
  success: true,
  data,
  message: message || 'Success',
  timestamp: new Date().toISOString()
});

// Standard error response format
const createErrorResponse = (error: string, message: string, statusCode: number = 500): ApiError => ({
  success: false,
  error,
  message,
  statusCode,
  timestamp: new Date().toISOString()
});

// Wrapper to catch async errors
const asyncHandler = (fn: (req: Request, res: Response, next: NextFunction) => Promise<any>) =>
  (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };

// ============================================================================
// Individual Data Endpoints
// ============================================================================

/**
 * GET /api/data/customer-types
 * Returns customer type data for visualization
 */
router.get('/customer-types', asyncHandler(async (req: Request, res: Response) => {
  console.log('📊 Fetching customer types data...');
  
  try {
    const customerTypes = await dataService.loadCustomerTypes();
    
    res.json(createSuccessResponse(
      customerTypes,
      `Retrieved ${customerTypes.length} customer types`
    ));
    
    console.log(`✅ Sent ${customerTypes.length} customer types`);
  } catch (error) {
    console.error('❌ Error fetching customer types:', error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch customer types data'
    ));
  }
}));

/**
 * GET /api/data/account-industries
 * Returns account industry data for visualization
 */
router.get('/account-industries', asyncHandler(async (req: Request, res: Response) => {
  console.log('📊 Fetching account industries data...');
  
  try {
    const accountIndustries = await dataService.loadAccountIndustries();
    
    res.json(createSuccessResponse(
      accountIndustries,
      `Retrieved ${accountIndustries.length} account industries`
    ));
    
    console.log(`✅ Sent ${accountIndustries.length} account industries`);
  } catch (error) {
    console.error('❌ Error fetching account industries:', error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch account industries data'
    ));
  }
}));

/**
 * GET /api/data/teams
 * Returns team data for visualization
 */
router.get('/teams', asyncHandler(async (req: Request, res: Response) => {
  console.log('📊 Fetching teams data...');
  
  try {
    const teams = await dataService.loadTeams();
    
    res.json(createSuccessResponse(
      teams,
      `Retrieved ${teams.length} teams`
    ));
    
    console.log(`✅ Sent ${teams.length} teams`);
  } catch (error) {
    console.error('❌ Error fetching teams:', error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch teams data'
    ));
  }
}));

/**
 * GET /api/data/acv-ranges
 * Returns ACV range data for visualization
 */
router.get('/acv-ranges', asyncHandler(async (req: Request, res: Response) => {
  console.log('📊 Fetching ACV ranges data...');
  
  try {
    const acvRanges = await dataService.loadACVRanges();
    
    res.json(createSuccessResponse(
      acvRanges,
      `Retrieved ${acvRanges.length} ACV ranges`
    ));
    
    console.log(`✅ Sent ${acvRanges.length} ACV ranges`);
  } catch (error) {
    console.error('❌ Error fetching ACV ranges:', error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch ACV ranges data'
    ));
  }
}));

// ============================================================================
// Combined Dashboard Endpoint
// ============================================================================

/**
 * GET /api/data/dashboard
 * Returns all dashboard data in a single response
 * This is the main endpoint the frontend will use
 */
router.get('/dashboard', asyncHandler(async (req: Request, res: Response) => {
  console.log('📊 Fetching complete dashboard data...');
  
  try {
    const dashboardData = await dataService.loadAllData();
    
    res.json(createSuccessResponse(
      dashboardData,
      'Dashboard data retrieved successfully'
    ));
    
    console.log('✅ Sent complete dashboard data');
  } catch (error) {
    console.error('❌ Error fetching dashboard data:', error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch dashboard data'
    ));
  }
}));

// ============================================================================
// Chart-Specific Data Endpoints
// ============================================================================

/**
 * GET /api/data/charts/:type
 * Returns data formatted specifically for chart libraries (D3.js)
 * Supported types: bar, doughnut
 */
router.get('/charts/:type', asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { type } = req.params;
  const { dataType } = req.query;
  
  console.log(`📊 Fetching chart data for type: ${type}, dataType: ${dataType}`);
  
  try {
    let rawData: any[] = [];
    
    // Get the appropriate raw data based on dataType query parameter
    switch (dataType) {
      case 'customer-types':
        rawData = await dataService.loadCustomerTypes();
        break;
      case 'account-industries':
        rawData = await dataService.loadAccountIndustries();
        break;
      case 'teams':
        rawData = await dataService.loadTeams();
        break;
      case 'acv-ranges':
        rawData = await dataService.loadACVRanges();
        break;
      default:
        res.status(400).json(createErrorResponse(
          'INVALID_DATA_TYPE',
          'Invalid dataType. Must be one of: customer-types, account-industries, teams, acv-ranges'
        ));
        return;
    }
    
    // Format data based on chart type
    let chartData;
    switch (type) {
      case 'bar':
        chartData = prepareChartData.forBarChart(rawData);
        break;
      case 'doughnut':
        chartData = prepareChartData.forDoughnutChart(rawData);
        break;
      default:
        res.status(400).json(createErrorResponse(
          'INVALID_CHART_TYPE',
          'Invalid chart type. Supported types: bar, doughnut'
        ));
        return;
    }
    
    res.json(createSuccessResponse(
      chartData,
      `Chart data prepared for ${type} chart with ${dataType} data`
    ));
    
    console.log(`✅ Sent ${type} chart data for ${dataType}`);
  } catch (error) {
    console.error(`❌ Error fetching chart data:`, error);
    res.status(500).json(createErrorResponse(
      'FETCH_ERROR',
      'Failed to fetch chart data'
    ));
  }
}));

// ============================================================================
// Health Check Endpoint
// ============================================================================

/**
 * GET /api/data/health
 * Simple health check endpoint to verify the API is working
 */
router.get('/health', (req: Request, res: Response) => {
  res.json(createSuccessResponse(
    { status: 'healthy', uptime: process.uptime() },
    'Data API is healthy'
  ));
});

// ============================================================================
// Error Handling Middleware
// ============================================================================

/**
 * Global error handler for data routes
 */
router.use((error: Error, req: Request, res: Response, next: Function) => {
  console.error('❌ Unhandled error in data routes:', error);
  
  res.status(500).json(createErrorResponse(
    'INTERNAL_ERROR',
    'An unexpected error occurred while processing your request'
  ));
});

export default router;
