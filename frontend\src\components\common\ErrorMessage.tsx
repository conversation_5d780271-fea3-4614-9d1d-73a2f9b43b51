/**
 * ErrorMessage Component for SkyGeni Dashboard
 * 
 * A comprehensive error display component with:
 * - Multiple severity levels
 * - Retry functionality
 * - Dismissible alerts
 * - Error details expansion
 * - Custom actions
 * - Responsive design
 */

import React, { useState } from 'react';
import {
  Box,
  Alert,
  AlertTitle,
  Button,
  IconButton,
  Typography,
  Collapse,
  Paper,
  useTheme,
} from '@mui/material';
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { ErrorMessageProps } from './types';

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Extract error message from various error types
 */
const getErrorMessage = (error: string | Error | null): string => {
  if (!error) return 'An unknown error occurred';
  if (typeof error === 'string') return error;
  if (error instanceof Error) return error.message;
  return String(error);
};

/**
 * Extract error details for debugging
 */
const getErrorDetails = (error: string | Error | null): string | null => {
  if (!error) return null;
  if (typeof error === 'string') return null;
  if (error instanceof Error) {
    return error.stack || error.toString();
  }
  return null;
};

// ============================================================================
// ErrorMessage Component
// ============================================================================

const ErrorMessage: React.FC<ErrorMessageProps> = ({
  error,
  title,
  showRetryButton = false,
  onRetry,
  retryButtonText = 'Try Again',
  showDetails = false,
  className,
  sx,
  severity = 'error',
  centered = true,
  icon,
  dismissible = false,
  onDismiss,
  actions,
}) => {
  const theme = useTheme();
  const [showErrorDetails, setShowErrorDetails] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  // Don't render if no error or if dismissed
  if (!error || dismissed) return null;

  const errorMessage = getErrorMessage(error);
  const errorDetails = getErrorDetails(error);
  const hasDetails = showDetails && errorDetails;

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const handleDismiss = () => {
    setDismissed(true);
    if (onDismiss) {
      onDismiss();
    }
  };

  const toggleDetails = () => {
    setShowErrorDetails(!showErrorDetails);
  };

  // ========================================================================
  // Icon Selection
  // ========================================================================

  const getIcon = () => {
    if (icon) return icon;
    
    switch (severity) {
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      case 'error':
      default:
        return <ErrorIcon />;
    }
  };

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Box
      className={className}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: centered ? 'center' : 'stretch',
        justifyContent: centered ? 'center' : 'flex-start',
        width: '100%',
        ...sx,
      }}
    >
      <Alert
        severity={severity}
        icon={getIcon()}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Custom Actions */}
            {actions}
            
            {/* Retry Button */}
            {showRetryButton && onRetry && (
              <Button
                size="small"
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={handleRetry}
                sx={{
                  borderColor: 'currentColor',
                  color: 'inherit',
                  '&:hover': {
                    borderColor: 'currentColor',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                {retryButtonText}
              </Button>
            )}
            
            {/* Details Toggle */}
            {hasDetails && (
              <IconButton
                size="small"
                onClick={toggleDetails}
                sx={{ color: 'inherit' }}
                aria-label={showErrorDetails ? 'Hide details' : 'Show details'}
              >
                {showErrorDetails ? <ExpandLessIcon /> : <ExpandMoreIcon />}
              </IconButton>
            )}
            
            {/* Dismiss Button */}
            {dismissible && (
              <IconButton
                size="small"
                onClick={handleDismiss}
                sx={{ color: 'inherit' }}
                aria-label="Dismiss"
              >
                <CloseIcon />
              </IconButton>
            )}
          </Box>
        }
        sx={{
          width: '100%',
          maxWidth: centered ? 600 : '100%',
          '& .MuiAlert-message': {
            width: '100%',
          },
        }}
      >
        {title && <AlertTitle>{title}</AlertTitle>}
        
        <Typography variant="body2" component="div">
          {errorMessage}
        </Typography>
        
        {/* Error Details Collapse */}
        {hasDetails && (
          <Collapse in={showErrorDetails} sx={{ mt: 1 }}>
            <Paper
              variant="outlined"
              sx={{
                p: 2,
                mt: 1,
                backgroundColor: theme.palette.action.hover,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              <Typography
                variant="caption"
                component="div"
                sx={{
                  fontFamily: 'monospace',
                  fontSize: '0.75rem',
                  whiteSpace: 'pre-wrap',
                  wordBreak: 'break-word',
                  color: theme.palette.text.secondary,
                }}
              >
                {errorDetails}
              </Typography>
            </Paper>
          </Collapse>
        )}
      </Alert>
    </Box>
  );
};

// ============================================================================
// Preset Error Components
// ============================================================================

/**
 * Network error component with retry functionality
 */
export const NetworkError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title' | 'showRetryButton'>> = (props) => (
  <ErrorMessage
    {...props}
    severity="error"
    title="Network Error"
    showRetryButton={true}
    icon={<RefreshIcon />}
  />
);

/**
 * Validation error component
 */
export const ValidationError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title'>> = (props) => (
  <ErrorMessage
    {...props}
    severity="warning"
    title="Validation Error"
  />
);

/**
 * Not found error component
 */
export const NotFoundError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title'>> = (props) => (
  <ErrorMessage
    {...props}
    severity="info"
    title="Not Found"
  />
);

/**
 * Permission error component
 */
export const PermissionError: React.FC<Omit<ErrorMessageProps, 'severity' | 'title'>> = (props) => (
  <ErrorMessage
    {...props}
    severity="warning"
    title="Permission Denied"
  />
);

/**
 * Generic API error component with retry
 */
export const ApiError: React.FC<Omit<ErrorMessageProps, 'severity' | 'showRetryButton' | 'showDetails'>> = (props) => (
  <ErrorMessage
    {...props}
    severity="error"
    showRetryButton={true}
    showDetails={true}
  />
);

export default ErrorMessage;
