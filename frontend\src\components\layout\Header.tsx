/**
 * Header Component for SkyGeni Dashboard
 * 
 * Main navigation header with:
 * - Application title and logo
 * - Refresh button for data
 * - Theme toggle
 * - User actions
 * - Responsive design
 */

import React from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Tooltip,
  // Button,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { HeaderProps } from '../../types';
import { useData } from '../../hooks/useData';

// ============================================================================
// Header Component
// ============================================================================

const Header: React.FC<HeaderProps> = ({
  title = 'SkyGeni Dashboard',
  showRefreshButton = true,
  onRefresh,
}) => {
  const theme = useTheme();
  const { 
    refetch, 
    isLoading, 
    lastFetched, 
    dashboardData,
    isSuccess 
  } = useData({ autoFetch: false });

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleRefresh = async () => {
    try {
      if (onRefresh) {
        await onRefresh();
      } else {
        await refetch();
      }
    } catch (error) {
      console.error('Failed to refresh data:', error);
    }
  };

  const handleThemeToggle = () => {
    // This would be implemented with a theme context
    console.log('Theme toggle clicked');
  };

  const handleSettings = () => {
    console.log('Settings clicked');
  };

  const handleInfo = () => {
    console.log('Info clicked');
  };

  // ========================================================================
  // Helper Functions
  // ========================================================================

  const formatLastUpdated = (timestamp: string | null) => {
    if (!timestamp) return 'Never';
    
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  const getDataStatus = () => {
    if (isLoading) return { label: 'Loading...', color: 'info' as const };
    if (isSuccess && dashboardData) return { label: 'Connected', color: 'success' as const };
    return { label: 'Disconnected', color: 'error' as const };
  };

  const dataStatus = getDataStatus();

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <AppBar 
      position="sticky" 
      elevation={1}
      sx={{
        backgroundColor: alpha(theme.palette.background.paper, 0.95),
        backdropFilter: 'blur(8px)',
        borderBottom: `1px solid ${theme.palette.divider}`,
        color: theme.palette.text.primary,
      }}
    >
      <Toolbar sx={{ justifyContent: 'space-between', minHeight: 64 }}>
        {/* Left Section - Logo and Title */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DashboardIcon 
              sx={{ 
                fontSize: 32, 
                color: theme.palette.primary.main 
              }} 
            />
            <Typography 
              variant="h5" 
              component="h1" 
              sx={{ 
                fontWeight: 600,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {title}
            </Typography>
          </Box>

        </Box>

        {/* Right Section - Refresh Button */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Tooltip title="Refresh Data" arrow>
            <IconButton
              onClick={handleRefresh}
              disabled={Boolean(isLoading)}
              color="inherit"
              aria-label="Refresh data"
              sx={{
                '&:hover': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                },
              }}
            >
              <RefreshIcon
                sx={{
                  animation: Boolean(isLoading) ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  },
                }}
              />
            </IconButton>
          </Tooltip>
        </Box>

      </Toolbar>
    </AppBar>
  );
};

export default Header;
