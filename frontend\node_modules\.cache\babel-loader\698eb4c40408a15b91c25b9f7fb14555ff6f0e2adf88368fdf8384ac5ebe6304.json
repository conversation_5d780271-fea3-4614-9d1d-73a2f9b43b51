{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\pages\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\n/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport { Box, Grid, Typography, Paper, Alert, Fade, useTheme } from '@mui/material';\nimport { CustomerTypeCard, AccountIndustryCard, TeamCard, ACVRangeCard } from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0 // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats = () => {\n    if (!(dashboardData !== null && dashboardData !== void 0 && dashboardData.summary)) return null;\n    const {\n      summary\n    } = dashboardData;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 3,\n      sx: {\n        p: {\n          xs: 3,\n          md: 4\n        },\n        mb: {\n          xs: 3,\n          md: 4\n        },\n        background: `linear-gradient(135deg, ${theme.palette.primary.main}08, ${theme.palette.secondary.main}08)`,\n        border: `1px solid ${theme.palette.divider}`,\n        borderRadius: 3,\n        position: 'relative',\n        overflow: 'hidden',\n        '&::before': {\n          content: '\"\"',\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          height: '4px',\n          background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h4\",\n          fontWeight: 700,\n          sx: {\n            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent'\n          },\n          children: \"\\uD83D\\uDCCA Dashboard Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 11\n        }, this), lastFetched && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            ml: 'auto',\n            textAlign: 'right'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"text.secondary\",\n            children: \"Last updated\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            fontWeight: 500,\n            children: new Date(lastFetched).toLocaleString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: {\n          xs: 2,\n          sm: 3,\n          md: 4\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDC65\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'primary.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: summary.totalCustomers.toLocaleString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Total Customers\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDCB0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'success.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: [\"$\", summary.totalRevenue.toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Total Revenue\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83C\\uDFE2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'info.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: summary.totalTeams\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Active Teams\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: {\n                xs: 2,\n                sm: 3\n              },\n              borderRadius: 2,\n              background: 'rgba(255, 255, 255, 0.7)',\n              border: '1px solid rgba(0, 0, 0, 0.05)',\n              transition: 'all 0.2s ease-in-out',\n              '&:hover': {\n                transform: 'translateY(-2px)',\n                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h2\",\n              sx: {\n                fontSize: '2rem',\n                mb: 1\n              },\n              children: \"\\uD83D\\uDCC8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              sx: {\n                color: 'warning.main',\n                fontWeight: 700,\n                fontSize: {\n                  xs: '1.5rem',\n                  sm: '2rem'\n                },\n                mb: 1\n              },\n              children: [\"$\", Math.round(summary.averageACV).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              fontWeight: 500,\n              sx: {\n                textTransform: 'uppercase',\n                letterSpacing: '0.5px'\n              },\n              children: \"Average ACV\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Loader, {\n          size: \"large\",\n          message: \"Loading dashboard data...\",\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 276,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 275,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          error: error,\n          title: \"Failed to Load Dashboard\",\n          showRetryButton: true,\n          onRetry: refetch,\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return /*#__PURE__*/_jsxDEV(Layout, {\n      title: \"SkyGeni Dashboard\",\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'center',\n          alignItems: 'center',\n          minHeight: '60vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          action: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: refetch,\n            children: \"Retry\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 15\n          }, this),\n          children: \"No dashboard data available. Please check your data sources.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 328,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 327,\n      columnNumber: 7\n    }, this);\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    title: \"SkyGeni Dashboard\",\n    children: /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 500,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            component: \"h1\",\n            gutterBottom: true,\n            fontWeight: 600,\n            color: \"text.primary\",\n            sx: {\n              mb: 0.5\n            },\n            children: \"Data Analytics Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Customer insights and performance metrics\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SummaryStats, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: {\n            xs: 2,\n            sm: 3,\n            md: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 600,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 387,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 700,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 397,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 396,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 800,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(TeamCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            lg: 6,\n            xl: 3,\n            children: /*#__PURE__*/_jsxDEV(Fade, {\n              in: true,\n              timeout: 900,\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: '100%'\n                },\n                children: /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n                  elevation: 3\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 414,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 4,\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"text.secondary\",\n            children: \"Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 358,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 356,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"pF5peWU5QlZmCSoxyU0BtdoBz10=\", false, function () {\n  return [useTheme, useData];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Box", "Grid", "Typography", "Paper", "<PERSON><PERSON>", "Fade", "useTheme", "CustomerTypeCard", "AccountIndustryCard", "TeamCard", "ACVRangeCard", "Layout", "Loader", "ErrorMessage", "useData", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "theme", "dashboardData", "isLoading", "isError", "error", "isEmpty", "refetch", "lastFetched", "autoFetch", "refreshInterval", "SummaryStats", "summary", "elevation", "sx", "p", "xs", "md", "mb", "background", "palette", "primary", "main", "secondary", "border", "divider", "borderRadius", "position", "overflow", "content", "top", "left", "right", "height", "children", "display", "alignItems", "variant", "fontWeight", "dark", "backgroundClip", "WebkitBackgroundClip", "WebkitTextFillColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ml", "textAlign", "color", "Date", "toLocaleString", "container", "spacing", "sm", "item", "transition", "transform", "boxShadow", "fontSize", "totalCustomers", "textTransform", "letterSpacing", "totalRevenue", "totalTeams", "Math", "round", "averageACV", "title", "justifyContent", "minHeight", "size", "message", "centered", "showRetryButton", "onRetry", "severity", "action", "onClick", "in", "timeout", "component", "gutterBottom", "lg", "xl", "mt", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/pages/Dashboard.tsx"], "sourcesContent": ["/**\n * Dashboard Page for SkyGeni Dashboard\n * \n * Main dashboard page that displays:\n * - Overview cards with charts\n * - Data visualization for all data types\n * - Responsive grid layout\n * - Loading and error states\n * - Real-time data updates\n */\n\nimport React from 'react';\nimport {\n  Box,\n  Grid,\n  Typography,\n  Paper,\n  Alert,\n  Fade,\n  useTheme,\n} from '@mui/material';\nimport {\n  CustomerTypeCard,\n  AccountIndustryCard,\n  TeamCard,\n  ACVRangeCard,\n} from '../components/cards';\nimport Layout from '../components/layout/Layout';\nimport Loader from '../components/common/Loader';\nimport ErrorMessage from '../components/common/ErrorMessage';\nimport { useData } from '../hooks/useData';\n\n// ============================================================================\n// Dashboard Component\n// ============================================================================\n\nconst Dashboard: React.FC = () => {\n  const theme = useTheme();\n  const {\n    dashboardData,\n    isLoading,\n    isError,\n    error,\n    isEmpty,\n    refetch,\n    lastFetched,\n  } = useData({\n    autoFetch: true,\n    refreshInterval: 0, // Disable auto-refresh for now\n  });\n\n  // ========================================================================\n  // Summary Statistics Component\n  // ========================================================================\n\n  const SummaryStats: React.FC = () => {\n    if (!dashboardData?.summary) return null;\n\n    const { summary } = dashboardData;\n\n    return (\n      <Paper\n        elevation={3}\n        sx={{\n          p: { xs: 3, md: 4 },\n          mb: { xs: 3, md: 4 },\n          background: `linear-gradient(135deg, ${theme.palette.primary.main}08, ${theme.palette.secondary.main}08)`,\n          border: `1px solid ${theme.palette.divider}`,\n          borderRadius: 3,\n          position: 'relative',\n          overflow: 'hidden',\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            height: '4px',\n            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,\n          },\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n          <Typography variant=\"h4\" fontWeight={700} sx={{\n            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,\n            backgroundClip: 'text',\n            WebkitBackgroundClip: 'text',\n            WebkitTextFillColor: 'transparent',\n          }}>\n            📊 Dashboard Overview\n          </Typography>\n          {lastFetched && (\n            <Box sx={{ ml: 'auto', textAlign: 'right' }}>\n              <Typography variant=\"caption\" color=\"text.secondary\">\n                Last updated\n              </Typography>\n              <Typography variant=\"body2\" fontWeight={500}>\n                {new Date(lastFetched).toLocaleString()}\n              </Typography>\n            </Box>\n          )}\n        </Box>\n\n        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                👥\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'primary.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                {summary.totalCustomers.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Total Customers\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                💰\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'success.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                ${summary.totalRevenue.toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Total Revenue\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                🏢\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'info.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                {summary.totalTeams}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Active Teams\n              </Typography>\n            </Box>\n          </Grid>\n\n          <Grid item xs={6} sm={3}>\n            <Box\n              sx={{\n                textAlign: 'center',\n                p: { xs: 2, sm: 3 },\n                borderRadius: 2,\n                background: 'rgba(255, 255, 255, 0.7)',\n                border: '1px solid rgba(0, 0, 0, 0.05)',\n                transition: 'all 0.2s ease-in-out',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                },\n              }}\n            >\n              <Typography variant=\"h2\" sx={{ fontSize: '2rem', mb: 1 }}>\n                📈\n              </Typography>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  color: 'warning.main',\n                  fontWeight: 700,\n                  fontSize: { xs: '1.5rem', sm: '2rem' },\n                  mb: 1,\n                }}\n              >\n                ${Math.round(summary.averageACV).toLocaleString()}\n              </Typography>\n              <Typography\n                variant=\"body2\"\n                color=\"text.secondary\"\n                fontWeight={500}\n                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}\n              >\n                Average ACV\n              </Typography>\n            </Box>\n          </Grid>\n        </Grid>\n      </Paper>\n    );\n  };\n\n  // ========================================================================\n  // Loading State\n  // ========================================================================\n\n  if (isLoading) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Loader\n            size=\"large\"\n            message=\"Loading dashboard data...\"\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Error State\n  // ========================================================================\n\n  if (isError) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <ErrorMessage\n            error={error}\n            title=\"Failed to Load Dashboard\"\n            showRetryButton={true}\n            onRetry={refetch}\n            centered={true}\n          />\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Empty State\n  // ========================================================================\n\n  if (isEmpty) {\n    return (\n      <Layout title=\"SkyGeni Dashboard\">\n        <Box\n          sx={{\n            display: 'flex',\n            justifyContent: 'center',\n            alignItems: 'center',\n            minHeight: '60vh',\n          }}\n        >\n          <Alert\n            severity=\"info\"\n            action={\n              <button onClick={refetch}>\n                Retry\n              </button>\n            }\n          >\n            No dashboard data available. Please check your data sources.\n          </Alert>\n        </Box>\n      </Layout>\n    );\n  }\n\n  // ========================================================================\n  // Main Dashboard Content\n  // ========================================================================\n\n  return (\n    <Layout title=\"SkyGeni Dashboard\">\n      <Fade in={true} timeout={500}>\n        <Box>\n          {/* Page Header */}\n          <Box sx={{ mb: 3 }}>\n            <Typography\n              variant=\"h5\"\n              component=\"h1\"\n              gutterBottom\n              fontWeight={600}\n              color=\"text.primary\"\n              sx={{ mb: 0.5 }}\n            >\n              Data Analytics Dashboard\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              color=\"text.secondary\"\n            >\n              Customer insights and performance metrics\n            </Typography>\n          </Box>\n\n          {/* Summary Statistics */}\n          <SummaryStats />\n\n          {/* Data Cards Grid */}\n          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>\n            {/* Customer Types Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={600}>\n                <Box sx={{ height: '100%' }}>\n                  <CustomerTypeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Account Industries Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={700}>\n                <Box sx={{ height: '100%' }}>\n                  <AccountIndustryCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* Teams Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={800}>\n                <Box sx={{ height: '100%' }}>\n                  <TeamCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n\n            {/* ACV Ranges Card */}\n            <Grid item xs={12} sm={6} lg={6} xl={3}>\n              <Fade in={true} timeout={900}>\n                <Box sx={{ height: '100%' }}>\n                  <ACVRangeCard elevation={3} />\n                </Box>\n              </Fade>\n            </Grid>\n          </Grid>\n\n          {/* Additional Information */}\n          <Box sx={{ mt: 4, textAlign: 'center' }}>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.\n            </Typography>\n          </Box>\n        </Box>\n      </Fade>\n    </Layout>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,UAAU,EACVC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,QAAQ,QACH,eAAe;AACtB,SACEC,gBAAgB,EAChBC,mBAAmB,EACnBC,QAAQ,EACRC,YAAY,QACP,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,SAASC,OAAO,QAAQ,kBAAkB;;AAE1C;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB,MAAM;IACJc,aAAa;IACbC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC;EACF,CAAC,GAAGZ,OAAO,CAAC;IACVa,SAAS,EAAE,IAAI;IACfC,eAAe,EAAE,CAAC,CAAE;EACtB,CAAC,CAAC;;EAEF;EACA;EACA;;EAEA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,EAACT,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEU,OAAO,GAAE,OAAO,IAAI;IAExC,MAAM;MAAEA;IAAQ,CAAC,GAAGV,aAAa;IAEjC,oBACEJ,OAAA,CAACb,KAAK;MACJ4B,SAAS,EAAE,CAAE;MACbC,EAAE,EAAE;QACFC,CAAC,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACnBC,EAAE,EAAE;UAAEF,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAC;QACpBE,UAAU,EAAE,2BAA2BlB,KAAK,CAACmB,OAAO,CAACC,OAAO,CAACC,IAAI,OAAOrB,KAAK,CAACmB,OAAO,CAACG,SAAS,CAACD,IAAI,KAAK;QACzGE,MAAM,EAAE,aAAavB,KAAK,CAACmB,OAAO,CAACK,OAAO,EAAE;QAC5CC,YAAY,EAAE,CAAC;QACfC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE;UACXC,OAAO,EAAE,IAAI;UACbF,QAAQ,EAAE,UAAU;UACpBG,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,KAAK;UACbd,UAAU,EAAE,0BAA0BlB,KAAK,CAACmB,OAAO,CAACC,OAAO,CAACC,IAAI,KAAKrB,KAAK,CAACmB,OAAO,CAACG,SAAS,CAACD,IAAI;QACnG;MACF,CAAE;MAAAY,QAAA,gBAEFpC,OAAA,CAAChB,GAAG;QAACgC,EAAE,EAAE;UAAEqB,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAElB,EAAE,EAAE;QAAE,CAAE;QAAAgB,QAAA,gBACxDpC,OAAA,CAACd,UAAU;UAACqD,OAAO,EAAC,IAAI;UAACC,UAAU,EAAE,GAAI;UAACxB,EAAE,EAAE;YAC5CK,UAAU,EAAE,2BAA2BlB,KAAK,CAACmB,OAAO,CAACC,OAAO,CAACC,IAAI,KAAKrB,KAAK,CAACmB,OAAO,CAACC,OAAO,CAACkB,IAAI,GAAG;YACnGC,cAAc,EAAE,MAAM;YACtBC,oBAAoB,EAAE,MAAM;YAC5BC,mBAAmB,EAAE;UACvB,CAAE;UAAAR,QAAA,EAAC;QAEH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,EACZtC,WAAW,iBACVV,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEiC,EAAE,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAQ,CAAE;UAAAd,QAAA,gBAC1CpC,OAAA,CAACd,UAAU;YAACqD,OAAO,EAAC,SAAS;YAACY,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAErD;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;YAACqD,OAAO,EAAC,OAAO;YAACC,UAAU,EAAE,GAAI;YAAAJ,QAAA,EACzC,IAAIgB,IAAI,CAAC1C,WAAW,CAAC,CAAC2C,cAAc,CAAC;UAAC;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhD,OAAA,CAACf,IAAI;QAACqE,SAAS;QAACC,OAAO,EAAE;UAAErC,EAAE,EAAE,CAAC;UAAEsC,EAAE,EAAE,CAAC;UAAErC,EAAE,EAAE;QAAE,CAAE;QAAAiB,QAAA,gBAC/CpC,OAAA,CAACf,IAAI;UAACwE,IAAI;UAACvC,EAAE,EAAE,CAAE;UAACsC,EAAE,EAAE,CAAE;UAAApB,QAAA,eACtBpC,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFkC,SAAS,EAAE,QAAQ;cACnBjC,CAAC,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEsC,EAAE,EAAE;cAAE,CAAC;cACnB5B,YAAY,EAAE,CAAC;cACfP,UAAU,EAAE,0BAA0B;cACtCK,MAAM,EAAE,+BAA+B;cACvCgC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAxB,QAAA,gBAEFpC,OAAA,CAACd,UAAU;cAACqD,OAAO,EAAC,IAAI;cAACvB,EAAE,EAAE;gBAAE6C,QAAQ,EAAE,MAAM;gBAAEzC,EAAE,EAAE;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAE1D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,IAAI;cACZvB,EAAE,EAAE;gBACFmC,KAAK,EAAE,cAAc;gBACrBX,UAAU,EAAE,GAAG;gBACfqB,QAAQ,EAAE;kBAAE3C,EAAE,EAAE,QAAQ;kBAAEsC,EAAE,EAAE;gBAAO,CAAC;gBACtCpC,EAAE,EAAE;cACN,CAAE;cAAAgB,QAAA,EAEDtB,OAAO,CAACgD,cAAc,CAACT,cAAc,CAAC;YAAC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,OAAO;cACfY,KAAK,EAAC,gBAAgB;cACtBX,UAAU,EAAE,GAAI;cAChBxB,EAAE,EAAE;gBAAE+C,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAA5B,QAAA,EAC5D;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACwE,IAAI;UAACvC,EAAE,EAAE,CAAE;UAACsC,EAAE,EAAE,CAAE;UAAApB,QAAA,eACtBpC,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFkC,SAAS,EAAE,QAAQ;cACnBjC,CAAC,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEsC,EAAE,EAAE;cAAE,CAAC;cACnB5B,YAAY,EAAE,CAAC;cACfP,UAAU,EAAE,0BAA0B;cACtCK,MAAM,EAAE,+BAA+B;cACvCgC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAxB,QAAA,gBAEFpC,OAAA,CAACd,UAAU;cAACqD,OAAO,EAAC,IAAI;cAACvB,EAAE,EAAE;gBAAE6C,QAAQ,EAAE,MAAM;gBAAEzC,EAAE,EAAE;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAE1D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,IAAI;cACZvB,EAAE,EAAE;gBACFmC,KAAK,EAAE,cAAc;gBACrBX,UAAU,EAAE,GAAG;gBACfqB,QAAQ,EAAE;kBAAE3C,EAAE,EAAE,QAAQ;kBAAEsC,EAAE,EAAE;gBAAO,CAAC;gBACtCpC,EAAE,EAAE;cACN,CAAE;cAAAgB,QAAA,GACH,GACE,EAACtB,OAAO,CAACmD,YAAY,CAACZ,cAAc,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,OAAO;cACfY,KAAK,EAAC,gBAAgB;cACtBX,UAAU,EAAE,GAAI;cAChBxB,EAAE,EAAE;gBAAE+C,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAA5B,QAAA,EAC5D;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACwE,IAAI;UAACvC,EAAE,EAAE,CAAE;UAACsC,EAAE,EAAE,CAAE;UAAApB,QAAA,eACtBpC,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFkC,SAAS,EAAE,QAAQ;cACnBjC,CAAC,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEsC,EAAE,EAAE;cAAE,CAAC;cACnB5B,YAAY,EAAE,CAAC;cACfP,UAAU,EAAE,0BAA0B;cACtCK,MAAM,EAAE,+BAA+B;cACvCgC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAxB,QAAA,gBAEFpC,OAAA,CAACd,UAAU;cAACqD,OAAO,EAAC,IAAI;cAACvB,EAAE,EAAE;gBAAE6C,QAAQ,EAAE,MAAM;gBAAEzC,EAAE,EAAE;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAE1D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,IAAI;cACZvB,EAAE,EAAE;gBACFmC,KAAK,EAAE,WAAW;gBAClBX,UAAU,EAAE,GAAG;gBACfqB,QAAQ,EAAE;kBAAE3C,EAAE,EAAE,QAAQ;kBAAEsC,EAAE,EAAE;gBAAO,CAAC;gBACtCpC,EAAE,EAAE;cACN,CAAE;cAAAgB,QAAA,EAEDtB,OAAO,CAACoD;YAAU;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,OAAO;cACfY,KAAK,EAAC,gBAAgB;cACtBX,UAAU,EAAE,GAAI;cAChBxB,EAAE,EAAE;gBAAE+C,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAA5B,QAAA,EAC5D;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPhD,OAAA,CAACf,IAAI;UAACwE,IAAI;UAACvC,EAAE,EAAE,CAAE;UAACsC,EAAE,EAAE,CAAE;UAAApB,QAAA,eACtBpC,OAAA,CAAChB,GAAG;YACFgC,EAAE,EAAE;cACFkC,SAAS,EAAE,QAAQ;cACnBjC,CAAC,EAAE;gBAAEC,EAAE,EAAE,CAAC;gBAAEsC,EAAE,EAAE;cAAE,CAAC;cACnB5B,YAAY,EAAE,CAAC;cACfP,UAAU,EAAE,0BAA0B;cACtCK,MAAM,EAAE,+BAA+B;cACvCgC,UAAU,EAAE,sBAAsB;cAClC,SAAS,EAAE;gBACTC,SAAS,EAAE,kBAAkB;gBAC7BC,SAAS,EAAE;cACb;YACF,CAAE;YAAAxB,QAAA,gBAEFpC,OAAA,CAACd,UAAU;cAACqD,OAAO,EAAC,IAAI;cAACvB,EAAE,EAAE;gBAAE6C,QAAQ,EAAE,MAAM;gBAAEzC,EAAE,EAAE;cAAE,CAAE;cAAAgB,QAAA,EAAC;YAE1D;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,IAAI;cACZvB,EAAE,EAAE;gBACFmC,KAAK,EAAE,cAAc;gBACrBX,UAAU,EAAE,GAAG;gBACfqB,QAAQ,EAAE;kBAAE3C,EAAE,EAAE,QAAQ;kBAAEsC,EAAE,EAAE;gBAAO,CAAC;gBACtCpC,EAAE,EAAE;cACN,CAAE;cAAAgB,QAAA,GACH,GACE,EAAC+B,IAAI,CAACC,KAAK,CAACtD,OAAO,CAACuD,UAAU,CAAC,CAAChB,cAAc,CAAC,CAAC;YAAA;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACbhD,OAAA,CAACd,UAAU;cACTqD,OAAO,EAAC,OAAO;cACfY,KAAK,EAAC,gBAAgB;cACtBX,UAAU,EAAE,GAAI;cAChBxB,EAAE,EAAE;gBAAE+C,aAAa,EAAE,WAAW;gBAAEC,aAAa,EAAE;cAAQ,CAAE;cAAA5B,QAAA,EAC5D;YAED;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAEZ,CAAC;;EAED;EACA;EACA;;EAEA,IAAI3C,SAAS,EAAE;IACb,oBACEL,OAAA,CAACL,MAAM;MAAC2E,KAAK,EAAC,mBAAmB;MAAAlC,QAAA,eAC/BpC,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFqB,OAAO,EAAE,MAAM;UACfkC,cAAc,EAAE,QAAQ;UACxBjC,UAAU,EAAE,QAAQ;UACpBkC,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,eAEFpC,OAAA,CAACJ,MAAM;UACL6E,IAAI,EAAC,OAAO;UACZC,OAAO,EAAC,2BAA2B;UACnCC,QAAQ,EAAE;QAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAI1C,OAAO,EAAE;IACX,oBACEN,OAAA,CAACL,MAAM;MAAC2E,KAAK,EAAC,mBAAmB;MAAAlC,QAAA,eAC/BpC,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFqB,OAAO,EAAE,MAAM;UACfkC,cAAc,EAAE,QAAQ;UACxBjC,UAAU,EAAE,QAAQ;UACpBkC,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,eAEFpC,OAAA,CAACH,YAAY;UACXU,KAAK,EAAEA,KAAM;UACb+D,KAAK,EAAC,0BAA0B;UAChCM,eAAe,EAAE,IAAK;UACtBC,OAAO,EAAEpE,OAAQ;UACjBkE,QAAQ,EAAE;QAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,IAAIxC,OAAO,EAAE;IACX,oBACER,OAAA,CAACL,MAAM;MAAC2E,KAAK,EAAC,mBAAmB;MAAAlC,QAAA,eAC/BpC,OAAA,CAAChB,GAAG;QACFgC,EAAE,EAAE;UACFqB,OAAO,EAAE,MAAM;UACfkC,cAAc,EAAE,QAAQ;UACxBjC,UAAU,EAAE,QAAQ;UACpBkC,SAAS,EAAE;QACb,CAAE;QAAApC,QAAA,eAEFpC,OAAA,CAACZ,KAAK;UACJ0F,QAAQ,EAAC,MAAM;UACfC,MAAM,eACJ/E,OAAA;YAAQgF,OAAO,EAAEvE,OAAQ;YAAA2B,QAAA,EAAC;UAE1B;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;UAAAZ,QAAA,EACF;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAEb;;EAEA;EACA;EACA;;EAEA,oBACEhD,OAAA,CAACL,MAAM;IAAC2E,KAAK,EAAC,mBAAmB;IAAAlC,QAAA,eAC/BpC,OAAA,CAACX,IAAI;MAAC4F,EAAE,EAAE,IAAK;MAACC,OAAO,EAAE,GAAI;MAAA9C,QAAA,eAC3BpC,OAAA,CAAChB,GAAG;QAAAoD,QAAA,gBAEFpC,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEI,EAAE,EAAE;UAAE,CAAE;UAAAgB,QAAA,gBACjBpC,OAAA,CAACd,UAAU;YACTqD,OAAO,EAAC,IAAI;YACZ4C,SAAS,EAAC,IAAI;YACdC,YAAY;YACZ5C,UAAU,EAAE,GAAI;YAChBW,KAAK,EAAC,cAAc;YACpBnC,EAAE,EAAE;cAAEI,EAAE,EAAE;YAAI,CAAE;YAAAgB,QAAA,EACjB;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhD,OAAA,CAACd,UAAU;YACTqD,OAAO,EAAC,OAAO;YACfY,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EACvB;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNhD,OAAA,CAACa,YAAY;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGhBhD,OAAA,CAACf,IAAI;UAACqE,SAAS;UAACC,OAAO,EAAE;YAAErC,EAAE,EAAE,CAAC;YAAEsC,EAAE,EAAE,CAAC;YAAErC,EAAE,EAAE;UAAE,CAAE;UAAAiB,QAAA,gBAE/CpC,OAAA,CAACf,IAAI;YAACwE,IAAI;YAACvC,EAAE,EAAE,EAAG;YAACsC,EAAE,EAAE,CAAE;YAAC6B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlD,QAAA,eACrCpC,OAAA,CAACX,IAAI;cAAC4F,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAA9C,QAAA,eAC3BpC,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmB,MAAM,EAAE;gBAAO,CAAE;gBAAAC,QAAA,eAC1BpC,OAAA,CAACT,gBAAgB;kBAACwB,SAAS,EAAE;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPhD,OAAA,CAACf,IAAI;YAACwE,IAAI;YAACvC,EAAE,EAAE,EAAG;YAACsC,EAAE,EAAE,CAAE;YAAC6B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlD,QAAA,eACrCpC,OAAA,CAACX,IAAI;cAAC4F,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAA9C,QAAA,eAC3BpC,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmB,MAAM,EAAE;gBAAO,CAAE;gBAAAC,QAAA,eAC1BpC,OAAA,CAACR,mBAAmB;kBAACuB,SAAS,EAAE;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPhD,OAAA,CAACf,IAAI;YAACwE,IAAI;YAACvC,EAAE,EAAE,EAAG;YAACsC,EAAE,EAAE,CAAE;YAAC6B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlD,QAAA,eACrCpC,OAAA,CAACX,IAAI;cAAC4F,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAA9C,QAAA,eAC3BpC,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmB,MAAM,EAAE;gBAAO,CAAE;gBAAAC,QAAA,eAC1BpC,OAAA,CAACP,QAAQ;kBAACsB,SAAS,EAAE;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGPhD,OAAA,CAACf,IAAI;YAACwE,IAAI;YAACvC,EAAE,EAAE,EAAG;YAACsC,EAAE,EAAE,CAAE;YAAC6B,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAlD,QAAA,eACrCpC,OAAA,CAACX,IAAI;cAAC4F,EAAE,EAAE,IAAK;cAACC,OAAO,EAAE,GAAI;cAAA9C,QAAA,eAC3BpC,OAAA,CAAChB,GAAG;gBAACgC,EAAE,EAAE;kBAAEmB,MAAM,EAAE;gBAAO,CAAE;gBAAAC,QAAA,eAC1BpC,OAAA,CAACN,YAAY;kBAACqB,SAAS,EAAE;gBAAE;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGPhD,OAAA,CAAChB,GAAG;UAACgC,EAAE,EAAE;YAAEuE,EAAE,EAAE,CAAC;YAAErC,SAAS,EAAE;UAAS,CAAE;UAAAd,QAAA,eACtCpC,OAAA,CAACd,UAAU;YAACqD,OAAO,EAAC,OAAO;YAACY,KAAK,EAAC,gBAAgB;YAAAf,QAAA,EAAC;UAEnD;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEb,CAAC;AAAC9C,EAAA,CA1YID,SAAmB;EAAA,QACTX,QAAQ,EASlBQ,OAAO;AAAA;AAAA0F,EAAA,GAVPvF,SAAmB;AA4YzB,eAAeA,SAAS;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}