{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\charts\\\\DoughnutChart.tsx\",\n  _s = $RefreshSig$();\n/**\n * DoughnutChart Component for SkyGeni Dashboard\n * \n * A responsive D3.js-powered doughnut chart component with:\n * - Interactive hover effects\n * - Smooth animations\n * - Center text display\n * - Legend support\n * - Tooltip support\n * - Responsive design\n * - TypeScript support\n */\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as d3 from 'd3';\nimport { Box, Typography, useTheme } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_CONFIG = {\n  width: 400,\n  height: 400,\n  margin: {\n    top: 20,\n    right: 20,\n    bottom: 20,\n    left: 20\n  },\n  innerRadius: 60,\n  outerRadius: 150,\n  padAngle: 0.02,\n  cornerRadius: 3,\n  showLabels: true,\n  showLegend: false,\n  showTooltip: true,\n  centerText: ''\n};\n\n// ============================================================================\n// DoughnutChart Component\n// ============================================================================\n\nconst DoughnutChart = ({\n  data,\n  config = {},\n  animation = {\n    duration: 750,\n    delay: 0\n  },\n  colorScheme,\n  onSegmentClick,\n  onSegmentHover,\n  className,\n  title\n}) => {\n  _s();\n  const theme = useTheme();\n  const svgRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const [tooltip, setTooltip] = useState({\n    data: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    visible: false\n  });\n\n  // Merge default config with provided config\n  const chartConfig = {\n    ...DEFAULT_CONFIG,\n    ...config\n  };\n\n  // Calculate chart dimensions\n  const dimensions = {\n    width: chartConfig.width,\n    height: chartConfig.height,\n    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,\n    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom\n  };\n\n  // Calculate center position\n  const centerX = dimensions.innerWidth / 2;\n  const centerY = dimensions.innerHeight / 2;\n  const radius = Math.min(centerX, centerY);\n\n  // Adjust radii based on available space\n  const innerRadius = Math.min(chartConfig.innerRadius, radius * 0.4);\n  const outerRadius = Math.min(chartConfig.outerRadius, radius * 0.8);\n\n  // Default color scheme\n  const colors = colorScheme || {\n    primary: [theme.palette.primary.main, theme.palette.secondary.main, '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'],\n    secondary: [],\n    background: theme.palette.background.paper,\n    text: theme.palette.text.primary,\n    grid: theme.palette.divider,\n    axis: theme.palette.text.secondary\n  };\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  const processedData = React.useMemo(() => {\n    const pie = d3.pie().value(d => d.value).sort(null).padAngle(chartConfig.padAngle);\n    const arcs = pie(data.segments);\n    return arcs.map((arc, index) => ({\n      ...arc.data,\n      startAngle: arc.startAngle,\n      endAngle: arc.endAngle,\n      index,\n      data: arc.data,\n      color: arc.data.color || colors.primary[index % colors.primary.length]\n    }));\n  }, [data.segments, chartConfig.padAngle, colors.primary]);\n\n  // ========================================================================\n  // D3 Scales and Generators\n  // ========================================================================\n\n  const scales = React.useMemo(() => {\n    const colorScale = d3.scaleOrdinal().domain(processedData.map(d => d.label)).range(colors.primary);\n    const arcGenerator = d3.arc().innerRadius(innerRadius).outerRadius(outerRadius).cornerRadius(chartConfig.cornerRadius);\n    const pieGenerator = d3.pie().value(d => d.value).sort(null).padAngle(chartConfig.padAngle);\n    return {\n      color: colorScale,\n      arc: arcGenerator,\n      pie: pieGenerator\n    };\n  }, [processedData, colors.primary, innerRadius, outerRadius, chartConfig.cornerRadius, chartConfig.padAngle]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleSegmentMouseEnter = useCallback((event, d) => {\n    var _svgRef$current;\n    // Prevent event bubbling\n    event.stopPropagation();\n    const rect = (_svgRef$current = svgRef.current) === null || _svgRef$current === void 0 ? void 0 : _svgRef$current.getBoundingClientRect();\n    if (!rect) return;\n    const tooltipData = {\n      title: d.label,\n      value: d.value,\n      percentage: d.percentage,\n      color: d.color\n    };\n    setTooltip({\n      data: tooltipData,\n      position: {\n        x: event.clientX - rect.left,\n        y: event.clientY - rect.top\n      },\n      visible: true\n    });\n    onSegmentHover === null || onSegmentHover === void 0 ? void 0 : onSegmentHover(d, event);\n  }, [onSegmentHover]);\n  const handleSegmentMouseLeave = useCallback(event => {\n    // Prevent event bubbling\n    event.stopPropagation();\n\n    // Add small delay to prevent flicker\n    setTimeout(() => {\n      setTooltip(prev => ({\n        ...prev,\n        visible: false\n      }));\n    }, 50);\n    onSegmentHover === null || onSegmentHover === void 0 ? void 0 : onSegmentHover(null, event);\n  }, [onSegmentHover]);\n  const handleSegmentClick = useCallback((event, d) => {\n    onSegmentClick === null || onSegmentClick === void 0 ? void 0 : onSegmentClick(d, event);\n  }, [onSegmentClick]);\n\n  // ========================================================================\n  // Chart Rendering\n  // ========================================================================\n\n  useEffect(() => {\n    if (!svgRef.current || processedData.length === 0) return;\n    const svg = d3.select(svgRef.current);\n\n    // Clear previous content\n    svg.selectAll('*').remove();\n\n    // Create main group with margins and center translation\n    const g = svg.append('g').attr('transform', `translate(${chartConfig.margin.left + centerX},${chartConfig.margin.top + centerY})`);\n\n    // Create segments\n    const segments = g.selectAll('.segment').data(processedData).enter().append('path').attr('class', 'segment').attr('fill', d => d.color).attr('stroke', colors.background).attr('stroke-width', 2).style('cursor', 'pointer').on('mouseenter', function (event, d) {\n      // Simple highlight effect\n      d3.select(this).attr('stroke-width', 3).style('opacity', 0.8);\n      handleSegmentMouseEnter(event, d);\n    }).on('mouseleave', function (event, d) {\n      // Reset highlight\n      d3.select(this).attr('stroke-width', 2).style('opacity', 1);\n      handleSegmentMouseLeave(event);\n    }).on('click', handleSegmentClick);\n\n    // Animate segments\n    segments.attr('d', scales.arc).style('opacity', 0).transition().duration(animation.duration).delay((d, i) => (animation.delay || 0) + i * 100).ease(d3.easeBackOut.overshoot(1.1)).style('opacity', 1).attrTween('d', function (d) {\n      const interpolate = d3.interpolate({\n        startAngle: 0,\n        endAngle: 0\n      }, d);\n      return function (t) {\n        return scales.arc(interpolate(t)) || '';\n      };\n    });\n\n    // Add labels if enabled\n    if (chartConfig.showLabels) {\n      const labelArc = d3.arc().innerRadius(outerRadius + 10).outerRadius(outerRadius + 10);\n      const labels = g.selectAll('.label').data(processedData).enter().append('text').attr('class', 'label').attr('text-anchor', 'middle').attr('dominant-baseline', 'middle').style('fill', colors.text).style('font-size', '12px').style('font-weight', 'bold').style('opacity', 0).text(d => {\n        var _d$percentage;\n        return `${d.label} (${(_d$percentage = d.percentage) === null || _d$percentage === void 0 ? void 0 : _d$percentage.toFixed(1)}%)`;\n      });\n\n      // Position and animate labels\n      labels.transition().duration(animation.duration).delay((d, i) => (animation.delay || 0) + i * 100 + 300).attr('transform', d => {\n        const centroid = labelArc.centroid(d);\n        return `translate(${centroid[0]}, ${centroid[1]})`;\n      }).style('opacity', 1);\n    }\n\n    // Add center text if provided\n    if (chartConfig.centerText || data.centerLabel) {\n      const centerText = g.append('text').attr('class', 'center-text').attr('text-anchor', 'middle').attr('dominant-baseline', 'middle').style('fill', colors.text).style('font-size', '14px').style('font-weight', '600').style('opacity', 0).text(chartConfig.centerText || data.centerLabel || '');\n      centerText.transition().duration(animation.duration).delay(animation.delay || 0).style('opacity', 1);\n    }\n  }, [processedData, scales, dimensions, chartConfig, colors, animation, centerX, centerY, outerRadius, data.centerLabel, handleSegmentMouseEnter, handleSegmentMouseLeave, handleSegmentClick]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: className,\n    sx: {\n      position: 'relative',\n      width: '100%'\n    },\n    children: [title && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      component: \"h3\",\n      gutterBottom: true,\n      align: \"center\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        ref: svgRef,\n        width: dimensions.width,\n        height: dimensions.height,\n        style: {\n          background: colors.background,\n          borderRadius: theme.shape.borderRadius,\n          boxShadow: theme.shadows[1]\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 348,\n        columnNumber: 9\n      }, this), tooltip.visible && tooltip.data && /*#__PURE__*/_jsxDEV(Box, {\n        ref: tooltipRef,\n        sx: {\n          position: 'absolute',\n          left: tooltip.position.x + 10,\n          top: tooltip.position.y - 10,\n          background: theme.palette.background.paper,\n          border: `1px solid ${theme.palette.divider}`,\n          borderRadius: 1,\n          padding: 1,\n          boxShadow: theme.shadows[3],\n          pointerEvents: 'none',\n          zIndex: 1000,\n          fontSize: '0.875rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: tooltip.data.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Value: \", tooltip.data.value.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), tooltip.data.percentage !== undefined && /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Percentage: \", tooltip.data.percentage.toFixed(1), \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 340,\n    columnNumber: 5\n  }, this);\n};\n_s(DoughnutChart, \"OCv3NYxAIx+AdQK3NYaixJHYCC0=\", false, function () {\n  return [useTheme];\n});\n_c = DoughnutChart;\nexport default DoughnutChart;\nvar _c;\n$RefreshReg$(_c, \"DoughnutChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "d3", "Box", "Typography", "useTheme", "jsxDEV", "_jsxDEV", "DEFAULT_CONFIG", "width", "height", "margin", "top", "right", "bottom", "left", "innerRadius", "outerRadius", "padAngle", "cornerRadius", "showLabels", "showLegend", "showTooltip", "centerText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "config", "animation", "duration", "delay", "colorScheme", "onSegmentClick", "onSegmentHover", "className", "title", "_s", "theme", "svgRef", "tooltipRef", "tooltip", "setTooltip", "position", "x", "y", "visible", "chartConfig", "dimensions", "innerWidth", "innerHeight", "centerX", "centerY", "radius", "Math", "min", "colors", "primary", "palette", "main", "secondary", "background", "paper", "text", "grid", "divider", "axis", "processedData", "useMemo", "pie", "value", "d", "sort", "arcs", "segments", "map", "arc", "index", "startAngle", "endAngle", "color", "length", "scales", "colorScale", "scaleOrdinal", "domain", "label", "range", "arcGenerator", "pieGenerator", "handleSegmentMouseEnter", "event", "_svgRef$current", "stopPropagation", "rect", "current", "getBoundingClientRect", "tooltipData", "percentage", "clientX", "clientY", "handleSegmentMouseLeave", "setTimeout", "prev", "handleSegmentClick", "svg", "select", "selectAll", "remove", "g", "append", "attr", "enter", "style", "on", "transition", "i", "ease", "easeBackOut", "overshoot", "attrTween", "interpolate", "t", "labelArc", "labels", "_d$percentage", "toFixed", "centroid", "centerLabel", "sx", "children", "variant", "component", "gutterBottom", "align", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "ref", "borderRadius", "shape", "boxShadow", "shadows", "border", "padding", "pointerEvents", "zIndex", "fontSize", "fontWeight", "toLocaleString", "undefined", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/charts/DoughnutChart.tsx"], "sourcesContent": ["/**\n * DoughnutChart Component for SkyGeni Dashboard\n * \n * A responsive D3.js-powered doughnut chart component with:\n * - Interactive hover effects\n * - Smooth animations\n * - Center text display\n * - Legend support\n * - Tooltip support\n * - Responsive design\n * - TypeScript support\n */\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as d3 from 'd3';\nimport { Box, Typography, useTheme } from '@mui/material';\nimport {\n  DoughnutChartProps,\n  DoughnutChartConfig,\n  DoughnutChartDataPoint,\n  DoughnutChartScales,\n  ChartDimensions,\n  TooltipData,\n  D3Selection,\n  D3GSelection\n} from './types';\n\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_CONFIG: <PERSON>hnutChartConfig = {\n  width: 400,\n  height: 400,\n  margin: { top: 20, right: 20, bottom: 20, left: 20 },\n  innerRadius: 60,\n  outerRadius: 150,\n  padAngle: 0.02,\n  cornerRadius: 3,\n  showLabels: true,\n  showLegend: false,\n  showTooltip: true,\n  centerText: '',\n};\n\n// ============================================================================\n// DoughnutChart Component\n// ============================================================================\n\nconst DoughnutChart: React.FC<DoughnutChartProps> = ({\n  data,\n  config = {},\n  animation = { duration: 750, delay: 0 },\n  colorScheme,\n  onSegmentClick,\n  onSegmentHover,\n  className,\n  title,\n}) => {\n  const theme = useTheme();\n  const svgRef = useRef<SVGSVGElement>(null);\n  const tooltipRef = useRef<HTMLDivElement>(null);\n  const [tooltip, setTooltip] = useState<{\n    data: TooltipData | null;\n    position: { x: number; y: number };\n    visible: boolean;\n  }>({\n    data: null,\n    position: { x: 0, y: 0 },\n    visible: false,\n  });\n\n  // Merge default config with provided config\n  const chartConfig: DoughnutChartConfig = { ...DEFAULT_CONFIG, ...config };\n\n  // Calculate chart dimensions\n  const dimensions: ChartDimensions = {\n    width: chartConfig.width,\n    height: chartConfig.height,\n    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,\n    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom,\n  };\n\n  // Calculate center position\n  const centerX = dimensions.innerWidth / 2;\n  const centerY = dimensions.innerHeight / 2;\n  const radius = Math.min(centerX, centerY);\n\n  // Adjust radii based on available space\n  const innerRadius = Math.min(chartConfig.innerRadius, radius * 0.4);\n  const outerRadius = Math.min(chartConfig.outerRadius, radius * 0.8);\n\n  // Default color scheme\n  const colors = colorScheme || {\n    primary: [\n      theme.palette.primary.main,\n      theme.palette.secondary.main,\n      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',\n      '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'\n    ],\n    secondary: [],\n    background: theme.palette.background.paper,\n    text: theme.palette.text.primary,\n    grid: theme.palette.divider,\n    axis: theme.palette.text.secondary,\n  };\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  const processedData: DoughnutChartDataPoint[] = React.useMemo(() => {\n    const pie = d3.pie<any, any>()\n      .value(d => d.value)\n      .sort(null)\n      .padAngle(chartConfig.padAngle);\n\n    const arcs = pie(data.segments);\n\n    return arcs.map((arc, index) => ({\n      ...arc.data,\n      startAngle: arc.startAngle,\n      endAngle: arc.endAngle,\n      index,\n      data: arc.data,\n      color: arc.data.color || colors.primary[index % colors.primary.length],\n    }));\n  }, [data.segments, chartConfig.padAngle, colors.primary]);\n\n  // ========================================================================\n  // D3 Scales and Generators\n  // ========================================================================\n\n  const scales: DoughnutChartScales = React.useMemo(() => {\n    const colorScale = d3\n      .scaleOrdinal<string>()\n      .domain(processedData.map(d => d.label))\n      .range(colors.primary);\n\n    const arcGenerator = d3\n      .arc<DoughnutChartDataPoint>()\n      .innerRadius(innerRadius)\n      .outerRadius(outerRadius)\n      .cornerRadius(chartConfig.cornerRadius);\n\n    const pieGenerator = d3.pie<any, any>()\n      .value(d => d.value)\n      .sort(null)\n      .padAngle(chartConfig.padAngle);\n\n    return { \n      color: colorScale, \n      arc: arcGenerator, \n      pie: pieGenerator \n    };\n  }, [processedData, colors.primary, innerRadius, outerRadius, chartConfig.cornerRadius, chartConfig.padAngle]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleSegmentMouseEnter = useCallback((event: MouseEvent, d: DoughnutChartDataPoint) => {\n    // Prevent event bubbling\n    event.stopPropagation();\n\n    const rect = svgRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    const tooltipData: TooltipData = {\n      title: d.label,\n      value: d.value,\n      percentage: d.percentage,\n      color: d.color,\n    };\n\n    setTooltip({\n      data: tooltipData,\n      position: {\n        x: event.clientX - rect.left,\n        y: event.clientY - rect.top,\n      },\n      visible: true,\n    });\n\n    onSegmentHover?.(d, event);\n  }, [onSegmentHover]);\n\n  const handleSegmentMouseLeave = useCallback((event: MouseEvent) => {\n    // Prevent event bubbling\n    event.stopPropagation();\n\n    // Add small delay to prevent flicker\n    setTimeout(() => {\n      setTooltip(prev => ({ ...prev, visible: false }));\n    }, 50);\n\n    onSegmentHover?.(null, event);\n  }, [onSegmentHover]);\n\n  const handleSegmentClick = useCallback((event: MouseEvent, d: DoughnutChartDataPoint) => {\n    onSegmentClick?.(d, event);\n  }, [onSegmentClick]);\n\n  // ========================================================================\n  // Chart Rendering\n  // ========================================================================\n\n  useEffect(() => {\n    if (!svgRef.current || processedData.length === 0) return;\n\n    const svg = d3.select(svgRef.current);\n\n    // Clear previous content\n    svg.selectAll('*').remove();\n\n    // Create main group with margins and center translation\n    const g = svg\n      .append('g')\n      .attr('transform', `translate(${chartConfig.margin.left + centerX},${chartConfig.margin.top + centerY})`);\n\n    // Create segments\n    const segments = g\n      .selectAll('.segment')\n      .data(processedData)\n      .enter()\n      .append('path')\n      .attr('class', 'segment')\n      .attr('fill', d => d.color)\n      .attr('stroke', colors.background)\n      .attr('stroke-width', 2)\n      .style('cursor', 'pointer')\n      .on('mouseenter', function(event, d) {\n        // Simple highlight effect\n        d3.select(this)\n          .attr('stroke-width', 3)\n          .style('opacity', 0.8);\n\n        handleSegmentMouseEnter(event, d);\n      })\n      .on('mouseleave', function(event, d) {\n        // Reset highlight\n        d3.select(this)\n          .attr('stroke-width', 2)\n          .style('opacity', 1);\n\n        handleSegmentMouseLeave(event);\n      })\n      .on('click', handleSegmentClick);\n\n    // Animate segments\n    segments\n      .attr('d', scales.arc)\n      .style('opacity', 0)\n      .transition()\n      .duration(animation.duration)\n      .delay((d, i) => (animation.delay || 0) + i * 100)\n      .ease(d3.easeBackOut.overshoot(1.1))\n      .style('opacity', 1)\n      .attrTween('d', function(d) {\n        const interpolate = d3.interpolate({ startAngle: 0, endAngle: 0 }, d);\n        return function(t) {\n          return scales.arc(interpolate(t)) || '';\n        };\n      });\n\n    // Add labels if enabled\n    if (chartConfig.showLabels) {\n      const labelArc = d3\n        .arc<DoughnutChartDataPoint>()\n        .innerRadius(outerRadius + 10)\n        .outerRadius(outerRadius + 10);\n\n      const labels = g\n        .selectAll('.label')\n        .data(processedData)\n        .enter()\n        .append('text')\n        .attr('class', 'label')\n        .attr('text-anchor', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .style('fill', colors.text)\n        .style('font-size', '12px')\n        .style('font-weight', 'bold')\n        .style('opacity', 0)\n        .text(d => `${d.label} (${d.percentage?.toFixed(1)}%)`);\n\n      // Position and animate labels\n      labels\n        .transition()\n        .duration(animation.duration)\n        .delay((d, i) => (animation.delay || 0) + i * 100 + 300)\n        .attr('transform', d => {\n          const centroid = labelArc.centroid(d);\n          return `translate(${centroid[0]}, ${centroid[1]})`;\n        })\n        .style('opacity', 1);\n    }\n\n    // Add center text if provided\n    if (chartConfig.centerText || data.centerLabel) {\n      const centerText = g\n        .append('text')\n        .attr('class', 'center-text')\n        .attr('text-anchor', 'middle')\n        .attr('dominant-baseline', 'middle')\n        .style('fill', colors.text)\n        .style('font-size', '14px')\n        .style('font-weight', '600')\n        .style('opacity', 0)\n        .text(chartConfig.centerText || data.centerLabel || '');\n\n      centerText\n        .transition()\n        .duration(animation.duration)\n        .delay(animation.delay || 0)\n        .style('opacity', 1);\n    }\n\n  }, [\n    processedData,\n    scales,\n    dimensions,\n    chartConfig,\n    colors,\n    animation,\n    centerX,\n    centerY,\n    outerRadius,\n    data.centerLabel,\n    handleSegmentMouseEnter,\n    handleSegmentMouseLeave,\n    handleSegmentClick,\n  ]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return (\n    <Box className={className} sx={{ position: 'relative', width: '100%' }}>\n      {title && (\n        <Typography variant=\"h6\" component=\"h3\" gutterBottom align=\"center\">\n          {title}\n        </Typography>\n      )}\n\n      <Box sx={{ position: 'relative', display: 'inline-block' }}>\n        <svg\n          ref={svgRef}\n          width={dimensions.width}\n          height={dimensions.height}\n          style={{\n            background: colors.background,\n            borderRadius: theme.shape.borderRadius,\n            boxShadow: theme.shadows[1],\n          }}\n        />\n\n        {/* Tooltip */}\n        {tooltip.visible && tooltip.data && (\n          <Box\n            ref={tooltipRef}\n            sx={{\n              position: 'absolute',\n              left: tooltip.position.x + 10,\n              top: tooltip.position.y - 10,\n              background: theme.palette.background.paper,\n              border: `1px solid ${theme.palette.divider}`,\n              borderRadius: 1,\n              padding: 1,\n              boxShadow: theme.shadows[3],\n              pointerEvents: 'none',\n              zIndex: 1000,\n              fontSize: '0.875rem',\n            }}\n          >\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              {tooltip.data.title}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Value: {tooltip.data.value.toLocaleString()}\n            </Typography>\n            {tooltip.data.percentage !== undefined && (\n              <Typography variant=\"body2\" color=\"text.secondary\">\n                Percentage: {tooltip.data.percentage.toFixed(1)}%\n              </Typography>\n            )}\n          </Box>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default DoughnutChart;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,SAASC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1D;AACA;AACA;;AAEA,MAAMC,cAAmC,GAAG;EAC1CC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE;IAAEC,GAAG,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC;EACpDC,WAAW,EAAE,EAAE;EACfC,WAAW,EAAE,GAAG;EAChBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,KAAK;EACjBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;;AAEA,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,IAAI;EACJC,MAAM,GAAG,CAAC,CAAC;EACXC,SAAS,GAAG;IAAEC,QAAQ,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EACvCC,WAAW;EACXC,cAAc;EACdC,cAAc;EACdC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAG/B,QAAQ,CAAC,CAAC;EACxB,MAAMgC,MAAM,GAAGtC,MAAM,CAAgB,IAAI,CAAC;EAC1C,MAAMuC,UAAU,GAAGvC,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAInC;IACDyB,IAAI,EAAE,IAAI;IACVgB,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACxBC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,WAAgC,GAAG;IAAE,GAAGrC,cAAc;IAAE,GAAGkB;EAAO,CAAC;;EAEzE;EACA,MAAMoB,UAA2B,GAAG;IAClCrC,KAAK,EAAEoC,WAAW,CAACpC,KAAK;IACxBC,MAAM,EAAEmC,WAAW,CAACnC,MAAM;IAC1BqC,UAAU,EAAEF,WAAW,CAACpC,KAAK,GAAGoC,WAAW,CAAClC,MAAM,CAACI,IAAI,GAAG8B,WAAW,CAAClC,MAAM,CAACE,KAAK;IAClFmC,WAAW,EAAEH,WAAW,CAACnC,MAAM,GAAGmC,WAAW,CAAClC,MAAM,CAACC,GAAG,GAAGiC,WAAW,CAAClC,MAAM,CAACG;EAChF,CAAC;;EAED;EACA,MAAMmC,OAAO,GAAGH,UAAU,CAACC,UAAU,GAAG,CAAC;EACzC,MAAMG,OAAO,GAAGJ,UAAU,CAACE,WAAW,GAAG,CAAC;EAC1C,MAAMG,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,OAAO,EAAEC,OAAO,CAAC;;EAEzC;EACA,MAAMlC,WAAW,GAAGoC,IAAI,CAACC,GAAG,CAACR,WAAW,CAAC7B,WAAW,EAAEmC,MAAM,GAAG,GAAG,CAAC;EACnE,MAAMlC,WAAW,GAAGmC,IAAI,CAACC,GAAG,CAACR,WAAW,CAAC5B,WAAW,EAAEkC,MAAM,GAAG,GAAG,CAAC;;EAEnE;EACA,MAAMG,MAAM,GAAGxB,WAAW,IAAI;IAC5ByB,OAAO,EAAE,CACPnB,KAAK,CAACoB,OAAO,CAACD,OAAO,CAACE,IAAI,EAC1BrB,KAAK,CAACoB,OAAO,CAACE,SAAS,CAACD,IAAI,EAC5B,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACDC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAEvB,KAAK,CAACoB,OAAO,CAACG,UAAU,CAACC,KAAK;IAC1CC,IAAI,EAAEzB,KAAK,CAACoB,OAAO,CAACK,IAAI,CAACN,OAAO;IAChCO,IAAI,EAAE1B,KAAK,CAACoB,OAAO,CAACO,OAAO;IAC3BC,IAAI,EAAE5B,KAAK,CAACoB,OAAO,CAACK,IAAI,CAACH;EAC3B,CAAC;;EAED;EACA;EACA;;EAEA,MAAMO,aAAuC,GAAGpE,KAAK,CAACqE,OAAO,CAAC,MAAM;IAClE,MAAMC,GAAG,GAAGjE,EAAE,CAACiE,GAAG,CAAW,CAAC,CAC3BC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACD,KAAK,CAAC,CACnBE,IAAI,CAAC,IAAI,CAAC,CACVpD,QAAQ,CAAC2B,WAAW,CAAC3B,QAAQ,CAAC;IAEjC,MAAMqD,IAAI,GAAGJ,GAAG,CAAC1C,IAAI,CAAC+C,QAAQ,CAAC;IAE/B,OAAOD,IAAI,CAACE,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;MAC/B,GAAGD,GAAG,CAACjD,IAAI;MACXmD,UAAU,EAAEF,GAAG,CAACE,UAAU;MAC1BC,QAAQ,EAAEH,GAAG,CAACG,QAAQ;MACtBF,KAAK;MACLlD,IAAI,EAAEiD,GAAG,CAACjD,IAAI;MACdqD,KAAK,EAAEJ,GAAG,CAACjD,IAAI,CAACqD,KAAK,IAAIxB,MAAM,CAACC,OAAO,CAACoB,KAAK,GAAGrB,MAAM,CAACC,OAAO,CAACwB,MAAM;IACvE,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACtD,IAAI,CAAC+C,QAAQ,EAAE3B,WAAW,CAAC3B,QAAQ,EAAEoC,MAAM,CAACC,OAAO,CAAC,CAAC;;EAEzD;EACA;EACA;;EAEA,MAAMyB,MAA2B,GAAGnF,KAAK,CAACqE,OAAO,CAAC,MAAM;IACtD,MAAMe,UAAU,GAAG/E,EAAE,CAClBgF,YAAY,CAAS,CAAC,CACtBC,MAAM,CAAClB,aAAa,CAACQ,GAAG,CAACJ,CAAC,IAAIA,CAAC,CAACe,KAAK,CAAC,CAAC,CACvCC,KAAK,CAAC/B,MAAM,CAACC,OAAO,CAAC;IAExB,MAAM+B,YAAY,GAAGpF,EAAE,CACpBwE,GAAG,CAAyB,CAAC,CAC7B1D,WAAW,CAACA,WAAW,CAAC,CACxBC,WAAW,CAACA,WAAW,CAAC,CACxBE,YAAY,CAAC0B,WAAW,CAAC1B,YAAY,CAAC;IAEzC,MAAMoE,YAAY,GAAGrF,EAAE,CAACiE,GAAG,CAAW,CAAC,CACpCC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAACD,KAAK,CAAC,CACnBE,IAAI,CAAC,IAAI,CAAC,CACVpD,QAAQ,CAAC2B,WAAW,CAAC3B,QAAQ,CAAC;IAEjC,OAAO;MACL4D,KAAK,EAAEG,UAAU;MACjBP,GAAG,EAAEY,YAAY;MACjBnB,GAAG,EAAEoB;IACP,CAAC;EACH,CAAC,EAAE,CAACtB,aAAa,EAAEX,MAAM,CAACC,OAAO,EAAEvC,WAAW,EAAEC,WAAW,EAAE4B,WAAW,CAAC1B,YAAY,EAAE0B,WAAW,CAAC3B,QAAQ,CAAC,CAAC;;EAE7G;EACA;EACA;;EAEA,MAAMsE,uBAAuB,GAAGvF,WAAW,CAAC,CAACwF,KAAiB,EAAEpB,CAAyB,KAAK;IAAA,IAAAqB,eAAA;IAC5F;IACAD,KAAK,CAACE,eAAe,CAAC,CAAC;IAEvB,MAAMC,IAAI,IAAAF,eAAA,GAAGrD,MAAM,CAACwD,OAAO,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,qBAAqB,CAAC,CAAC;IACpD,IAAI,CAACF,IAAI,EAAE;IAEX,MAAMG,WAAwB,GAAG;MAC/B7D,KAAK,EAAEmC,CAAC,CAACe,KAAK;MACdhB,KAAK,EAAEC,CAAC,CAACD,KAAK;MACd4B,UAAU,EAAE3B,CAAC,CAAC2B,UAAU;MACxBlB,KAAK,EAAET,CAAC,CAACS;IACX,CAAC;IAEDtC,UAAU,CAAC;MACTf,IAAI,EAAEsE,WAAW;MACjBtD,QAAQ,EAAE;QACRC,CAAC,EAAE+C,KAAK,CAACQ,OAAO,GAAGL,IAAI,CAAC7E,IAAI;QAC5B4B,CAAC,EAAE8C,KAAK,CAACS,OAAO,GAAGN,IAAI,CAAChF;MAC1B,CAAC;MACDgC,OAAO,EAAE;IACX,CAAC,CAAC;IAEFZ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGqC,CAAC,EAAEoB,KAAK,CAAC;EAC5B,CAAC,EAAE,CAACzD,cAAc,CAAC,CAAC;EAEpB,MAAMmE,uBAAuB,GAAGlG,WAAW,CAAEwF,KAAiB,IAAK;IACjE;IACAA,KAAK,CAACE,eAAe,CAAC,CAAC;;IAEvB;IACAS,UAAU,CAAC,MAAM;MACf5D,UAAU,CAAC6D,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAEzD,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnD,CAAC,EAAE,EAAE,CAAC;IAENZ,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,IAAI,EAAEyD,KAAK,CAAC;EAC/B,CAAC,EAAE,CAACzD,cAAc,CAAC,CAAC;EAEpB,MAAMsE,kBAAkB,GAAGrG,WAAW,CAAC,CAACwF,KAAiB,EAAEpB,CAAyB,KAAK;IACvFtC,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAGsC,CAAC,EAAEoB,KAAK,CAAC;EAC5B,CAAC,EAAE,CAAC1D,cAAc,CAAC,CAAC;;EAEpB;EACA;EACA;;EAEAjC,SAAS,CAAC,MAAM;IACd,IAAI,CAACuC,MAAM,CAACwD,OAAO,IAAI5B,aAAa,CAACc,MAAM,KAAK,CAAC,EAAE;IAEnD,MAAMwB,GAAG,GAAGrG,EAAE,CAACsG,MAAM,CAACnE,MAAM,CAACwD,OAAO,CAAC;;IAErC;IACAU,GAAG,CAACE,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC;;IAE3B;IACA,MAAMC,CAAC,GAAGJ,GAAG,CACVK,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,WAAW,EAAE,aAAahE,WAAW,CAAClC,MAAM,CAACI,IAAI,GAAGkC,OAAO,IAAIJ,WAAW,CAAClC,MAAM,CAACC,GAAG,GAAGsC,OAAO,GAAG,CAAC;;IAE3G;IACA,MAAMsB,QAAQ,GAAGmC,CAAC,CACfF,SAAS,CAAC,UAAU,CAAC,CACrBhF,IAAI,CAACwC,aAAa,CAAC,CACnB6C,KAAK,CAAC,CAAC,CACPF,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CACxBA,IAAI,CAAC,MAAM,EAAExC,CAAC,IAAIA,CAAC,CAACS,KAAK,CAAC,CAC1B+B,IAAI,CAAC,QAAQ,EAAEvD,MAAM,CAACK,UAAU,CAAC,CACjCkD,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACvBE,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAC1BC,EAAE,CAAC,YAAY,EAAE,UAASvB,KAAK,EAAEpB,CAAC,EAAE;MACnC;MACAnE,EAAE,CAACsG,MAAM,CAAC,IAAI,CAAC,CACZK,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACvBE,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC;MAExBvB,uBAAuB,CAACC,KAAK,EAAEpB,CAAC,CAAC;IACnC,CAAC,CAAC,CACD2C,EAAE,CAAC,YAAY,EAAE,UAASvB,KAAK,EAAEpB,CAAC,EAAE;MACnC;MACAnE,EAAE,CAACsG,MAAM,CAAC,IAAI,CAAC,CACZK,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACvBE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;MAEtBZ,uBAAuB,CAACV,KAAK,CAAC;IAChC,CAAC,CAAC,CACDuB,EAAE,CAAC,OAAO,EAAEV,kBAAkB,CAAC;;IAElC;IACA9B,QAAQ,CACLqC,IAAI,CAAC,GAAG,EAAE7B,MAAM,CAACN,GAAG,CAAC,CACrBqC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBE,UAAU,CAAC,CAAC,CACZrF,QAAQ,CAACD,SAAS,CAACC,QAAQ,CAAC,CAC5BC,KAAK,CAAC,CAACwC,CAAC,EAAE6C,CAAC,KAAK,CAACvF,SAAS,CAACE,KAAK,IAAI,CAAC,IAAIqF,CAAC,GAAG,GAAG,CAAC,CACjDC,IAAI,CAACjH,EAAE,CAACkH,WAAW,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CACnCN,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBO,SAAS,CAAC,GAAG,EAAE,UAASjD,CAAC,EAAE;MAC1B,MAAMkD,WAAW,GAAGrH,EAAE,CAACqH,WAAW,CAAC;QAAE3C,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAE,CAAC,EAAER,CAAC,CAAC;MACrE,OAAO,UAASmD,CAAC,EAAE;QACjB,OAAOxC,MAAM,CAACN,GAAG,CAAC6C,WAAW,CAACC,CAAC,CAAC,CAAC,IAAI,EAAE;MACzC,CAAC;IACH,CAAC,CAAC;;IAEJ;IACA,IAAI3E,WAAW,CAACzB,UAAU,EAAE;MAC1B,MAAMqG,QAAQ,GAAGvH,EAAE,CAChBwE,GAAG,CAAyB,CAAC,CAC7B1D,WAAW,CAACC,WAAW,GAAG,EAAE,CAAC,CAC7BA,WAAW,CAACA,WAAW,GAAG,EAAE,CAAC;MAEhC,MAAMyG,MAAM,GAAGf,CAAC,CACbF,SAAS,CAAC,QAAQ,CAAC,CACnBhF,IAAI,CAACwC,aAAa,CAAC,CACnB6C,KAAK,CAAC,CAAC,CACPF,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CACtBA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CACnCE,KAAK,CAAC,MAAM,EAAEzD,MAAM,CAACO,IAAI,CAAC,CAC1BkD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BA,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAC5BA,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBlD,IAAI,CAACQ,CAAC;QAAA,IAAAsD,aAAA;QAAA,OAAI,GAAGtD,CAAC,CAACe,KAAK,MAAAuC,aAAA,GAAKtD,CAAC,CAAC2B,UAAU,cAAA2B,aAAA,uBAAZA,aAAA,CAAcC,OAAO,CAAC,CAAC,CAAC,IAAI;MAAA,EAAC;;MAEzD;MACAF,MAAM,CACHT,UAAU,CAAC,CAAC,CACZrF,QAAQ,CAACD,SAAS,CAACC,QAAQ,CAAC,CAC5BC,KAAK,CAAC,CAACwC,CAAC,EAAE6C,CAAC,KAAK,CAACvF,SAAS,CAACE,KAAK,IAAI,CAAC,IAAIqF,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CACvDL,IAAI,CAAC,WAAW,EAAExC,CAAC,IAAI;QACtB,MAAMwD,QAAQ,GAAGJ,QAAQ,CAACI,QAAQ,CAACxD,CAAC,CAAC;QACrC,OAAO,aAAawD,QAAQ,CAAC,CAAC,CAAC,KAAKA,QAAQ,CAAC,CAAC,CAAC,GAAG;MACpD,CAAC,CAAC,CACDd,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IACxB;;IAEA;IACA,IAAIlE,WAAW,CAACtB,UAAU,IAAIE,IAAI,CAACqG,WAAW,EAAE;MAC9C,MAAMvG,UAAU,GAAGoF,CAAC,CACjBC,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAC5BA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,mBAAmB,EAAE,QAAQ,CAAC,CACnCE,KAAK,CAAC,MAAM,EAAEzD,MAAM,CAACO,IAAI,CAAC,CAC1BkD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BA,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAC3BA,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBlD,IAAI,CAAChB,WAAW,CAACtB,UAAU,IAAIE,IAAI,CAACqG,WAAW,IAAI,EAAE,CAAC;MAEzDvG,UAAU,CACP0F,UAAU,CAAC,CAAC,CACZrF,QAAQ,CAACD,SAAS,CAACC,QAAQ,CAAC,CAC5BC,KAAK,CAACF,SAAS,CAACE,KAAK,IAAI,CAAC,CAAC,CAC3BkF,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IACxB;EAEF,CAAC,EAAE,CACD9C,aAAa,EACbe,MAAM,EACNlC,UAAU,EACVD,WAAW,EACXS,MAAM,EACN3B,SAAS,EACTsB,OAAO,EACPC,OAAO,EACPjC,WAAW,EACXQ,IAAI,CAACqG,WAAW,EAChBtC,uBAAuB,EACvBW,uBAAuB,EACvBG,kBAAkB,CACnB,CAAC;;EAEF;EACA;EACA;;EAEA,oBACE/F,OAAA,CAACJ,GAAG;IAAC8B,SAAS,EAAEA,SAAU;IAAC8F,EAAE,EAAE;MAAEtF,QAAQ,EAAE,UAAU;MAAEhC,KAAK,EAAE;IAAO,CAAE;IAAAuH,QAAA,GACpE9F,KAAK,iBACJ3B,OAAA,CAACH,UAAU;MAAC6H,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAACC,KAAK,EAAC,QAAQ;MAAAJ,QAAA,EAChE9F;IAAK;MAAAmG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAEDjI,OAAA,CAACJ,GAAG;MAAC4H,EAAE,EAAE;QAAEtF,QAAQ,EAAE,UAAU;QAAEgG,OAAO,EAAE;MAAe,CAAE;MAAAT,QAAA,gBACzDzH,OAAA;QACEmI,GAAG,EAAErG,MAAO;QACZ5B,KAAK,EAAEqC,UAAU,CAACrC,KAAM;QACxBC,MAAM,EAAEoC,UAAU,CAACpC,MAAO;QAC1BqG,KAAK,EAAE;UACLpD,UAAU,EAAEL,MAAM,CAACK,UAAU;UAC7BgF,YAAY,EAAEvG,KAAK,CAACwG,KAAK,CAACD,YAAY;UACtCE,SAAS,EAAEzG,KAAK,CAAC0G,OAAO,CAAC,CAAC;QAC5B;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGDjG,OAAO,CAACK,OAAO,IAAIL,OAAO,CAACd,IAAI,iBAC9BlB,OAAA,CAACJ,GAAG;QACFuI,GAAG,EAAEpG,UAAW;QAChByF,EAAE,EAAE;UACFtF,QAAQ,EAAE,UAAU;UACpB1B,IAAI,EAAEwB,OAAO,CAACE,QAAQ,CAACC,CAAC,GAAG,EAAE;UAC7B9B,GAAG,EAAE2B,OAAO,CAACE,QAAQ,CAACE,CAAC,GAAG,EAAE;UAC5BgB,UAAU,EAAEvB,KAAK,CAACoB,OAAO,CAACG,UAAU,CAACC,KAAK;UAC1CmF,MAAM,EAAE,aAAa3G,KAAK,CAACoB,OAAO,CAACO,OAAO,EAAE;UAC5C4E,YAAY,EAAE,CAAC;UACfK,OAAO,EAAE,CAAC;UACVH,SAAS,EAAEzG,KAAK,CAAC0G,OAAO,CAAC,CAAC,CAAC;UAC3BG,aAAa,EAAE,MAAM;UACrBC,MAAM,EAAE,IAAI;UACZC,QAAQ,EAAE;QACZ,CAAE;QAAAnB,QAAA,gBAEFzH,OAAA,CAACH,UAAU;UAAC6H,OAAO,EAAC,OAAO;UAACmB,UAAU,EAAC,MAAM;UAAApB,QAAA,EAC1CzF,OAAO,CAACd,IAAI,CAACS;QAAK;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACbjI,OAAA,CAACH,UAAU;UAAC6H,OAAO,EAAC,OAAO;UAACnD,KAAK,EAAC,gBAAgB;UAAAkD,QAAA,GAAC,SAC1C,EAACzF,OAAO,CAACd,IAAI,CAAC2C,KAAK,CAACiF,cAAc,CAAC,CAAC;QAAA;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,EACZjG,OAAO,CAACd,IAAI,CAACuE,UAAU,KAAKsD,SAAS,iBACpC/I,OAAA,CAACH,UAAU;UAAC6H,OAAO,EAAC,OAAO;UAACnD,KAAK,EAAC,gBAAgB;UAAAkD,QAAA,GAAC,cACrC,EAACzF,OAAO,CAACd,IAAI,CAACuE,UAAU,CAAC4B,OAAO,CAAC,CAAC,CAAC,EAAC,GAClD;QAAA;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrG,EAAA,CAvVIX,aAA2C;EAAA,QAUjCnB,QAAQ;AAAA;AAAAkJ,EAAA,GAVlB/H,aAA2C;AAyVjD,eAAeA,aAAa;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}