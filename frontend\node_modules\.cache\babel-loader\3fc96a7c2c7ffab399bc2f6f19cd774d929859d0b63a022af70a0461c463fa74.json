{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\cards\\\\DataCard.tsx\",\n  _s = $RefreshSig$();\n/**\n * DataCard Component for SkyGeni Dashboard\n * \n * A reusable card component that displays data with charts:\n * - Material-UI Card with elevation\n * - Integrated chart display (Bar or Doughnut)\n * - Loading and error states\n * - Responsive design\n * - Action buttons\n * - Data summary display\n */\n\nimport React from 'react';\nimport { Card, CardHeader, CardContent, CardActions, Typography, Box, IconButton, Chip, Divider } from '@mui/material';\nimport { Refresh as RefreshIcon, MoreVert as MoreVertIcon, TrendingUp as TrendingUpIcon, Assessment as AssessmentIcon } from '@mui/icons-material';\nimport BarChart from '../charts/BarChart';\nimport DoughnutChart from '../charts/DoughnutChart';\nimport Shimmer from '../common/Shimmer';\nimport ErrorMessage from '../common/ErrorMessage';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// Helper Functions\n// ============================================================================\n\n/**\n * Transform generic data to chart format\n */\nconst transformDataForChart = (data, chartType) => {\n  if (!data || data.length === 0) {\n    return chartType === 'bar' ? {\n      categories: [],\n      values: [],\n      colors: []\n    } : {\n      segments: [],\n      total: 0\n    };\n  }\n  if (chartType === 'bar') {\n    return {\n      categories: data.map(item => item.type || item.name || item.label || 'Unknown'),\n      values: data.map(item => item.count || item.value || 0),\n      colors: data.map((_, index) => `hsl(${index * 360 / data.length}, 70%, 50%)`)\n    };\n  } else {\n    const segments = data.map((item, index) => ({\n      label: item.type || item.name || item.label || 'Unknown',\n      value: item.count || item.value || 0,\n      percentage: item.percentage || 0,\n      color: `hsl(${index * 360 / data.length}, 70%, 50%)`\n    }));\n    const total = segments.reduce((sum, segment) => sum + segment.value, 0);\n    return {\n      segments,\n      total,\n      centerLabel: `Total: ${total.toLocaleString()}`\n    };\n  }\n};\n\n/**\n * Calculate summary statistics\n */\nconst calculateSummary = data => {\n  if (!data || data.length === 0) {\n    return {\n      total: 0,\n      count: 0,\n      average: 0\n    };\n  }\n  const total = data.reduce((sum, item) => sum + (item.count || item.value || 0), 0);\n  const count = data.length;\n  const average = count > 0 ? total / count : 0;\n  return {\n    total,\n    count,\n    average\n  };\n};\n\n// ============================================================================\n// DataCard Component\n// ============================================================================\n\nconst DataCard = ({\n  title,\n  data,\n  chartType,\n  loading = false,\n  error,\n  className,\n  elevation = 2\n}) => {\n  _s();\n  // const theme = useTheme();\n  const [refreshing, setRefreshing] = React.useState(false);\n\n  // Calculate summary statistics\n  const summary = React.useMemo(() => calculateSummary(data), [data]);\n\n  // Transform data for chart\n  const chartData = React.useMemo(() => transformDataForChart(data, chartType), [data, chartType]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  };\n  const handleChartClick = (chartData, event) => {\n    console.log('Chart clicked:', chartData);\n  };\n  const handleChartHover = (chartData, event) => {\n    // Handle chart hover if needed\n  };\n\n  // ========================================================================\n  // Render Chart\n  // ========================================================================\n\n  const renderChart = () => {\n    if (loading || refreshing) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Shimmer, {\n          variant: \"chart\",\n          height: 280\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this);\n    }\n    if (error) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n          error: error,\n          showRetryButton: true,\n          onRetry: handleRefresh,\n          centered: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this);\n    }\n    if (!data || data.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          py: 4,\n          color: 'text.secondary'\n        },\n        children: [/*#__PURE__*/_jsxDEV(AssessmentIcon, {\n          sx: {\n            fontSize: 48,\n            mb: 1,\n            opacity: 0.5\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: \"No data available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this);\n    }\n    const commonProps = {\n      onBarClick: chartType === 'bar' ? handleChartClick : undefined,\n      onBarHover: chartType === 'bar' ? handleChartHover : undefined,\n      onSegmentClick: chartType === 'doughnut' ? handleChartClick : undefined,\n      onSegmentHover: chartType === 'doughnut' ? handleChartHover : undefined\n    };\n    if (chartType === 'bar') {\n      return /*#__PURE__*/_jsxDEV(BarChart, {\n        data: chartData,\n        config: {\n          width: 350,\n          height: 280,\n          margin: {\n            top: 20,\n            right: 20,\n            bottom: 60,\n            left: 60\n          },\n          showGrid: true,\n          showLabels: false,\n          // Keep labels minimal in cards\n          xAxisLabel: title.includes('Industry') ? 'Industry' : title.includes('Team') ? 'Teams' : 'Category',\n          yAxisLabel: title.includes('Industry') ? 'Revenue ($)' : title.includes('Team') ? 'Members' : 'Count'\n        },\n        ...commonProps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 9\n      }, this);\n    } else {\n      return /*#__PURE__*/_jsxDEV(DoughnutChart, {\n        data: chartData,\n        config: {\n          width: 300,\n          height: 250,\n          margin: {\n            top: 10,\n            right: 10,\n            bottom: 10,\n            left: 10\n          },\n          innerRadius: 40,\n          outerRadius: 90,\n          showLabels: false // Keep labels minimal in cards\n        },\n        ...commonProps\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this);\n    }\n  };\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: className,\n    elevation: elevation,\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: '1px solid rgba(0, 0, 0, 0.1)',\n      borderRadius: 1\n    },\n    children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n      sx: {\n        pb: 1\n      },\n      title: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          component: \"h2\",\n          sx: {\n            fontWeight: 600,\n            fontSize: '1.1rem',\n            color: 'text.primary'\n          },\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), chartType === 'bar' ? /*#__PURE__*/_jsxDEV(TrendingUpIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n          color: \"primary\",\n          fontSize: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 243,\n        columnNumber: 11\n      }, this),\n      action: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleRefresh,\n          disabled: Boolean(loading) || Boolean(refreshing),\n          \"aria-label\": \"Refresh data\",\n          children: /*#__PURE__*/_jsxDEV(RefreshIcon, {\n            sx: {\n              animation: Boolean(loading) || Boolean(refreshing) ? 'spin 1s linear infinite' : 'none',\n              '@keyframes spin': {\n                '0%': {\n                  transform: 'rotate(0deg)'\n                },\n                '100%': {\n                  transform: 'rotate(360deg)'\n                }\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          \"aria-label\": \"More options\",\n          children: /*#__PURE__*/_jsxDEV(MoreVertIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this),\n      sx: {\n        pb: 1\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), !loading && !error && data && data.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 2,\n          pb: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            flexWrap: 'wrap'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Chip, {\n            label: `Total: ${summary.total.toLocaleString()}`,\n            size: \"small\",\n            color: \"primary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `Items: ${summary.count}`,\n            size: \"small\",\n            color: \"secondary\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this), summary.average > 0 && /*#__PURE__*/_jsxDEV(Chip, {\n            label: `Avg: ${summary.average.toFixed(1)}`,\n            size: \"small\",\n            variant: \"outlined\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 314,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(CardContent, {\n      sx: {\n        flex: 1,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        p: 1,\n        '&:last-child': {\n          pb: 1\n        }\n      },\n      children: renderChart()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n      sx: {\n        justifyContent: 'space-between',\n        px: 2,\n        py: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: data && data.length > 0 ? `${data.length} items` : 'No data'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: new Date().toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 333,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 228,\n    columnNumber: 5\n  }, this);\n};\n_s(DataCard, \"WGcNFMQWVYjWWys36J2hyhdLEe4=\");\n_c = DataCard;\nexport default DataCard;\nvar _c;\n$RefreshReg$(_c, \"DataCard\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Typography", "Box", "IconButton", "Chip", "Divider", "Refresh", "RefreshIcon", "<PERSON><PERSON><PERSON>", "MoreVertIcon", "TrendingUp", "TrendingUpIcon", "Assessment", "AssessmentIcon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Shimmer", "ErrorMessage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "transformDataForChart", "data", "chartType", "length", "categories", "values", "colors", "segments", "total", "map", "item", "type", "name", "label", "count", "value", "_", "index", "percentage", "color", "reduce", "sum", "segment", "centerLabel", "toLocaleString", "calculateSummary", "average", "DataCard", "title", "loading", "error", "className", "elevation", "_s", "refreshing", "setRefreshing", "useState", "summary", "useMemo", "chartData", "handleRefresh", "setTimeout", "handleChartClick", "event", "console", "log", "handleChartHover", "<PERSON><PERSON><PERSON>", "sx", "p", "children", "variant", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showRetryButton", "onRetry", "centered", "display", "flexDirection", "alignItems", "justifyContent", "py", "fontSize", "mb", "opacity", "commonProps", "onBarClick", "undefined", "onBarHover", "onSegmentClick", "onSegmentHover", "config", "width", "margin", "top", "right", "bottom", "left", "showGrid", "showLabels", "xAxisLabel", "includes", "yAxisLabel", "innerRadius", "outerRadius", "border", "borderRadius", "pb", "gap", "component", "fontWeight", "action", "size", "onClick", "disabled", "Boolean", "animation", "transform", "px", "flexWrap", "toFixed", "flex", "Date", "toLocaleDateString", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/cards/DataCard.tsx"], "sourcesContent": ["/**\n * DataCard Component for SkyGeni Dashboard\n * \n * A reusable card component that displays data with charts:\n * - Material-UI Card with elevation\n * - Integrated chart display (Bar or Doughnut)\n * - Loading and error states\n * - Responsive design\n * - Action buttons\n * - Data summary display\n */\n\nimport React from 'react';\nimport {\n  Card,\n  CardHeader,\n  CardContent,\n  CardActions,\n  Typography,\n  Box,\n  IconButton,\n  Chip,\n  Divider,\n  useTheme,\n} from '@mui/material';\nimport {\n  Refresh as RefreshIcon,\n  MoreVert as MoreVertIcon,\n  TrendingUp as TrendingUpIcon,\n  Assessment as AssessmentIcon,\n} from '@mui/icons-material';\nimport BarChart from '../charts/BarChart';\nimport DoughnutChart from '../charts/DoughnutChart';\nimport Loader from '../common/Loader';\nimport Shimmer from '../common/Shimmer';\nimport ErrorMessage from '../common/ErrorMessage';\nimport { DataCardProps } from '../../types';\nimport { BarChartData, DoughnutChartData } from '../charts/types';\n\n// ============================================================================\n// Helper Functions\n// ============================================================================\n\n/**\n * Transform generic data to chart format\n */\nconst transformDataForChart = (data: any[], chartType: 'bar' | 'doughnut') => {\n  if (!data || data.length === 0) {\n    return chartType === 'bar' \n      ? { categories: [], values: [], colors: [] }\n      : { segments: [], total: 0 };\n  }\n\n  if (chartType === 'bar') {\n    return {\n      categories: data.map(item => item.type || item.name || item.label || 'Unknown'),\n      values: data.map(item => item.count || item.value || 0),\n      colors: data.map((_, index) => `hsl(${(index * 360) / data.length}, 70%, 50%)`),\n    } as BarChartData;\n  } else {\n    const segments = data.map((item, index) => ({\n      label: item.type || item.name || item.label || 'Unknown',\n      value: item.count || item.value || 0,\n      percentage: item.percentage || 0,\n      color: `hsl(${(index * 360) / data.length}, 70%, 50%)`,\n    }));\n    \n    const total = segments.reduce((sum, segment) => sum + segment.value, 0);\n    \n    return {\n      segments,\n      total,\n      centerLabel: `Total: ${total.toLocaleString()}`,\n    } as DoughnutChartData;\n  }\n};\n\n/**\n * Calculate summary statistics\n */\nconst calculateSummary = (data: any[]) => {\n  if (!data || data.length === 0) {\n    return { total: 0, count: 0, average: 0 };\n  }\n\n  const total = data.reduce((sum, item) => sum + (item.count || item.value || 0), 0);\n  const count = data.length;\n  const average = count > 0 ? total / count : 0;\n\n  return { total, count, average };\n};\n\n// ============================================================================\n// DataCard Component\n// ============================================================================\n\nconst DataCard: React.FC<DataCardProps> = ({\n  title,\n  data,\n  chartType,\n  loading = false,\n  error,\n  className,\n  elevation = 2,\n}) => {\n  // const theme = useTheme();\n  const [refreshing, setRefreshing] = React.useState(false);\n\n  // Calculate summary statistics\n  const summary = React.useMemo(() => calculateSummary(data), [data]);\n\n  // Transform data for chart\n  const chartData = React.useMemo(() => \n    transformDataForChart(data, chartType), \n    [data, chartType]\n  );\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleRefresh = async () => {\n    setRefreshing(true);\n    // Simulate refresh delay\n    setTimeout(() => {\n      setRefreshing(false);\n    }, 1000);\n  };\n\n  const handleChartClick = (chartData: any, event: MouseEvent) => {\n    console.log('Chart clicked:', chartData);\n  };\n\n  const handleChartHover = (chartData: any, event: MouseEvent) => {\n    // Handle chart hover if needed\n  };\n\n  // ========================================================================\n  // Render Chart\n  // ========================================================================\n\n  const renderChart = () => {\n    if (loading || refreshing) {\n      return (\n        <Box sx={{ p: 2 }}>\n          <Shimmer variant=\"chart\" height={280} />\n        </Box>\n      );\n    }\n\n    if (error) {\n      return (\n        <Box sx={{ p: 2 }}>\n          <ErrorMessage\n            error={error}\n            showRetryButton={true}\n            onRetry={handleRefresh}\n            centered={true}\n          />\n        </Box>\n      );\n    }\n\n    if (!data || data.length === 0) {\n      return (\n        <Box \n          sx={{ \n            display: 'flex', \n            flexDirection: 'column',\n            alignItems: 'center', \n            justifyContent: 'center',\n            py: 4,\n            color: 'text.secondary'\n          }}\n        >\n          <AssessmentIcon sx={{ fontSize: 48, mb: 1, opacity: 0.5 }} />\n          <Typography variant=\"body2\">No data available</Typography>\n        </Box>\n      );\n    }\n\n    const commonProps = {\n      onBarClick: chartType === 'bar' ? handleChartClick : undefined,\n      onBarHover: chartType === 'bar' ? handleChartHover : undefined,\n      onSegmentClick: chartType === 'doughnut' ? handleChartClick : undefined,\n      onSegmentHover: chartType === 'doughnut' ? handleChartHover : undefined,\n    };\n\n    if (chartType === 'bar') {\n      return (\n        <BarChart\n          data={chartData as BarChartData}\n          config={{\n            width: 350,\n            height: 280,\n            margin: { top: 20, right: 20, bottom: 60, left: 60 },\n            showGrid: true,\n            showLabels: false, // Keep labels minimal in cards\n            xAxisLabel: title.includes('Industry') ? 'Industry' : title.includes('Team') ? 'Teams' : 'Category',\n            yAxisLabel: title.includes('Industry') ? 'Revenue ($)' : title.includes('Team') ? 'Members' : 'Count',\n          }}\n          {...commonProps}\n        />\n      );\n    } else {\n      return (\n        <DoughnutChart\n          data={chartData as DoughnutChartData}\n          config={{\n            width: 300,\n            height: 250,\n            margin: { top: 10, right: 10, bottom: 10, left: 10 },\n            innerRadius: 40,\n            outerRadius: 90,\n            showLabels: false, // Keep labels minimal in cards\n          }}\n          {...commonProps}\n        />\n      );\n    }\n  };\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return (\n    <Card\n      className={className}\n      elevation={elevation}\n      sx={{\n        height: '100%',\n        display: 'flex',\n        flexDirection: 'column',\n        border: '1px solid rgba(0, 0, 0, 0.1)',\n        borderRadius: 1,\n      }}\n    >\n      {/* Card Header */}\n      <CardHeader\n        sx={{ pb: 1 }}\n        title={\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n            <Typography\n              variant=\"h6\"\n              component=\"h2\"\n              sx={{\n                fontWeight: 600,\n                fontSize: '1.1rem',\n                color: 'text.primary',\n              }}\n            >\n              {title}\n            </Typography>\n            {chartType === 'bar' ? (\n              <TrendingUpIcon color=\"primary\" fontSize=\"small\" />\n            ) : (\n              <AssessmentIcon color=\"primary\" fontSize=\"small\" />\n            )}\n          </Box>\n        }\n        action={\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n            <IconButton\n              size=\"small\"\n              onClick={handleRefresh}\n              disabled={Boolean(loading) || Boolean(refreshing)}\n              aria-label=\"Refresh data\"\n            >\n              <RefreshIcon\n                sx={{\n                  animation: (Boolean(loading) || Boolean(refreshing)) ? 'spin 1s linear infinite' : 'none',\n                  '@keyframes spin': {\n                    '0%': { transform: 'rotate(0deg)' },\n                    '100%': { transform: 'rotate(360deg)' },\n                  },\n                }} \n              />\n            </IconButton>\n            <IconButton size=\"small\" aria-label=\"More options\">\n              <MoreVertIcon />\n            </IconButton>\n          </Box>\n        }\n        sx={{ pb: 1 }}\n      />\n\n      {/* Summary Statistics */}\n      {!loading && !error && data && data.length > 0 && (\n        <>\n          <Box sx={{ px: 2, pb: 1 }}>\n            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n              <Chip\n                label={`Total: ${summary.total.toLocaleString()}`}\n                size=\"small\"\n                color=\"primary\"\n                variant=\"outlined\"\n              />\n              <Chip\n                label={`Items: ${summary.count}`}\n                size=\"small\"\n                color=\"secondary\"\n                variant=\"outlined\"\n              />\n              {summary.average > 0 && (\n                <Chip\n                  label={`Avg: ${summary.average.toFixed(1)}`}\n                  size=\"small\"\n                  variant=\"outlined\"\n                />\n              )}\n            </Box>\n          </Box>\n          <Divider />\n        </>\n      )}\n\n      {/* Chart Content */}\n      <CardContent \n        sx={{ \n          flex: 1, \n          display: 'flex', \n          alignItems: 'center', \n          justifyContent: 'center',\n          p: 1,\n          '&:last-child': { pb: 1 },\n        }}\n      >\n        {renderChart()}\n      </CardContent>\n\n      {/* Card Actions */}\n      <CardActions sx={{ justifyContent: 'space-between', px: 2, py: 1 }}>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          {data && data.length > 0 ? `${data.length} items` : 'No data'}\n        </Typography>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          {new Date().toLocaleDateString()}\n        </Typography>\n      </CardActions>\n    </Card>\n  );\n};\n\nexport default DataCard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,IAAI,EACJC,UAAU,EACVC,WAAW,EACXC,WAAW,EACXC,UAAU,EACVC,GAAG,EACHC,UAAU,EACVC,IAAI,EACJC,OAAO,QAEF,eAAe;AACtB,SACEC,OAAO,IAAIC,WAAW,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,QACvB,qBAAqB;AAC5B,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,OAAOC,aAAa,MAAM,yBAAyB;AAEnD,OAAOC,OAAO,MAAM,mBAAmB;AACvC,OAAOC,YAAY,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIlD;AACA;AACA;;AAEA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAGA,CAACC,IAAW,EAAEC,SAA6B,KAAK;EAC5E,IAAI,CAACD,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAOD,SAAS,KAAK,KAAK,GACtB;MAAEE,UAAU,EAAE,EAAE;MAAEC,MAAM,EAAE,EAAE;MAAEC,MAAM,EAAE;IAAG,CAAC,GAC1C;MAAEC,QAAQ,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAE,CAAC;EAChC;EAEA,IAAIN,SAAS,KAAK,KAAK,EAAE;IACvB,OAAO;MACLE,UAAU,EAAEH,IAAI,CAACQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,IAAI,SAAS,CAAC;MAC/ER,MAAM,EAAEJ,IAAI,CAACQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,KAAK,IAAI,CAAC,CAAC;MACvDT,MAAM,EAAEL,IAAI,CAACQ,GAAG,CAAC,CAACO,CAAC,EAAEC,KAAK,KAAK,OAAQA,KAAK,GAAG,GAAG,GAAIhB,IAAI,CAACE,MAAM,aAAa;IAChF,CAAC;EACH,CAAC,MAAM;IACL,MAAMI,QAAQ,GAAGN,IAAI,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEO,KAAK,MAAM;MAC1CJ,KAAK,EAAEH,IAAI,CAACC,IAAI,IAAID,IAAI,CAACE,IAAI,IAAIF,IAAI,CAACG,KAAK,IAAI,SAAS;MACxDE,KAAK,EAAEL,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,KAAK,IAAI,CAAC;MACpCG,UAAU,EAAER,IAAI,CAACQ,UAAU,IAAI,CAAC;MAChCC,KAAK,EAAE,OAAQF,KAAK,GAAG,GAAG,GAAIhB,IAAI,CAACE,MAAM;IAC3C,CAAC,CAAC,CAAC;IAEH,MAAMK,KAAK,GAAGD,QAAQ,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACP,KAAK,EAAE,CAAC,CAAC;IAEvE,OAAO;MACLR,QAAQ;MACRC,KAAK;MACLe,WAAW,EAAE,UAAUf,KAAK,CAACgB,cAAc,CAAC,CAAC;IAC/C,CAAC;EACH;AACF,CAAC;;AAED;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIxB,IAAW,IAAK;EACxC,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;IAC9B,OAAO;MAAEK,KAAK,EAAE,CAAC;MAAEM,KAAK,EAAE,CAAC;MAAEY,OAAO,EAAE;IAAE,CAAC;EAC3C;EAEA,MAAMlB,KAAK,GAAGP,IAAI,CAACmB,MAAM,CAAC,CAACC,GAAG,EAAEX,IAAI,KAAKW,GAAG,IAAIX,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAClF,MAAMD,KAAK,GAAGb,IAAI,CAACE,MAAM;EACzB,MAAMuB,OAAO,GAAGZ,KAAK,GAAG,CAAC,GAAGN,KAAK,GAAGM,KAAK,GAAG,CAAC;EAE7C,OAAO;IAAEN,KAAK;IAAEM,KAAK;IAAEY;EAAQ,CAAC;AAClC,CAAC;;AAED;AACA;AACA;;AAEA,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,KAAK;EACL3B,IAAI;EACJC,SAAS;EACT2B,OAAO,GAAG,KAAK;EACfC,KAAK;EACLC,SAAS;EACTC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG7D,KAAK,CAAC8D,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA,MAAMC,OAAO,GAAG/D,KAAK,CAACgE,OAAO,CAAC,MAAMb,gBAAgB,CAACxB,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEnE;EACA,MAAMsC,SAAS,GAAGjE,KAAK,CAACgE,OAAO,CAAC,MAC9BtC,qBAAqB,CAACC,IAAI,EAAEC,SAAS,CAAC,EACtC,CAACD,IAAI,EAAEC,SAAS,CAClB,CAAC;;EAED;EACA;EACA;;EAEA,MAAMsC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCL,aAAa,CAAC,IAAI,CAAC;IACnB;IACAM,UAAU,CAAC,MAAM;MACfN,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMO,gBAAgB,GAAGA,CAACH,SAAc,EAAEI,KAAiB,KAAK;IAC9DC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEN,SAAS,CAAC;EAC1C,CAAC;EAED,MAAMO,gBAAgB,GAAGA,CAACP,SAAc,EAAEI,KAAiB,KAAK;IAC9D;EAAA,CACD;;EAED;EACA;EACA;;EAEA,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIlB,OAAO,IAAIK,UAAU,EAAE;MACzB,oBACErC,OAAA,CAACjB,GAAG;QAACoE,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAChBrD,OAAA,CAACH,OAAO;UAACyD,OAAO,EAAC,OAAO;UAACC,MAAM,EAAE;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrC,CAAC;IAEV;IAEA,IAAI1B,KAAK,EAAE;MACT,oBACEjC,OAAA,CAACjB,GAAG;QAACoE,EAAE,EAAE;UAAEC,CAAC,EAAE;QAAE,CAAE;QAAAC,QAAA,eAChBrD,OAAA,CAACF,YAAY;UACXmC,KAAK,EAAEA,KAAM;UACb2B,eAAe,EAAE,IAAK;UACtBC,OAAO,EAAElB,aAAc;UACvBmB,QAAQ,EAAE;QAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,IAAI,CAACvD,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;MAC9B,oBACEN,OAAA,CAACjB,GAAG;QACFoE,EAAE,EAAE;UACFY,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,EAAE,EAAE,CAAC;UACL7C,KAAK,EAAE;QACT,CAAE;QAAA+B,QAAA,gBAEFrD,OAAA,CAACN,cAAc;UAACyD,EAAE,EAAE;YAAEiB,QAAQ,EAAE,EAAE;YAAEC,EAAE,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAI;QAAE;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7D3D,OAAA,CAAClB,UAAU;UAACwE,OAAO,EAAC,OAAO;UAAAD,QAAA,EAAC;QAAiB;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC;IAEV;IAEA,MAAMY,WAAW,GAAG;MAClBC,UAAU,EAAEnE,SAAS,KAAK,KAAK,GAAGwC,gBAAgB,GAAG4B,SAAS;MAC9DC,UAAU,EAAErE,SAAS,KAAK,KAAK,GAAG4C,gBAAgB,GAAGwB,SAAS;MAC9DE,cAAc,EAAEtE,SAAS,KAAK,UAAU,GAAGwC,gBAAgB,GAAG4B,SAAS;MACvEG,cAAc,EAAEvE,SAAS,KAAK,UAAU,GAAG4C,gBAAgB,GAAGwB;IAChE,CAAC;IAED,IAAIpE,SAAS,KAAK,KAAK,EAAE;MACvB,oBACEL,OAAA,CAACL,QAAQ;QACPS,IAAI,EAAEsC,SAA0B;QAChCmC,MAAM,EAAE;UACNC,KAAK,EAAE,GAAG;UACVvB,MAAM,EAAE,GAAG;UACXwB,MAAM,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAG,CAAC;UACpDC,QAAQ,EAAE,IAAI;UACdC,UAAU,EAAE,KAAK;UAAE;UACnBC,UAAU,EAAEvD,KAAK,CAACwD,QAAQ,CAAC,UAAU,CAAC,GAAG,UAAU,GAAGxD,KAAK,CAACwD,QAAQ,CAAC,MAAM,CAAC,GAAG,OAAO,GAAG,UAAU;UACnGC,UAAU,EAAEzD,KAAK,CAACwD,QAAQ,CAAC,UAAU,CAAC,GAAG,aAAa,GAAGxD,KAAK,CAACwD,QAAQ,CAAC,MAAM,CAAC,GAAG,SAAS,GAAG;QAChG,CAAE;QAAA,GACEhB;MAAW;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAEN,CAAC,MAAM;MACL,oBACE3D,OAAA,CAACJ,aAAa;QACZQ,IAAI,EAAEsC,SAA+B;QACrCmC,MAAM,EAAE;UACNC,KAAK,EAAE,GAAG;UACVvB,MAAM,EAAE,GAAG;UACXwB,MAAM,EAAE;YAAEC,GAAG,EAAE,EAAE;YAAEC,KAAK,EAAE,EAAE;YAAEC,MAAM,EAAE,EAAE;YAAEC,IAAI,EAAE;UAAG,CAAC;UACpDM,WAAW,EAAE,EAAE;UACfC,WAAW,EAAE,EAAE;UACfL,UAAU,EAAE,KAAK,CAAE;QACrB,CAAE;QAAA,GACEd;MAAW;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC;IAEN;EACF,CAAC;;EAED;EACA;EACA;;EAEA,oBACE3D,OAAA,CAACtB,IAAI;IACHwD,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA,SAAU;IACrBgB,EAAE,EAAE;MACFI,MAAM,EAAE,MAAM;MACdQ,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvB2B,MAAM,EAAE,8BAA8B;MACtCC,YAAY,EAAE;IAChB,CAAE;IAAAvC,QAAA,gBAGFrD,OAAA,CAACrB,UAAU;MACTwE,EAAE,EAAE;QAAE0C,EAAE,EAAE;MAAE,CAAE;MACd9D,KAAK,eACH/B,OAAA,CAACjB,GAAG;QAACoE,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE6B,GAAG,EAAE;QAAI,CAAE;QAAAzC,QAAA,gBAC3DrD,OAAA,CAAClB,UAAU;UACTwE,OAAO,EAAC,IAAI;UACZyC,SAAS,EAAC,IAAI;UACd5C,EAAE,EAAE;YACF6C,UAAU,EAAE,GAAG;YACf5B,QAAQ,EAAE,QAAQ;YAClB9C,KAAK,EAAE;UACT,CAAE;UAAA+B,QAAA,EAEDtB;QAAK;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EACZtD,SAAS,KAAK,KAAK,gBAClBL,OAAA,CAACR,cAAc;UAAC8B,KAAK,EAAC,SAAS;UAAC8C,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAEnD3D,OAAA,CAACN,cAAc;UAAC4B,KAAK,EAAC,SAAS;UAAC8C,QAAQ,EAAC;QAAO;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CACnD;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;MACDsC,MAAM,eACJjG,OAAA,CAACjB,GAAG;QAACoE,EAAE,EAAE;UAAEY,OAAO,EAAE,MAAM;UAAEE,UAAU,EAAE,QAAQ;UAAE6B,GAAG,EAAE;QAAE,CAAE;QAAAzC,QAAA,gBACzDrD,OAAA,CAAChB,UAAU;UACTkH,IAAI,EAAC,OAAO;UACZC,OAAO,EAAExD,aAAc;UACvByD,QAAQ,EAAEC,OAAO,CAACrE,OAAO,CAAC,IAAIqE,OAAO,CAAChE,UAAU,CAAE;UAClD,cAAW,cAAc;UAAAgB,QAAA,eAEzBrD,OAAA,CAACZ,WAAW;YACV+D,EAAE,EAAE;cACFmD,SAAS,EAAGD,OAAO,CAACrE,OAAO,CAAC,IAAIqE,OAAO,CAAChE,UAAU,CAAC,GAAI,yBAAyB,GAAG,MAAM;cACzF,iBAAiB,EAAE;gBACjB,IAAI,EAAE;kBAAEkE,SAAS,EAAE;gBAAe,CAAC;gBACnC,MAAM,EAAE;kBAAEA,SAAS,EAAE;gBAAiB;cACxC;YACF;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,eACb3D,OAAA,CAAChB,UAAU;UAACkH,IAAI,EAAC,OAAO;UAAC,cAAW,cAAc;UAAA7C,QAAA,eAChDrD,OAAA,CAACV,YAAY;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;MACDR,EAAE,EAAE;QAAE0C,EAAE,EAAE;MAAE;IAAE;MAAArC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,EAGD,CAAC3B,OAAO,IAAI,CAACC,KAAK,IAAI7B,IAAI,IAAIA,IAAI,CAACE,MAAM,GAAG,CAAC,iBAC5CN,OAAA,CAAAE,SAAA;MAAAmD,QAAA,gBACErD,OAAA,CAACjB,GAAG;QAACoE,EAAE,EAAE;UAAEqD,EAAE,EAAE,CAAC;UAAEX,EAAE,EAAE;QAAE,CAAE;QAAAxC,QAAA,eACxBrD,OAAA,CAACjB,GAAG;UAACoE,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAE+B,GAAG,EAAE,CAAC;YAAEW,QAAQ,EAAE;UAAO,CAAE;UAAApD,QAAA,gBACrDrD,OAAA,CAACf,IAAI;YACH+B,KAAK,EAAE,UAAUwB,OAAO,CAAC7B,KAAK,CAACgB,cAAc,CAAC,CAAC,EAAG;YAClDuE,IAAI,EAAC,OAAO;YACZ5E,KAAK,EAAC,SAAS;YACfgC,OAAO,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eACF3D,OAAA,CAACf,IAAI;YACH+B,KAAK,EAAE,UAAUwB,OAAO,CAACvB,KAAK,EAAG;YACjCiF,IAAI,EAAC,OAAO;YACZ5E,KAAK,EAAC,WAAW;YACjBgC,OAAO,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,EACDnB,OAAO,CAACX,OAAO,GAAG,CAAC,iBAClB7B,OAAA,CAACf,IAAI;YACH+B,KAAK,EAAE,QAAQwB,OAAO,CAACX,OAAO,CAAC6E,OAAO,CAAC,CAAC,CAAC,EAAG;YAC5CR,IAAI,EAAC,OAAO;YACZ5C,OAAO,EAAC;UAAU;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACN3D,OAAA,CAACd,OAAO;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA,eACX,CACH,eAGD3D,OAAA,CAACpB,WAAW;MACVuE,EAAE,EAAE;QACFwD,IAAI,EAAE,CAAC;QACP5C,OAAO,EAAE,MAAM;QACfE,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBd,CAAC,EAAE,CAAC;QACJ,cAAc,EAAE;UAAEyC,EAAE,EAAE;QAAE;MAC1B,CAAE;MAAAxC,QAAA,EAEDH,WAAW,CAAC;IAAC;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGd3D,OAAA,CAACnB,WAAW;MAACsE,EAAE,EAAE;QAAEe,cAAc,EAAE,eAAe;QAAEsC,EAAE,EAAE,CAAC;QAAErC,EAAE,EAAE;MAAE,CAAE;MAAAd,QAAA,gBACjErD,OAAA,CAAClB,UAAU;QAACwE,OAAO,EAAC,SAAS;QAAChC,KAAK,EAAC,gBAAgB;QAAA+B,QAAA,EACjDjD,IAAI,IAAIA,IAAI,CAACE,MAAM,GAAG,CAAC,GAAG,GAAGF,IAAI,CAACE,MAAM,QAAQ,GAAG;MAAS;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC,eACb3D,OAAA,CAAClB,UAAU;QAACwE,OAAO,EAAC,SAAS;QAAChC,KAAK,EAAC,gBAAgB;QAAA+B,QAAA,EACjD,IAAIuD,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC;MAAC;QAAArD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEX,CAAC;AAACvB,EAAA,CAtPIN,QAAiC;AAAAgF,EAAA,GAAjChF,QAAiC;AAwPvC,eAAeA,QAAQ;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}