/**
 * Common Component Type Definitions
 * Contains types for shared UI components
 */

import { ReactNode } from 'react';
import { SxProps, Theme } from '@mui/material';

// ============================================================================
// Loader Component Types
// ============================================================================

/**
 * Loader component props
 */
export interface LoaderProps {
  /**
   * Size of the loader (small, medium, large, or custom number)
   */
  size?: 'small' | 'medium' | 'large' | number;
  
  /**
   * Color of the loader
   */
  color?: 'primary' | 'secondary' | 'inherit' | string;
  
  /**
   * Loading message to display
   */
  message?: string;
  
  /**
   * Whether to show the loading message
   */
  showMessage?: boolean;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
  
  /**
   * Whether to center the loader
   */
  centered?: boolean;
  
  /**
   * Minimum height for the loader container
   */
  minHeight?: number | string;
  
  /**
   * Whether to show a backdrop
   */
  backdrop?: boolean;
  
  /**
   * Variant of the loader
   */
  variant?: 'circular' | 'linear' | 'dots' | 'skeleton';
}

// ============================================================================
// Error Message Component Types
// ============================================================================

/**
 * Error message component props
 */
export interface ErrorMessageProps {
  /**
   * Error to display (string or Error object)
   */
  error: string | Error | null;
  
  /**
   * Title for the error message
   */
  title?: string;
  
  /**
   * Whether to show a retry button
   */
  showRetryButton?: boolean;
  
  /**
   * Callback for retry button click
   */
  onRetry?: () => void;
  
  /**
   * Retry button text
   */
  retryButtonText?: string;
  
  /**
   * Whether to show error details
   */
  showDetails?: boolean;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
  
  /**
   * Severity level of the error
   */
  severity?: 'error' | 'warning' | 'info';
  
  /**
   * Whether to center the error message
   */
  centered?: boolean;
  
  /**
   * Icon to display with the error
   */
  icon?: ReactNode;
  
  /**
   * Whether the error is dismissible
   */
  dismissible?: boolean;
  
  /**
   * Callback for dismiss action
   */
  onDismiss?: () => void;
  
  /**
   * Additional actions to display
   */
  actions?: ReactNode;
}

// ============================================================================
// Empty State Component Types
// ============================================================================

/**
 * Empty state component props
 */
export interface EmptyStateProps {
  /**
   * Title for the empty state
   */
  title?: string;
  
  /**
   * Description for the empty state
   */
  description?: string;
  
  /**
   * Icon to display
   */
  icon?: ReactNode;
  
  /**
   * Primary action button
   */
  primaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'contained' | 'outlined' | 'text';
  };
  
  /**
   * Secondary action button
   */
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'contained' | 'outlined' | 'text';
  };
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
  
  /**
   * Minimum height for the empty state container
   */
  minHeight?: number | string;
}

// ============================================================================
// Status Indicator Component Types
// ============================================================================

/**
 * Status indicator component props
 */
export interface StatusIndicatorProps {
  /**
   * Status to display
   */
  status: 'loading' | 'success' | 'error' | 'warning' | 'info' | 'idle';
  
  /**
   * Label to display with the status
   */
  label?: string;
  
  /**
   * Size of the indicator
   */
  size?: 'small' | 'medium' | 'large';
  
  /**
   * Whether to show the label
   */
  showLabel?: boolean;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
  
  /**
   * Custom colors for different statuses
   */
  colors?: {
    loading?: string;
    success?: string;
    error?: string;
    warning?: string;
    info?: string;
    idle?: string;
  };
}

// ============================================================================
// Confirmation Dialog Component Types
// ============================================================================

/**
 * Confirmation dialog component props
 */
export interface ConfirmationDialogProps {
  /**
   * Whether the dialog is open
   */
  open: boolean;
  
  /**
   * Callback for closing the dialog
   */
  onClose: () => void;
  
  /**
   * Callback for confirming the action
   */
  onConfirm: () => void;
  
  /**
   * Title of the dialog
   */
  title?: string;
  
  /**
   * Content/message of the dialog
   */
  content?: ReactNode;
  
  /**
   * Confirm button text
   */
  confirmText?: string;
  
  /**
   * Cancel button text
   */
  cancelText?: string;
  
  /**
   * Severity of the action
   */
  severity?: 'info' | 'warning' | 'error';
  
  /**
   * Whether the action is destructive
   */
  destructive?: boolean;
  
  /**
   * Whether to show loading state on confirm
   */
  loading?: boolean;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
}

// ============================================================================
// Data Display Component Types
// ============================================================================

/**
 * Data display component props for showing key-value pairs
 */
export interface DataDisplayProps {
  /**
   * Data to display as key-value pairs
   */
  data: Record<string, any>;
  
  /**
   * Fields to display (if not provided, all fields are shown)
   */
  fields?: string[];
  
  /**
   * Field labels mapping
   */
  fieldLabels?: Record<string, string>;
  
  /**
   * Field formatters
   */
  fieldFormatters?: Record<string, (value: any) => string>;
  
  /**
   * Layout orientation
   */
  orientation?: 'horizontal' | 'vertical';
  
  /**
   * Whether to show dividers between items
   */
  showDividers?: boolean;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
}

// ============================================================================
// Search Component Types
// ============================================================================

/**
 * Search component props
 */
export interface SearchProps {
  /**
   * Current search value
   */
  value: string;
  
  /**
   * Callback for value changes
   */
  onChange: (value: string) => void;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Whether to show clear button
   */
  showClearButton?: boolean;
  
  /**
   * Debounce delay in milliseconds
   */
  debounceDelay?: number;
  
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Material-UI sx prop for styling
   */
  sx?: SxProps<Theme>;
  
  /**
   * Size of the search input
   */
  size?: 'small' | 'medium';
  
  /**
   * Whether the search is disabled
   */
  disabled?: boolean;
  
  /**
   * Auto focus on mount
   */
  autoFocus?: boolean;
}

// ============================================================================
// Export utility types
// ============================================================================

/**
 * Common component sizes
 */
export type ComponentSize = 'small' | 'medium' | 'large';

/**
 * Common component variants
 */
export type ComponentVariant = 'contained' | 'outlined' | 'text';

/**
 * Common severity levels
 */
export type SeverityLevel = 'error' | 'warning' | 'info' | 'success';
