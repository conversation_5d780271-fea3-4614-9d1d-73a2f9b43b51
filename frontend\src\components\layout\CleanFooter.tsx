// Simple footer with version and last updated info

import React from 'react';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  GitHub as GitHubIcon,
  Description as DocumentationIcon,
} from '@mui/icons-material';



interface CleanFooterProps {
  lastUpdated?: Date | string | null;
  version?: string;
  githubUrl?: string;
  docsUrl?: string;
  className?: string;
}



const CleanFooter: React.FC<CleanFooterProps> = ({
  lastUpdated,
  version = 'v1.0.0',
  githubUrl = 'https://github.com/skygeni/dashboard',
  docsUrl = 'https://docs.skygeni.com',
  className,
}) => {
  const theme = useTheme();

  const formatTimestamp = (timestamp: Date | string | null): string => {
    if (!timestamp) return 'Never';
    
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <Box
      component="footer"
      className={className}
      sx={{
        width: '100%',
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        borderTop: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
        py: 2,
        px: 3,
        mt: 4,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          flexWrap: 'wrap',
          gap: 2,
          maxWidth: 'lg',
          mx: 'auto',
        }}
      >
        {/* Left Section - Version & Build Info */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '0.8rem',
              fontWeight: 500,
            }}
          >
            {version}
          </Typography>

          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '0.8rem',
              display: { xs: 'none', sm: 'block' },
            }}
          >
            Built with React, TypeScript, MUI, D3.js
          </Typography>
        </Box>

        {/* Center Section - Last Updated */}
        <Box sx={{ display: { xs: 'none', md: 'block' } }}>
          <Typography
            variant="body2"
            sx={{
              color: 'text.secondary',
              fontSize: '0.8rem',
              textAlign: 'center',
            }}
          >
            Last Updated: {formatTimestamp(lastUpdated)}
          </Typography>
        </Box>

        {/* Right Section - Links */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title="GitHub Repository">
            <IconButton
              href={githubUrl}
              target="_blank"
              rel="noopener noreferrer"
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  transform: 'scale(1.1)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <GitHubIcon fontSize="small" />
            </IconButton>
          </Tooltip>

          <Tooltip title="Documentation">
            <IconButton
              href={docsUrl}
              target="_blank"
              rel="noopener noreferrer"
              size="small"
              sx={{
                color: 'text.secondary',
                '&:hover': {
                  color: 'primary.main',
                  transform: 'scale(1.1)',
                },
                transition: 'all 0.2s ease-in-out',
              }}
            >
              <DocumentationIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>
    </Box>
  );
};

export default CleanFooter;
