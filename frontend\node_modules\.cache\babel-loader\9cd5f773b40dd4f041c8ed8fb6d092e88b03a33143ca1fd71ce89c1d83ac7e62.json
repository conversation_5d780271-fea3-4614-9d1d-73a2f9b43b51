{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\cards\\\\AccountIndustryCard.tsx\",\n  _s = $RefreshSig$();\n/**\n * AccountIndustryCard Component for SkyGeni Dashboard\n * Specialized card for account industry data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useAccountIndustries } from '../../hooks/useData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AccountIndustryCard = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'bar'\n}) => {\n  _s();\n  const {\n    accountIndustries,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch\n  } = useAccountIndustries();\n  const data = overrideData || accountIndustries;\n  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading === 'pending';\n  const error = overrideError || (isError ? hookError : undefined);\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n    return data.map(industry => ({\n      ...industry,\n      name: industry.industry,\n      label: industry.industry,\n      value: industry.count,\n      displayText: `${industry.industry} (${industry.count.toLocaleString()})`,\n      revenueText: industry.revenue ? `$${industry.revenue.toLocaleString()}` : ''\n    }));\n  }, [data]);\n  return /*#__PURE__*/_jsxDEV(DataCard, {\n    title: \"Account Industries\",\n    data: processedData,\n    chartType: chartType,\n    loading: loading,\n    error: error,\n    className: className,\n    elevation: elevation\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_s(AccountIndustryCard, \"qzHeFm9vo+YPQ9u2UTbJ1RMmG5s=\", false, function () {\n  return [useAccountIndustries];\n});\n_c = AccountIndustryCard;\nexport const AccountIndustryBarCard = props => /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n  ...props,\n  chartType: \"bar\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 69,\n  columnNumber: 3\n}, this);\n_c2 = AccountIndustryBarCard;\nexport const AccountIndustryDoughnutCard = props => /*#__PURE__*/_jsxDEV(AccountIndustryCard, {\n  ...props,\n  chartType: \"doughnut\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 73,\n  columnNumber: 3\n}, this);\n_c3 = AccountIndustryDoughnutCard;\nexport default AccountIndustryCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"AccountIndustryCard\");\n$RefreshReg$(_c2, \"AccountIndustryBarCard\");\n$RefreshReg$(_c3, \"AccountIndustryDoughnutCard\");", "map": {"version": 3, "names": ["React", "DataCard", "useAccountIndustries", "jsxDEV", "_jsxDEV", "AccountIndustryCard", "className", "elevation", "data", "overrideData", "loading", "overrideLoading", "error", "overrideError", "chartType", "_s", "accountIndustries", "hookLoading", "hookError", "isError", "refetch", "undefined", "processedData", "useMemo", "length", "map", "industry", "name", "label", "value", "count", "displayText", "toLocaleString", "revenueText", "revenue", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AccountIndustryBarCard", "props", "_c2", "AccountIndustryDoughnutCard", "_c3", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/cards/AccountIndustryCard.tsx"], "sourcesContent": ["/**\n * AccountIndustryCard Component for SkyGeni Dashboard\n * Specialized card for account industry data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useAccountIndustries } from '../../hooks/useData';\nimport { AccountIndustry } from '../../types';\n\ninterface AccountIndustryCardProps {\n  className?: string;\n  elevation?: number;\n  data?: AccountIndustry[];\n  loading?: boolean;\n  error?: string;\n  chartType?: 'bar' | 'doughnut';\n}\n\nconst AccountIndustryCard: React.FC<AccountIndustryCardProps> = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'bar',\n}) => {\n  const {\n    accountIndustries,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch,\n  } = useAccountIndustries();\n\n  const data = overrideData || accountIndustries;\n  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');\n  const error = overrideError || (isError ? hookError : undefined);\n\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n\n    return data.map(industry => ({\n      ...industry,\n      name: industry.industry,\n      label: industry.industry,\n      value: industry.count,\n      displayText: `${industry.industry} (${industry.count.toLocaleString()})`,\n      revenueText: industry.revenue \n        ? `$${industry.revenue.toLocaleString()}` \n        : '',\n    }));\n  }, [data]);\n\n  return (\n    <DataCard\n      title=\"Account Industries\"\n      data={processedData}\n      chartType={chartType}\n      loading={loading}\n      error={error}\n      className={className}\n      elevation={elevation}\n    />\n  );\n};\n\nexport const AccountIndustryBarCard: React.FC<Omit<AccountIndustryCardProps, 'chartType'>> = (props) => (\n  <AccountIndustryCard {...props} chartType=\"bar\" />\n);\n\nexport const AccountIndustryDoughnutCard: React.FC<Omit<AccountIndustryCardProps, 'chartType'>> = (props) => (\n  <AccountIndustryCard {...props} chartType=\"doughnut\" />\n);\n\nexport default AccountIndustryCard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,oBAAoB,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY3D,MAAMC,mBAAuD,GAAGA,CAAC;EAC/DC,SAAS;EACTC,SAAS,GAAG,CAAC;EACbC,IAAI,EAAEC,YAAY;EAClBC,OAAO,EAAEC,eAAe;EACxBC,KAAK,EAAEC,aAAa;EACpBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IACJC,iBAAiB;IACjBN,OAAO,EAAEO,WAAW;IACpBL,KAAK,EAAEM,SAAS;IAChBC,OAAO;IACPC;EACF,CAAC,GAAGlB,oBAAoB,CAAC,CAAC;EAE1B,MAAMM,IAAI,GAAGC,YAAY,IAAIO,iBAAiB;EAC9C,MAAMN,OAAO,GAAGC,eAAe,KAAKU,SAAS,GAAGV,eAAe,GAAIM,WAAW,KAAK,SAAU;EAC7F,MAAML,KAAK,GAAGC,aAAa,KAAKM,OAAO,GAAGD,SAAS,GAAGG,SAAS,CAAC;EAEhE,MAAMC,aAAa,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAM;IACxC,IAAI,CAACf,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOhB,IAAI,CAACiB,GAAG,CAACC,QAAQ,KAAK;MAC3B,GAAGA,QAAQ;MACXC,IAAI,EAAED,QAAQ,CAACA,QAAQ;MACvBE,KAAK,EAAEF,QAAQ,CAACA,QAAQ;MACxBG,KAAK,EAAEH,QAAQ,CAACI,KAAK;MACrBC,WAAW,EAAE,GAAGL,QAAQ,CAACA,QAAQ,KAAKA,QAAQ,CAACI,KAAK,CAACE,cAAc,CAAC,CAAC,GAAG;MACxEC,WAAW,EAAEP,QAAQ,CAACQ,OAAO,GACzB,IAAIR,QAAQ,CAACQ,OAAO,CAACF,cAAc,CAAC,CAAC,EAAE,GACvC;IACN,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACxB,IAAI,CAAC,CAAC;EAEV,oBACEJ,OAAA,CAACH,QAAQ;IACPkC,KAAK,EAAC,oBAAoB;IAC1B3B,IAAI,EAAEc,aAAc;IACpBR,SAAS,EAAEA,SAAU;IACrBJ,OAAO,EAAEA,OAAQ;IACjBE,KAAK,EAAEA,KAAM;IACbN,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA;EAAU;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEN,CAAC;AAACxB,EAAA,CA9CIV,mBAAuD;EAAA,QAcvDH,oBAAoB;AAAA;AAAAsC,EAAA,GAdpBnC,mBAAuD;AAgD7D,OAAO,MAAMoC,sBAA6E,GAAIC,KAAK,iBACjGtC,OAAA,CAACC,mBAAmB;EAAA,GAAKqC,KAAK;EAAE5B,SAAS,EAAC;AAAK;EAAAsB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAClD;AAACI,GAAA,GAFWF,sBAA6E;AAI1F,OAAO,MAAMG,2BAAkF,GAAIF,KAAK,iBACtGtC,OAAA,CAACC,mBAAmB;EAAA,GAAKqC,KAAK;EAAE5B,SAAS,EAAC;AAAU;EAAAsB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CACvD;AAACM,GAAA,GAFWD,2BAAkF;AAI/F,eAAevC,mBAAmB;AAAC,IAAAmC,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}