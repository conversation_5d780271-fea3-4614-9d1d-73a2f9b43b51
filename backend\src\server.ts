/**
 * Server Entry Point for SkyGeni Dashboard Backend
 * 
 * This file starts the Express server and handles:
 * - Port configuration
 * - Server startup logging
 * - Error handling during startup
 * - Environment-specific configurations
 */

import app from './app';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// ============================================================================
// Server Configuration
// ============================================================================

const PORT = process.env.PORT || 5000;
const HOST = process.env.HOST || 'localhost';
const NODE_ENV = process.env.NODE_ENV || 'development';

// ============================================================================
// Server Startup
// ============================================================================

/**
 * Start the Express server
 */
const startServer = async (): Promise<void> => {
  try {
    // Start listening on the specified port
    const server = app.listen(PORT, () => {
      console.log('🚀 ============================================');
      console.log('🚀 SkyGeni Dashboard Backend Server Started');
      console.log('🚀 ============================================');
      console.log(`📍 Environment: ${NODE_ENV}`);
      console.log(`📍 Server URL: http://${HOST}:${PORT}`);
      console.log(`📍 Health Check: http://${HOST}:${PORT}/health`);
      console.log(`📍 API Info: http://${HOST}:${PORT}/api`);
      console.log(`📍 Dashboard Data: http://${HOST}:${PORT}/api/data/dashboard`);
      console.log('🚀 ============================================');
      
      if (NODE_ENV === 'development') {
        console.log('🔧 Development mode - Hot reload enabled');
        console.log('🔧 CORS enabled for localhost:3000 and localhost:3001');
      }
      
      console.log('✅ Server is ready to accept connections');
    });

    // Handle server errors
    server.on('error', (error: NodeJS.ErrnoException) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        console.error('💡 Try using a different port or stop the process using this port');
        process.exit(1);
      } else if (error.code === 'EACCES') {
        console.error(`❌ Permission denied to bind to port ${PORT}`);
        console.error('💡 Try using a port number greater than 1024 or run with elevated privileges');
        process.exit(1);
      } else {
        console.error('❌ Server error:', error);
        process.exit(1);
      }
    });

    // Handle server close
    server.on('close', () => {
      console.log('🛑 Server closed');
    });

    // Graceful shutdown handling
    const gracefulShutdown = (signal: string) => {
      console.log(`🛑 ${signal} received. Starting graceful shutdown...`);
      
      server.close((error) => {
        if (error) {
          console.error('❌ Error during server shutdown:', error);
          process.exit(1);
        }
        
        console.log('✅ Server closed successfully');
        process.exit(0);
      });
      
      // Force close after 10 seconds
      setTimeout(() => {
        console.error('❌ Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    // Register shutdown handlers
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// ============================================================================
// Environment Validation
// ============================================================================

/**
 * Validate required environment variables and configurations
 */
const validateEnvironment = (): void => {
  console.log('🔍 Validating environment configuration...');
  
  // Check Node.js version
  const nodeVersion = process.version || 'v18.0.0';
  const versionParts = nodeVersion.slice(1).split('.');
  const majorVersion = parseInt(versionParts[0] || '18');
  
  if (majorVersion < 18) {
    console.warn(`⚠️  Node.js version ${nodeVersion} detected. Recommended: Node.js 18+`);
  } else {
    console.log(`✅ Node.js version ${nodeVersion} is supported`);
  }
  
  // Log environment variables (without sensitive data)
  console.log('📋 Environment Configuration:');
  console.log(`   NODE_ENV: ${NODE_ENV}`);
  console.log(`   PORT: ${PORT}`);
  console.log(`   HOST: ${HOST}`);
  
  // Validate port number
  const portNum = parseInt(PORT.toString());
  if (isNaN(portNum) || portNum < 1 || portNum > 65535) {
    console.error(`❌ Invalid port number: ${PORT}`);
    process.exit(1);
  }
  
  console.log('✅ Environment validation completed');
};

// ============================================================================
// Application Startup
// ============================================================================

/**
 * Main application startup function
 */
const main = async (): Promise<void> => {
  try {
    console.log('🔄 Starting SkyGeni Dashboard Backend...');
    
    // Validate environment
    validateEnvironment();
    
    // Start the server
    await startServer();
    
  } catch (error) {
    console.error('❌ Application startup failed:', error);
    process.exit(1);
  }
};

// ============================================================================
// Error Handling
// ============================================================================

/**
 * Handle uncaught exceptions
 */
process.on('uncaughtException', (error: Error) => {
  console.error('💥 Uncaught Exception:', error);
  console.error('🛑 Shutting down due to uncaught exception');
  process.exit(1);
});

/**
 * Handle unhandled promise rejections
 */
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  console.error('🛑 Shutting down due to unhandled promise rejection');
  process.exit(1);
});

// ============================================================================
// Start the application
// ============================================================================

// Only start the server if this file is run directly (not imported)
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Failed to start application:', error);
    process.exit(1);
  });
}

// Export for testing purposes
export default app;
