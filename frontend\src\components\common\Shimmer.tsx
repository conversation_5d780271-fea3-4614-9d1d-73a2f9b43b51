/**
 * Shimmer Component for SkyGeni Dashboard
 * 
 * A loading shimmer effect component for better UX during data loading
 */

import React from 'react';
import { Box, Skeleton, Card, CardHeader, CardContent } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface ShimmerProps {
  variant?: 'card' | 'chart' | 'text' | 'rectangular' | 'circular';
  width?: number | string;
  height?: number | string;
  lines?: number;
  className?: string;
}

// ============================================================================
// Shimmer Component
// ============================================================================

const Shimmer: React.FC<ShimmerProps> = ({
  variant = 'rectangular',
  width = '100%',
  height = 200,
  lines = 3,
  className,
}) => {
  if (variant === 'card') {
    return (
      <Card 
        className={className}
        sx={{
          height: '100%',
          borderRadius: 3,
          border: '1px solid rgba(0, 0, 0, 0.05)',
        }}
      >
        <CardHeader
          title={<Skeleton variant="text" width="60%" height={32} />}
          action={<Skeleton variant="circular" width={24} height={24} />}
        />
        <CardContent sx={{ pt: 0 }}>
          <Skeleton 
            variant="rectangular" 
            width="100%" 
            height={250}
            sx={{ borderRadius: 2, mb: 2 }}
          />
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Skeleton variant="text" width="30%" />
            <Skeleton variant="text" width="20%" />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
            <Skeleton variant="text" width="25%" />
            <Skeleton variant="text" width="15%" />
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (variant === 'chart') {
    return (
      <Box className={className} sx={{ width, height, p: 2 }}>
        <Skeleton 
          variant="rectangular" 
          width="100%" 
          height="100%"
          sx={{ borderRadius: 2 }}
        />
      </Box>
    );
  }

  if (variant === 'text') {
    return (
      <Box className={className}>
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton 
            key={index}
            variant="text" 
            width={index === lines - 1 ? '60%' : '100%'}
            sx={{ mb: 0.5 }}
          />
        ))}
      </Box>
    );
  }

  return (
    <Skeleton 
      className={className}
      variant={variant as any}
      width={width}
      height={height}
      sx={{ borderRadius: variant === 'rectangular' ? 2 : 0 }}
    />
  );
};

export default Shimmer;
