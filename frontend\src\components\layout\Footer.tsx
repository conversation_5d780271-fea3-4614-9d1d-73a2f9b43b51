/**
 * Footer Component for SkyGeni Dashboard
 * 
 * Application footer with:
 * - Copyright information
 * - Last updated timestamp
 * - Version information
 * - Links to documentation
 * - Responsive design
 */

import React from 'react';
import {
  Box,
  Typography,
  Link,
  Divider,
  Chip,
  useTheme,
  alpha,
} from '@mui/material';
import {
  GitHub as GitHubIcon,
  Description as DocsIcon,
  Update as UpdateIcon,
} from '@mui/icons-material';
import { FooterProps } from '../../types';

// ============================================================================
// Footer Component
// ============================================================================

const Footer: React.FC<FooterProps> = ({
  showLastUpdated = true,
  lastUpdated,
}) => {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  // ========================================================================
  // Helper Functions
  // ========================================================================

  const formatTimestamp = (timestamp: string | undefined) => {
    if (!timestamp) return new Date().toLocaleString();
    
    try {
      return new Date(timestamp).toLocaleString();
    } catch (error) {
      return new Date().toLocaleString();
    }
  };

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <Box
      component="footer"
      sx={{
        mt: 'auto',
        py: 3,
        px: 2,
        backgroundColor: alpha(theme.palette.background.paper, 0.8),
        borderTop: `1px solid ${theme.palette.divider}`,
        backdropFilter: 'blur(8px)',
      }}
    >
      <Box
        sx={{
          maxWidth: 'lg',
          mx: 'auto',
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          justifyContent: 'space-between',
          alignItems: { xs: 'center', md: 'flex-start' },
          gap: 2,
        }}
      >
        {/* Left Section - Copyright and Info */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: { xs: 'center', md: 'flex-start' },
            gap: 1,
          }}
        >
          <Typography variant="body2" color="text.secondary">
            © {currentYear} SkyGeni Dashboard. Built with React, TypeScript, and D3.js
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Link
              href="#"
              color="inherit"
              underline="hover"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                fontSize: '0.875rem',
                '&:hover': {
                  color: theme.palette.primary.main,
                },
              }}
            >
              <DocsIcon fontSize="small" />
              Documentation
            </Link>
            
            <Link
              href="#"
              color="inherit"
              underline="hover"
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                fontSize: '0.875rem',
                '&:hover': {
                  color: theme.palette.primary.main,
                },
              }}
            >
              <GitHubIcon fontSize="small" />
              GitHub
            </Link>
          </Box>
        </Box>

        {/* Center Section - Version and Status */}
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'row', md: 'column' },
            alignItems: 'center',
            gap: 1,
          }}
        >
          <Chip
            label="v1.0.0"
            size="small"
            variant="outlined"
            color="primary"
            sx={{ fontSize: '0.75rem' }}
          />
          
          <Chip
            label="Production"
            size="small"
            variant="outlined"
            color="success"
            sx={{ fontSize: '0.75rem' }}
          />
        </Box>

        {/* Right Section - Last Updated */}
        {showLastUpdated && (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: { xs: 'center', md: 'flex-end' },
              gap: 1,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                color: 'text.secondary',
              }}
            >
              <UpdateIcon fontSize="small" />
              <Typography variant="caption">
                Last Updated
              </Typography>
            </Box>
            
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{
                fontFamily: 'monospace',
                fontSize: '0.75rem',
                backgroundColor: alpha(theme.palette.background.default, 0.5),
                px: 1,
                py: 0.5,
                borderRadius: 1,
                border: `1px solid ${theme.palette.divider}`,
              }}
            >
              {formatTimestamp(lastUpdated)}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Divider */}
      <Divider sx={{ my: 2 }} />

      {/* Bottom Section - Additional Info */}
      <Box
        sx={{
          maxWidth: 'lg',
          mx: 'auto',
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          justifyContent: 'space-between',
          alignItems: 'center',
          gap: 1,
        }}
      >
        <Typography variant="caption" color="text.secondary">
          Dashboard for data visualization and analytics
        </Typography>
        
        <Typography variant="caption" color="text.secondary">
          Made with ❤️ for better data insights
        </Typography>
      </Box>
    </Box>
  );
};

export default Footer;
