/**
 * ACVRangeCard Component for SkyGeni Dashboard
 * Specialized card for ACV range data visualization
 */

import React from 'react';
import DataCard from './DataCard';
import { useACVRanges } from '../../hooks/useData';
import { ACVRange } from '../../types';

interface ACVRangeCardProps {
  className?: string;
  elevation?: number;
  data?: ACVRange[];
  loading?: boolean;
  error?: string;
  chartType?: 'bar' | 'doughnut';
}

const ACVRangeCard: React.FC<ACVRangeCardProps> = ({
  className,
  elevation = 2,
  data: overrideData,
  loading: overrideLoading,
  error: overrideError,
  chartType = 'doughnut',
}) => {
  const {
    acvRanges,
    loading: hookLoading,
    error: hookError,
    isError,
    refetch,
  } = useACVRanges();

  const data = overrideData || acvRanges;
  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');
  const error = overrideError || (isError ? hookError : undefined);

  const processedData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data.map(acvRange => ({
      ...acvRange,
      name: acvRange.range,
      label: acvRange.range,
      value: acvRange.count,
      count: acvRange.count,
      displayText: `${acvRange.range} (${acvRange.count} contracts)`,
      valueText: `$${acvRange.totalValue.toLocaleString()}`,
      percentageText: acvRange.percentage 
        ? `${acvRange.percentage.toFixed(1)}%` 
        : '',
    }));
  }, [data]);

  return (
    <DataCard
      title="ACV Ranges"
      data={processedData}
      chartType={chartType}
      loading={loading}
      error={error}
      className={className}
      elevation={elevation}
    />
  );
};

export const ACVRangeBarCard: React.FC<Omit<ACVRangeCardProps, 'chartType'>> = (props) => (
  <ACVRangeCard {...props} chartType="bar" />
);

export const ACVRangeDoughnutCard: React.FC<Omit<ACVRangeCardProps, 'chartType'>> = (props) => (
  <ACVRangeCard {...props} chartType="doughnut" />
);

export default ACVRangeCard;
