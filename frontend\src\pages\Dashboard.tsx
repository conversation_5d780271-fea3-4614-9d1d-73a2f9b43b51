/**
 * Dashboard Page for SkyGeni Dashboard
 * 
 * Main dashboard page that displays:
 * - Overview cards with charts
 * - Data visualization for all data types
 * - Responsive grid layout
 * - Loading and error states
 * - Real-time data updates
 */

import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  CustomerTypeCard,
  AccountIndustryCard,
  TeamCard,
  ACVRangeCard,
} from '../components/cards';
import Layout from '../components/layout/Layout';
import Loader from '../components/common/Loader';
import ErrorMessage from '../components/common/ErrorMessage';
import { useData } from '../hooks/useData';

// ============================================================================
// Dashboard Component
// ============================================================================

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const {
    dashboardData,
    isLoading,
    isError,
    error,
    isEmpty,
    refetch,
    lastFetched,
  } = useData({
    autoFetch: true,
    refreshInterval: 0, // Disable auto-refresh for now
  });

  // ========================================================================
  // Summary Statistics Component
  // ========================================================================

  const SummaryStats: React.FC = () => {
    if (!dashboardData?.summary) return null;

    const { summary } = dashboardData;

    return (
      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 3,
          background: `linear-gradient(135deg, ${theme.palette.primary.main}15, ${theme.palette.secondary.main}15)`,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <Typography variant="h5" gutterBottom fontWeight={600}>
          Dashboard Overview
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="primary" fontWeight="bold">
                {summary.totalCustomers.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Customers
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="secondary" fontWeight="bold">
                ${summary.totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Revenue
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="success.main" fontWeight="bold">
                {summary.totalTeams}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active Teams
              </Typography>
            </Box>
          </Grid>
          
          <Grid item xs={6} sm={3}>
            <Box textAlign="center">
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                ${summary.averageACV.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Average ACV
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  // ========================================================================
  // Loading State
  // ========================================================================

  if (isLoading) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Loader
            size="large"
            message="Loading dashboard data..."
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Error State
  // ========================================================================

  if (isError) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <ErrorMessage
            error={error}
            title="Failed to Load Dashboard"
            showRetryButton={true}
            onRetry={refetch}
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Empty State
  // ========================================================================

  if (isEmpty) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Alert
            severity="info"
            action={
              <button onClick={refetch}>
                Retry
              </button>
            }
          >
            No dashboard data available. Please check your data sources.
          </Alert>
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Main Dashboard Content
  // ========================================================================

  return (
    <Layout title="SkyGeni Dashboard">
      <Fade in={true} timeout={500}>
        <Box>
          {/* Page Header */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom fontWeight={600}>
              Data Analytics Dashboard
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Comprehensive view of customer types, account industries, teams, and ACV ranges
            </Typography>
            {lastFetched && (
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                Last updated: {new Date(lastFetched).toLocaleString()}
              </Typography>
            )}
          </Box>

          {/* Summary Statistics */}
          <SummaryStats />

          {/* Data Cards Grid */}
          <Grid container spacing={3}>
            {/* Customer Types Card */}
            <Grid item xs={12} md={6} lg={6}>
              <Fade in={true} timeout={600}>
                <Box>
                  <CustomerTypeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Account Industries Card */}
            <Grid item xs={12} md={6} lg={6}>
              <Fade in={true} timeout={700}>
                <Box>
                  <AccountIndustryCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Teams Card */}
            <Grid item xs={12} md={6} lg={6}>
              <Fade in={true} timeout={800}>
                <Box>
                  <TeamCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* ACV Ranges Card */}
            <Grid item xs={12} md={6} lg={6}>
              <Fade in={true} timeout={900}>
                <Box>
                  <ACVRangeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>
          </Grid>

          {/* Additional Information */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.
            </Typography>
          </Box>
        </Box>
      </Fade>
    </Layout>
  );
};

export default Dashboard;
