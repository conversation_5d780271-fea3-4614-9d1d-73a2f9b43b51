/**
 * Dashboard Page for SkyGeni Dashboard
 * 
 * Main dashboard page that displays:
 * - Overview cards with charts
 * - Data visualization for all data types
 * - Responsive grid layout
 * - Loading and error states
 * - Real-time data updates
 */

import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  CustomerTypeCard,
  AccountIndustryCard,
  TeamCard,
  ACVRangeCard,
} from '../components/cards';
import Layout from '../components/layout/Layout';
import Loader from '../components/common/Loader';
import ErrorMessage from '../components/common/ErrorMessage';
import { useData } from '../hooks/useData';

// ============================================================================
// Dashboard Component
// ============================================================================

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const {
    dashboardData,
    isLoading,
    isError,
    error,
    isEmpty,
    refetch,
    lastFetched,
  } = useData({
    autoFetch: true,
    refreshInterval: 0, // Disable auto-refresh for now
  });

  // ========================================================================
  // Summary Statistics Component
  // ========================================================================

  const SummaryStats: React.FC = () => {
    if (!dashboardData?.summary) return null;

    const { summary } = dashboardData;

    return (
      <Paper
        elevation={3}
        sx={{
          p: { xs: 3, md: 4 },
          mb: { xs: 3, md: 4 },
          background: `linear-gradient(135deg, ${theme.palette.primary.main}08, ${theme.palette.secondary.main}08)`,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 3,
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
          },
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" fontWeight={700} sx={{
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}>
            📊 Dashboard Overview
          </Typography>
          {lastFetched && (
            <Box sx={{ ml: 'auto', textAlign: 'right' }}>
              <Typography variant="caption" color="text.secondary">
                Last updated
              </Typography>
              <Typography variant="body2" fontWeight={500}>
                {new Date(lastFetched).toLocaleString()}
              </Typography>
            </Box>
          )}
        </Box>

        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          <Grid item xs={6} sm={3}>
            <Box
              sx={{
                textAlign: 'center',
                p: { xs: 2, sm: 3 },
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.7)',
                border: '1px solid rgba(0, 0, 0, 0.05)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>
                👥
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  color: 'primary.main',
                  fontWeight: 700,
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  mb: 1,
                }}
              >
                {summary.totalCustomers.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
              >
                Total Customers
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box
              sx={{
                textAlign: 'center',
                p: { xs: 2, sm: 3 },
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.7)',
                border: '1px solid rgba(0, 0, 0, 0.05)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>
                💰
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  color: 'success.main',
                  fontWeight: 700,
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  mb: 1,
                }}
              >
                ${summary.totalRevenue.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
              >
                Total Revenue
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box
              sx={{
                textAlign: 'center',
                p: { xs: 2, sm: 3 },
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.7)',
                border: '1px solid rgba(0, 0, 0, 0.05)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>
                🏢
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  color: 'info.main',
                  fontWeight: 700,
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  mb: 1,
                }}
              >
                {summary.totalTeams}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
              >
                Active Teams
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box
              sx={{
                textAlign: 'center',
                p: { xs: 2, sm: 3 },
                borderRadius: 2,
                background: 'rgba(255, 255, 255, 0.7)',
                border: '1px solid rgba(0, 0, 0, 0.05)',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-2px)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                },
              }}
            >
              <Typography variant="h2" sx={{ fontSize: '2rem', mb: 1 }}>
                📈
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  color: 'warning.main',
                  fontWeight: 700,
                  fontSize: { xs: '1.5rem', sm: '2rem' },
                  mb: 1,
                }}
              >
                ${Math.round(summary.averageACV).toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
                fontWeight={500}
                sx={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
              >
                Average ACV
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  // ========================================================================
  // Loading State
  // ========================================================================

  if (isLoading) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Loader
            size="large"
            message="Loading dashboard data..."
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Error State
  // ========================================================================

  if (isError) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <ErrorMessage
            error={error}
            title="Failed to Load Dashboard"
            showRetryButton={true}
            onRetry={refetch}
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Empty State
  // ========================================================================

  if (isEmpty) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Alert
            severity="info"
            action={
              <button onClick={refetch}>
                Retry
              </button>
            }
          >
            No dashboard data available. Please check your data sources.
          </Alert>
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Main Dashboard Content
  // ========================================================================

  return (
    <Layout title="SkyGeni Dashboard">
      <Fade in={true} timeout={500}>
        <Box>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 3, md: 4 } }}>
            <Typography
              variant="h3"
              component="h1"
              gutterBottom
              fontWeight={700}
              sx={{
                background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1,
              }}
            >
              Data Analytics Dashboard
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{ fontSize: '1.1rem', fontWeight: 500 }}
            >
              Comprehensive insights into customer types, account industries, teams, and ACV ranges
            </Typography>
          </Box>

          {/* Summary Statistics */}
          <SummaryStats />

          {/* Data Cards Grid */}
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            {/* Customer Types Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={600}>
                <Box sx={{ height: '100%' }}>
                  <CustomerTypeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Account Industries Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={700}>
                <Box sx={{ height: '100%' }}>
                  <AccountIndustryCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Teams Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={800}>
                <Box sx={{ height: '100%' }}>
                  <TeamCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* ACV Ranges Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={900}>
                <Box sx={{ height: '100%' }}>
                  <ACVRangeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>
          </Grid>

          {/* Additional Information */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body2" color="text.secondary">
              Dashboard automatically refreshes data. Click the refresh button in the header to manually update.
            </Typography>
          </Box>
        </Box>
      </Fade>
    </Layout>
  );
};

export default Dashboard;
