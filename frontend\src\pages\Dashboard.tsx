/**
 * Dashboard Page for SkyGeni Dashboard
 * 
 * Main dashboard page that displays:
 * - Overview cards with charts
 * - Data visualization for all data types
 * - Responsive grid layout
 * - Loading and error states
 * - Real-time data updates
 */

import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  CustomerTypeCard,
  AccountIndustryCard,
  TeamCard,
  ACVRangeCard,
} from '../components/cards';
import Layout from '../components/layout/Layout';
import Loader from '../components/common/Loader';
import ErrorMessage from '../components/common/ErrorMessage';
import { useData } from '../hooks/useData';

// ============================================================================
// Dashboard Component
// ============================================================================

const Dashboard: React.FC = () => {
  const theme = useTheme();
  const {
    dashboardData,
    isLoading,
    isError,
    error,
    isEmpty,
    refetch,
    lastFetched,
  } = useData({
    autoFetch: true,
    refreshInterval: 0, // Disable auto-refresh for now
  });

  // ========================================================================
  // Summary Statistics Component
  // ========================================================================

  const SummaryStats: React.FC = () => {
    if (!dashboardData?.summary) return null;

    const { summary } = dashboardData;

    return (
      <Paper
        elevation={1}
        sx={{
          p: 2,
          mb: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 1,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" fontWeight={600} color="text.primary">
            Overview
          </Typography>
          {lastFetched && (
            <Typography variant="caption" color="text.secondary">
              Last updated: {new Date(lastFetched).toLocaleString()}
            </Typography>
          )}
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1.5 }}>
              <Typography
                variant="h5"
                sx={{
                  color: 'primary.main',
                  fontWeight: 600,
                  mb: 0.5,
                }}
              >
                {summary.totalCustomers.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
              >
                Total Customers
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1.5 }}>
              <Typography
                variant="h5"
                sx={{
                  color: 'success.main',
                  fontWeight: 600,
                  mb: 0.5,
                }}
              >
                ${summary.totalRevenue.toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
              >
                Total Revenue
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1.5 }}>
              <Typography
                variant="h5"
                sx={{
                  color: 'info.main',
                  fontWeight: 600,
                  mb: 0.5,
                }}
              >
                {summary.totalTeams}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
              >
                Active Teams
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center', p: 1.5 }}>
              <Typography
                variant="h5"
                sx={{
                  color: 'warning.main',
                  fontWeight: 600,
                  mb: 0.5,
                }}
              >
                ${Math.round(summary.averageACV).toLocaleString()}
              </Typography>
              <Typography
                variant="body2"
                color="text.secondary"
              >
                Average ACV
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    );
  };

  // ========================================================================
  // Loading State
  // ========================================================================

  if (isLoading) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Loader
            size="large"
            message="Loading dashboard data..."
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Error State
  // ========================================================================

  if (isError) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <ErrorMessage
            error={error}
            title="Failed to Load Dashboard"
            showRetryButton={true}
            onRetry={refetch}
            centered={true}
          />
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Empty State
  // ========================================================================

  if (isEmpty) {
    return (
      <Layout title="SkyGeni Dashboard">
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '60vh',
          }}
        >
          <Alert
            severity="info"
            action={
              <button onClick={refetch}>
                Retry
              </button>
            }
          >
            No dashboard data available. Please check your data sources.
          </Alert>
        </Box>
      </Layout>
    );
  }

  // ========================================================================
  // Main Dashboard Content
  // ========================================================================

  return (
    <Layout title="SkyGeni Dashboard">
      <Fade in={true} timeout={500}>
        <Box>
          {/* Page Header */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h5"
              component="h1"
              gutterBottom
              fontWeight={600}
              color="text.primary"
              sx={{ mb: 0.5 }}
            >
              Data Analytics Dashboard
            </Typography>
            <Typography
              variant="body2"
              color="text.secondary"
            >
              Customer insights and performance metrics
            </Typography>
          </Box>

          {/* Summary Statistics */}
          <SummaryStats />

          {/* Data Cards Grid */}
          <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
            {/* Customer Types Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={600}>
                <Box sx={{ height: '100%' }}>
                  <CustomerTypeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Account Industries Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={700}>
                <Box sx={{ height: '100%' }}>
                  <AccountIndustryCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* Teams Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={800}>
                <Box sx={{ height: '100%' }}>
                  <TeamCard elevation={3} />
                </Box>
              </Fade>
            </Grid>

            {/* ACV Ranges Card */}
            <Grid item xs={12} sm={6} lg={6} xl={3}>
              <Fade in={true} timeout={900}>
                <Box sx={{ height: '100%' }}>
                  <ACVRangeCard elevation={3} />
                </Box>
              </Fade>
            </Grid>
          </Grid>


        </Box>
      </Fade>
    </Layout>
  );
};

export default Dashboard;
