{
  "compilerOptions": {
    /* Language and Environment */
    "target": "ES2020",
    "lib": [
      "dom",
      "dom.iterable",
      "ES6",
      "ES2020"
    ],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    
    /* Module Resolution */
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
    /* Type Checking */
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "exactOptionalPropertyTypes": false,
    "noUncheckedIndexedAccess": false,
    
    /* Additional Options */
    "baseUrl": "src",
    "paths": {
      "@/*": ["*"],
      "@/components/*": ["components/*"],
      "@/hooks/*": ["hooks/*"],
      "@/redux/*": ["redux/*"],
      "@/api/*": ["api/*"],
      "@/types/*": ["types/*"],
      "@/styles/*": ["styles/*"],
      "@/pages/*": ["pages/*"]
    },
    
    /* Emit */
    "declaration": false,
    "sourceMap": true,
    "removeComments": false,
    "importHelpers": true,
    
    /* Interop Constraints */
    "verbatimModuleSyntax": false
  },
  "include": [
    "src/**/*",
    "src/**/*.ts",
    "src/**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "build",
    "dist",
    "**/*.test.ts",
    "**/*.test.tsx",
    "**/*.spec.ts",
    "**/*.spec.tsx"
  ]
}
