/**
 * DonutChart Component using D3.js
 * Professional donut chart with center text and hover interactions
 */

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Box, Tooltip } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface DonutChartData {
  label: string;
  value: number;
  color?: string;
}

interface DonutChartProps {
  data: DonutChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  centerText?: string;
  colors?: string[];
  onSegmentHover?: (data: DonutChartData | null) => void;
}

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_COLORS = [
  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',
  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'
];

// ============================================================================
// DonutChart Component
// ============================================================================

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  width = 300,
  height = 300,
  innerRadius = 60,
  outerRadius = 120,
  centerText,
  colors = DEFAULT_COLORS,
  onSegmentHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    content: string;
    x: number;
    y: number;
  }>({
    visible: false,
    content: '',
    x: 0,
    y: 0,
  });

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const radius = Math.min(width, height) / 2;
    const g = svg
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Create pie generator
    const pie = d3
      .pie<DonutChartData>()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create hover arc generator
    const hoverArc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius + 5);

    // Calculate total for percentages
    const total = data.reduce((sum, d) => sum + d.value, 0);

    // Create arcs
    const arcs = g
      .selectAll('.arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add paths
    arcs
      .append('path')
      .attr('d', arc)
      .attr('fill', (d, i) => d.data.color || colors[i % colors.length])
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseenter', function(event, d) {
        // Hover effect
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', hoverArc);

        // Show tooltip
        const percentage = ((d.data.value / total) * 100).toFixed(1);
        const content = `${d.data.label}: ${d.data.value.toLocaleString()} (${percentage}%)`;
        
        setTooltip({
          visible: true,
          content,
          x: event.pageX,
          y: event.pageY,
        });

        onSegmentHover?.(d.data);
      })
      .on('mouseleave', function(event, d) {
        // Remove hover effect
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', arc);

        // Hide tooltip
        setTooltip(prev => ({ ...prev, visible: false }));
        onSegmentHover?.(null);
      })
      .on('mousemove', function(event) {
        setTooltip(prev => ({
          ...prev,
          x: event.pageX,
          y: event.pageY,
        }));
      });

    // Add center text
    if (centerText) {
      g.append('text')
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .style('font-size', '18px')
        .style('font-weight', '600')
        .style('fill', '#333')
        .text(centerText);
    }

    // Add total count in center
    g.append('text')
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .attr('y', centerText ? 20 : 0)
      .style('font-size', '24px')
      .style('font-weight', '700')
      .style('fill', '#1976d2')
      .text(total.toLocaleString());

    // Animate on mount
    arcs
      .selectAll('path')
      .style('opacity', 0)
      .transition()
      .duration(800)
      .delay((d, i) => i * 100)
      .style('opacity', 1);

  }, [data, width, height, innerRadius, outerRadius, centerText, colors, onSegmentHover]);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
      
      {tooltip.visible && (
        <Box
          sx={{
            position: 'fixed',
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: 'white',
            padding: '8px 12px',
            borderRadius: '4px',
            fontSize: '14px',
            pointerEvents: 'none',
            zIndex: 1000,
            transform: 'translate(-50%, -100%)',
          }}
        >
          {tooltip.content}
        </Box>
      )}
    </Box>
  );
};

export default DonutChart;
