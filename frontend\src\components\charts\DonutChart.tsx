/**
 * DonutChart Component using D3.js
 * Professional donut chart with center text and hover interactions
 */

import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import { Box, Tooltip } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface DonutChartData {
  label: string;
  value: number;
  color?: string;
}

interface DonutChartProps {
  data: DonutChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  centerText?: string;
  colors?: string[];
  onSegmentHover?: (data: DonutChartData | null) => void;
}

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_COLORS = [
  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',
  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'
];

// ============================================================================
// DonutChart Component
// ============================================================================

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  width = 300,
  height = 300,
  innerRadius = 60,
  outerRadius = 120,
  centerText,
  colors = DEFAULT_COLORS,
  onSegmentHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [tooltip, setTooltip] = useState<{
    visible: boolean;
    content: string;
    x: number;
    y: number;
  }>({
    visible: false,
    content: '',
    x: 0,
    y: 0,
  });

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const radius = Math.min(width, height) / 2;
    const g = svg
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Create pie generator
    const pie = d3
      .pie<DonutChartData>()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create hover arc generator
    const hoverArc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius + 5);

    // Calculate total for percentages
    const total = data.reduce((sum, d) => sum + d.value, 0);

    // Create arcs
    const arcs = g
      .selectAll('.arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add paths
    arcs
      .append('path')
      .attr('d', arc)
      .attr('fill', (d, i) => d.data.color || colors[i % colors.length])
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseenter', function(event, d) {
        // Hover effect - expand segment
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', hoverArc)
          .style('filter', 'brightness(1.1)');

        // Show detailed tooltip
        const percentage = ((d.data.value / total) * 100).toFixed(1);
        const content = `
          <div style="font-weight: 600; margin-bottom: 4px; color: #fff;">${d.data.label}</div>
          <div style="margin-bottom: 2px;">Value: <strong>${d.data.value.toLocaleString()}</strong></div>
          <div>Percentage: <strong>${percentage}%</strong></div>
        `;

        setTooltip({
          visible: true,
          content,
          x: event.pageX,
          y: event.pageY,
        });

        onSegmentHover?.(d.data);
      })
      .on('mouseleave', function(event, d) {
        // Remove hover effect
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', arc)
          .style('filter', 'brightness(1)');

        // Hide tooltip
        setTooltip(prev => ({ ...prev, visible: false }));
        onSegmentHover?.(null);
      })
      .on('mousemove', function(event) {
        setTooltip(prev => ({
          ...prev,
          x: event.pageX,
          y: event.pageY,
        }));
      });

    // Add center content
    const centerGroup = g.append('g').attr('class', 'center-content');

    // Add center label if provided
    if (centerText) {
      centerGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .attr('y', -8)
        .style('font-size', '14px')
        .style('font-weight', '500')
        .style('fill', '#666')
        .text(centerText);
    }

    // Add bold total count in center
    centerGroup.append('text')
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .attr('y', centerText ? 12 : 0)
      .style('font-size', '28px')
      .style('font-weight', '700')
      .style('fill', '#1976d2')
      .text(total.toLocaleString());

    // Add percentage labels on segments
    const labelArcs = g
      .selectAll('.label-arc')
      .data(pie(data))
      .enter()
      .append('g')
      .attr('class', 'label-arc');

    // Add percentage text on segments
    labelArcs
      .append('text')
      .attr('transform', d => {
        const centroid = arc.centroid(d);
        return `translate(${centroid[0]}, ${centroid[1]})`;
      })
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .style('font-size', '12px')
      .style('font-weight', '600')
      .style('fill', 'white')
      .style('text-shadow', '1px 1px 2px rgba(0,0,0,0.7)')
      .text(d => {
        const percentage = ((d.data.value / total) * 100);
        return percentage > 5 ? `${percentage.toFixed(1)}%` : ''; // Only show if > 5%
      });

    // Animate on mount with scale and opacity
    arcs
      .selectAll('path')
      .style('opacity', 0)
      .style('transform', 'scale(0)')
      .transition()
      .duration(800)
      .delay((d, i) => i * 150)
      .style('opacity', 1)
      .style('transform', 'scale(1)')
      .ease(d3.easeBounceOut);

    // Animate center content
    centerGroup
      .style('opacity', 0)
      .style('transform', 'scale(0)')
      .transition()
      .duration(600)
      .delay(400)
      .style('opacity', 1)
      .style('transform', 'scale(1)')
      .ease(d3.easeBackOut);

    // Animate percentage labels
    labelArcs
      .selectAll('text')
      .style('opacity', 0)
      .transition()
      .duration(400)
      .delay((d, i) => 800 + i * 100)
      .style('opacity', 1);

  }, [data, width, height, innerRadius, outerRadius, centerText, colors, onSegmentHover]);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
      
      {tooltip.visible && (
        <Box
          sx={{
            position: 'fixed',
            left: tooltip.x + 10,
            top: tooltip.y - 10,
            backgroundColor: 'rgba(0, 0, 0, 0.9)',
            color: 'white',
            padding: '12px 16px',
            borderRadius: '8px',
            fontSize: '13px',
            pointerEvents: 'none',
            zIndex: 1000,
            transform: 'translate(-50%, -100%)',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(10px)',
          }}
          dangerouslySetInnerHTML={{ __html: tooltip.content }}
        />
      )}
    </Box>
  );
};

export default DonutChart;
