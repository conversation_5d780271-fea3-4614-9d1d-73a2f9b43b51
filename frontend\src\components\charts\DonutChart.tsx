/**
 * DonutChart Component using D3.js
 * Professional donut chart with center text and hover interactions
 */

import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';
import { Box } from '@mui/material';

// ============================================================================
// Types
// ============================================================================

interface DonutChartData {
  label: string;
  value: number;
  color?: string;
  originalData?: any; // Preserve original data for detailed tooltips
}

interface DonutChartProps {
  data: DonutChartData[];
  width?: number;
  height?: number;
  innerRadius?: number;
  outerRadius?: number;
  centerText?: string;
  colors?: string[];
  onSegmentHover?: (data: DonutChartData | null) => void;
}

// ============================================================================
// Default Configuration
// ============================================================================

const DEFAULT_COLORS = [
  '#3f51b5', '#f50057', '#ff9800', '#4caf50', '#9c27b0',
  '#00bcd4', '#795548', '#607d8b', '#e91e63', '#2196f3'
];

// ============================================================================
// DonutChart Component
// ============================================================================

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  width = 300,
  height = 300,
  innerRadius = 60,
  outerRadius = 120,
  centerText,
  colors = DEFAULT_COLORS,
  onSegmentHover,
}) => {
  const svgRef = useRef<SVGSVGElement>(null);
  // Note: Using D3.js native tooltips instead of React state for better performance

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Validate and clean data
    const validData = data.filter(d => d && typeof d.value === 'number' && d.value > 0 && d.label);
    if (validData.length === 0) return;

    const g = svg
      .append('g')
      .attr('transform', `translate(${width / 2}, ${height / 2})`);

    // Create pie generator
    const pie = d3
      .pie<DonutChartData>()
      .value(d => d.value)
      .sort(null);

    // Create arc generator
    const arc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius);

    // Create hover arc generator
    const hoverArc = d3
      .arc<d3.PieArcDatum<DonutChartData>>()
      .innerRadius(innerRadius)
      .outerRadius(outerRadius + 5);

    // Calculate total for percentages
    const total = validData.reduce((sum, d) => sum + d.value, 0);

    // Create arcs
    const arcs = g
      .selectAll('.arc')
      .data(pie(validData))
      .enter()
      .append('g')
      .attr('class', 'arc');

    // Add paths
    arcs
      .append('path')
      .attr('d', arc)
      .attr('fill', (d, i) => d.data.color || colors[i % colors.length])
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .style('cursor', 'pointer')
      .on('mouseover', function(event, d) {
        // Get mouse position using d3.pointer for accurate positioning
        const [mouseX, mouseY] = d3.pointer(event, document.body);

        // Hover effect - expand segment
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', hoverArc)
          .style('filter', 'brightness(1.1)');

        // Calculate percentage of total
        const percentage = ((d.data.value / total) * 100).toFixed(1);

        // Create tooltip content based on data type
        let label = d.data.label;
        let absoluteValue = d.data.value.toLocaleString();

        // Enhanced content for specific data types
        if (d.data.originalData) {
          const original = d.data.originalData;

          if (original.type) {
            // Customer Types
            label = original.type;
            absoluteValue = `${original.count} customers`;
          } else if (original.range) {
            // ACV Ranges
            label = original.range;
            absoluteValue = `${original.count} accounts`;
          }
        }

        // Create clean tooltip with white background and shadow
        const tooltip = d3.select('body')
          .selectAll('.d3-tooltip')
          .data([0]);

        const tooltipEnter = tooltip.enter()
          .append('div')
          .attr('class', 'd3-tooltip')
          .style('position', 'absolute')
          .style('background', 'white')
          .style('border', '1px solid #ddd')
          .style('border-radius', '8px')
          .style('padding', '12px')
          .style('font-size', '13px')
          .style('font-family', 'system-ui, -apple-system, sans-serif')
          .style('box-shadow', '0 4px 12px rgba(0, 0, 0, 0.15)')
          .style('pointer-events', 'none')
          .style('z-index', '1000')
          .style('opacity', 0);

        const tooltipUpdate = tooltipEnter.merge(tooltip);

        // Update tooltip content
        tooltipUpdate.html(`
          <div style="font-weight: 600; margin-bottom: 6px; color: #333;">${label}</div>
          <div style="margin-bottom: 4px; color: #666;">Value: <strong style="color: #1976d2;">${absoluteValue}</strong></div>
          <div style="color: #666;">Percentage: <strong style="color: #1976d2;">${percentage}%</strong></div>
        `);

        // Position and show tooltip
        tooltipUpdate
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px')
          .transition()
          .duration(200)
          .style('opacity', 1);

        onSegmentHover?.(d.data);
      })
      .on('mousemove', function(event) {
        // Update tooltip position to follow mouse
        const [mouseX, mouseY] = d3.pointer(event, document.body);

        d3.select('.d3-tooltip')
          .style('left', (mouseX + 15) + 'px')
          .style('top', (mouseY - 10) + 'px');
      })
      .on('mouseout', function(event, d) {
        // Remove hover effect
        d3.select(this)
          .transition()
          .duration(200)
          .attr('d', arc)
          .style('filter', 'brightness(1)');

        // Hide and remove tooltip
        d3.select('.d3-tooltip')
          .transition()
          .duration(200)
          .style('opacity', 0)
          .remove();

        onSegmentHover?.(null);
      });

    // Add center content
    const centerGroup = g.append('g').attr('class', 'center-content');

    // Add center label if provided
    if (centerText) {
      centerGroup.append('text')
        .attr('text-anchor', 'middle')
        .attr('dominant-baseline', 'middle')
        .attr('y', -8)
        .style('font-size', '14px')
        .style('font-weight', '500')
        .style('fill', '#666')
        .text(centerText);
    }

    // Add bold total count in center
    centerGroup.append('text')
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .attr('y', centerText ? 12 : 0)
      .style('font-size', '28px')
      .style('font-weight', '700')
      .style('fill', '#1976d2')
      .text(total.toLocaleString());

    // Add percentage labels on segments
    const labelArcs = g
      .selectAll('.label-arc')
      .data(pie(validData))
      .enter()
      .append('g')
      .attr('class', 'label-arc');

    // Add percentage text on segments
    labelArcs
      .append('text')
      .attr('transform', d => {
        const centroid = arc.centroid(d);
        // Move text slightly outward for better visibility
        const factor = 1.1;
        return `translate(${centroid[0] * factor}, ${centroid[1] * factor})`;
      })
      .attr('text-anchor', 'middle')
      .attr('dominant-baseline', 'middle')
      .style('font-size', '11px')
      .style('font-weight', '700')
      .style('fill', 'white')
      .style('text-shadow', '2px 2px 4px rgba(0,0,0,0.8)')
      .style('stroke', 'rgba(0,0,0,0.5)')
      .style('stroke-width', '0.5px')
      .style('paint-order', 'stroke fill')
      .text(d => {
        const percentage = ((d.data.value / total) * 100);
        return percentage > 3 ? `${percentage.toFixed(1)}%` : ''; // Show if > 3%
      });

    // Animate on mount with scale and opacity
    arcs
      .selectAll('path')
      .style('opacity', 0)
      .style('transform', 'scale(0)')
      .transition()
      .duration(800)
      .delay((d, i) => i * 150)
      .style('opacity', 1)
      .style('transform', 'scale(1)')
      .ease(d3.easeBounceOut);

    // Animate center content
    centerGroup
      .style('opacity', 0)
      .style('transform', 'scale(0)')
      .transition()
      .duration(600)
      .delay(400)
      .style('opacity', 1)
      .style('transform', 'scale(1)')
      .ease(d3.easeBackOut);

    // Animate percentage labels
    labelArcs
      .selectAll('text')
      .style('opacity', 0)
      .transition()
      .duration(400)
      .delay((d, i) => 800 + i * 100)
      .style('opacity', 1);

  }, [data, width, height, innerRadius, outerRadius, centerText, colors, onSegmentHover]);

  return (
    <Box sx={{ position: 'relative', display: 'inline-block' }}>
      <svg
        ref={svgRef}
        width={width}
        height={height}
        style={{ overflow: 'visible' }}
      />
      
      {/* Tooltips are now handled by D3.js directly */}
    </Box>
  );
};

export default DonutChart;
