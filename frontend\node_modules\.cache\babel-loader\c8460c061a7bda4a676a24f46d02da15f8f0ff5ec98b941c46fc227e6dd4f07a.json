{"ast": null, "code": "import { Selection } from \"./index.js\";\nexport default function (context) {\n  var selection = context.selection ? context.selection() : context;\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n  return new Selection(merges, this._parents);\n}", "map": {"version": 3, "names": ["Selection", "context", "selection", "groups0", "_groups", "groups1", "m0", "length", "m1", "m", "Math", "min", "merges", "Array", "j", "group0", "group1", "n", "merge", "node", "i", "_parents"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/merge.js"], "sourcesContent": ["import {Selection} from \"./index.js\";\n\nexport default function(context) {\n  var selection = context.selection ? context.selection() : context;\n\n  for (var groups0 = this._groups, groups1 = selection._groups, m0 = groups0.length, m1 = groups1.length, m = Math.min(m0, m1), merges = new Array(m0), j = 0; j < m; ++j) {\n    for (var group0 = groups0[j], group1 = groups1[j], n = group0.length, merge = merges[j] = new Array(n), node, i = 0; i < n; ++i) {\n      if (node = group0[i] || group1[i]) {\n        merge[i] = node;\n      }\n    }\n  }\n\n  for (; j < m0; ++j) {\n    merges[j] = groups0[j];\n  }\n\n  return new Selection(merges, this._parents);\n}\n"], "mappings": "AAAA,SAAQA,SAAS,QAAO,YAAY;AAEpC,eAAe,UAASC,OAAO,EAAE;EAC/B,IAAIC,SAAS,GAAGD,OAAO,CAACC,SAAS,GAAGD,OAAO,CAACC,SAAS,CAAC,CAAC,GAAGD,OAAO;EAEjE,KAAK,IAAIE,OAAO,GAAG,IAAI,CAACC,OAAO,EAAEC,OAAO,GAAGH,SAAS,CAACE,OAAO,EAAEE,EAAE,GAAGH,OAAO,CAACI,MAAM,EAAEC,EAAE,GAAGH,OAAO,CAACE,MAAM,EAAEE,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACL,EAAE,EAAEE,EAAE,CAAC,EAAEI,MAAM,GAAG,IAAIC,KAAK,CAACP,EAAE,CAAC,EAAEQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,CAAC,EAAE,EAAEK,CAAC,EAAE;IACvK,KAAK,IAAIC,MAAM,GAAGZ,OAAO,CAACW,CAAC,CAAC,EAAEE,MAAM,GAAGX,OAAO,CAACS,CAAC,CAAC,EAAEG,CAAC,GAAGF,MAAM,CAACR,MAAM,EAAEW,KAAK,GAAGN,MAAM,CAACE,CAAC,CAAC,GAAG,IAAID,KAAK,CAACI,CAAC,CAAC,EAAEE,IAAI,EAAEC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAE,EAAEG,CAAC,EAAE;MAC/H,IAAID,IAAI,GAAGJ,MAAM,CAACK,CAAC,CAAC,IAAIJ,MAAM,CAACI,CAAC,CAAC,EAAE;QACjCF,KAAK,CAACE,CAAC,CAAC,GAAGD,IAAI;MACjB;IACF;EACF;EAEA,OAAOL,CAAC,GAAGR,EAAE,EAAE,EAAEQ,CAAC,EAAE;IAClBF,MAAM,CAACE,CAAC,CAAC,GAAGX,OAAO,CAACW,CAAC,CAAC;EACxB;EAEA,OAAO,IAAId,SAAS,CAACY,MAAM,EAAE,IAAI,CAACS,QAAQ,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}