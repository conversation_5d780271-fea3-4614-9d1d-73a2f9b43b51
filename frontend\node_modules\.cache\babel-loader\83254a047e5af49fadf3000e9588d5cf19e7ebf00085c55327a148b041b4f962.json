{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\cards\\\\TeamCard.tsx\",\n  _s = $RefreshSig$();\n/**\n * TeamCard Component for SkyGeni Dashboard\n * Specialized card for team data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useTeams } from '../../hooks/useData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamCard = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'bar'\n}) => {\n  _s();\n  const {\n    teams,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch\n  } = useTeams();\n  const data = overrideData || teams;\n  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading === 'pending';\n  const error = overrideError || (isError ? hookError : undefined);\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n    return data.map(team => ({\n      ...team,\n      label: team.name,\n      value: team.memberCount,\n      count: team.memberCount,\n      displayText: `${team.name} (${team.memberCount} members)`,\n      performanceText: `Performance: ${team.performance}%`,\n      departmentText: team.department\n    }));\n  }, [data]);\n  return /*#__PURE__*/_jsxDEV(DataCard, {\n    title: \"Teams\",\n    data: processedData,\n    chartType: chartType,\n    loading: loading,\n    error: error,\n    className: className,\n    elevation: elevation\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n};\n_s(TeamCard, \"5t8CLXme3cugR9tpMMEi8tnO1cg=\", false, function () {\n  return [useTeams];\n});\n_c = TeamCard;\nexport const TeamBarCard = props => /*#__PURE__*/_jsxDEV(TeamCard, {\n  ...props,\n  chartType: \"bar\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 68,\n  columnNumber: 3\n}, this);\n_c2 = TeamBarCard;\nexport const TeamDoughnutCard = props => /*#__PURE__*/_jsxDEV(TeamCard, {\n  ...props,\n  chartType: \"doughnut\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 72,\n  columnNumber: 3\n}, this);\n_c3 = TeamDoughnutCard;\nexport default TeamCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"TeamCard\");\n$RefreshReg$(_c2, \"TeamBarCard\");\n$RefreshReg$(_c3, \"TeamDoughnutCard\");", "map": {"version": 3, "names": ["React", "DataCard", "useTeams", "jsxDEV", "_jsxDEV", "TeamCard", "className", "elevation", "data", "overrideData", "loading", "overrideLoading", "error", "overrideError", "chartType", "_s", "teams", "hookLoading", "hookError", "isError", "refetch", "undefined", "processedData", "useMemo", "length", "map", "team", "label", "name", "value", "memberCount", "count", "displayText", "performanceText", "performance", "departmentText", "department", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "TeamBarCard", "props", "_c2", "TeamDoughnutCard", "_c3", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/cards/TeamCard.tsx"], "sourcesContent": ["/**\n * TeamCard Component for SkyGeni Dashboard\n * Specialized card for team data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useTeams } from '../../hooks/useData';\nimport { Team } from '../../types';\n\ninterface TeamCardProps {\n  className?: string;\n  elevation?: number;\n  data?: Team[];\n  loading?: boolean;\n  error?: string;\n  chartType?: 'bar' | 'doughnut';\n}\n\nconst TeamCard: React.FC<TeamCardProps> = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'bar',\n}) => {\n  const {\n    teams,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch,\n  } = useTeams();\n\n  const data = overrideData || teams;\n  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');\n  const error = overrideError || (isError ? hookError : undefined);\n\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n\n    return data.map(team => ({\n      ...team,\n      label: team.name,\n      value: team.memberCount,\n      count: team.memberCount,\n      displayText: `${team.name} (${team.memberCount} members)`,\n      performanceText: `Performance: ${team.performance}%`,\n      departmentText: team.department,\n    }));\n  }, [data]);\n\n  return (\n    <DataCard\n      title=\"Teams\"\n      data={processedData}\n      chartType={chartType}\n      loading={loading}\n      error={error}\n      className={className}\n      elevation={elevation}\n    />\n  );\n};\n\nexport const TeamBarCard: React.FC<Omit<TeamCardProps, 'chartType'>> = (props) => (\n  <TeamCard {...props} chartType=\"bar\" />\n);\n\nexport const TeamDoughnutCard: React.FC<Omit<TeamCardProps, 'chartType'>> = (props) => (\n  <TeamCard {...props} chartType=\"doughnut\" />\n);\n\nexport default TeamCard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,QAAQ,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY/C,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,SAAS;EACTC,SAAS,GAAG,CAAC;EACbC,IAAI,EAAEC,YAAY;EAClBC,OAAO,EAAEC,eAAe;EACxBC,KAAK,EAAEC,aAAa;EACpBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IACJC,KAAK;IACLN,OAAO,EAAEO,WAAW;IACpBL,KAAK,EAAEM,SAAS;IAChBC,OAAO;IACPC;EACF,CAAC,GAAGlB,QAAQ,CAAC,CAAC;EAEd,MAAMM,IAAI,GAAGC,YAAY,IAAIO,KAAK;EAClC,MAAMN,OAAO,GAAGC,eAAe,KAAKU,SAAS,GAAGV,eAAe,GAAIM,WAAW,KAAK,SAAU;EAC7F,MAAML,KAAK,GAAGC,aAAa,KAAKM,OAAO,GAAGD,SAAS,GAAGG,SAAS,CAAC;EAEhE,MAAMC,aAAa,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAM;IACxC,IAAI,CAACf,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOhB,IAAI,CAACiB,GAAG,CAACC,IAAI,KAAK;MACvB,GAAGA,IAAI;MACPC,KAAK,EAAED,IAAI,CAACE,IAAI;MAChBC,KAAK,EAAEH,IAAI,CAACI,WAAW;MACvBC,KAAK,EAAEL,IAAI,CAACI,WAAW;MACvBE,WAAW,EAAE,GAAGN,IAAI,CAACE,IAAI,KAAKF,IAAI,CAACI,WAAW,WAAW;MACzDG,eAAe,EAAE,gBAAgBP,IAAI,CAACQ,WAAW,GAAG;MACpDC,cAAc,EAAET,IAAI,CAACU;IACvB,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC5B,IAAI,CAAC,CAAC;EAEV,oBACEJ,OAAA,CAACH,QAAQ;IACPoC,KAAK,EAAC,OAAO;IACb7B,IAAI,EAAEc,aAAc;IACpBR,SAAS,EAAEA,SAAU;IACrBJ,OAAO,EAAEA,OAAQ;IACjBE,KAAK,EAAEA,KAAM;IACbN,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA;EAAU;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEN,CAAC;AAAC1B,EAAA,CA7CIV,QAAiC;EAAA,QAcjCH,QAAQ;AAAA;AAAAwC,EAAA,GAdRrC,QAAiC;AA+CvC,OAAO,MAAMsC,WAAuD,GAAIC,KAAK,iBAC3ExC,OAAA,CAACC,QAAQ;EAAA,GAAKuC,KAAK;EAAE9B,SAAS,EAAC;AAAK;EAAAwB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CACvC;AAACI,GAAA,GAFWF,WAAuD;AAIpE,OAAO,MAAMG,gBAA4D,GAAIF,KAAK,iBAChFxC,OAAA,CAACC,QAAQ;EAAA,GAAKuC,KAAK;EAAE9B,SAAS,EAAC;AAAU;EAAAwB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAC5C;AAACM,GAAA,GAFWD,gBAA4D;AAIzE,eAAezC,QAAQ;AAAC,IAAAqC,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}