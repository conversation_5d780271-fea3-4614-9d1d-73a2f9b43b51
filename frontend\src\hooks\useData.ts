/**
 * useData Hook for SkyGeni Dashboard
 * 
 * Custom hook that provides:
 * - Easy access to dashboard data
 * - Loading and error states
 * - Data fetching functions
 * - Automatic data refresh
 * - Cache management
 * - Type-safe data access
 */

import { useEffect, useCallback, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '../redux/store';
import {
  fetchDashboardData,
  fetchCustomerTypes,
  fetchAccountIndustries,
  fetchTeams,
  fetchACVRanges,
  refreshAllData,
  clearData,
  clearError,
  setSelectedDataType,
  setDataFilter,
  resetFilters,
} from '../redux/slices/dataSlice';
import {
  UseDataReturn,
  DataType,
  DataFilters,
  LoadingState,
  DashboardData,
  CustomerType,
  AccountIndustry,
  Team,
  ACVRange,
} from '../types';

// ============================================================================
// Hook Configuration
// ============================================================================

interface UseDataOptions {
  /**
   * Automatically fetch data on mount
   */
  autoFetch?: boolean;
  
  /**
   * Refresh interval in milliseconds (0 to disable)
   */
  refreshInterval?: number;
  
  /**
   * Enable cache (use cached data if available)
   */
  useCache?: boolean;
  
  /**
   * Specific data type to focus on
   */
  dataType?: DataType;
}

const DEFAULT_OPTIONS: UseDataOptions = {
  autoFetch: true,
  refreshInterval: 0, // Disabled by default
  useCache: true,
  dataType: undefined,
};

// ============================================================================
// Main Hook
// ============================================================================

/**
 * Custom hook for managing dashboard data
 */
export const useData = (options: UseDataOptions = {}): UseDataReturn => {
  const opts = { ...DEFAULT_OPTIONS, ...options };
  const dispatch = useAppDispatch();
  
  // ========================================================================
  // Selectors
  // ========================================================================
  
  const dashboardData = useAppSelector((state) => state.data.dashboardData);
  const customerTypes = useAppSelector((state) => state.data.customerTypes);
  const accountIndustries = useAppSelector((state) => state.data.accountIndustries);
  const teams = useAppSelector((state) => state.data.teams);
  const acvRanges = useAppSelector((state) => state.data.acvRanges);
  const loading = useAppSelector((state) => state.data.loading);
  const error = useAppSelector((state) => state.data.error);
  const lastFetched = useAppSelector((state) => state.data.lastFetched);
  const cacheExpiry = useAppSelector((state) => state.data.cacheExpiry);
  const filters = useAppSelector((state) => state.data.filters);
  const selectedDataType = useAppSelector((state) => state.data.selectedDataType);

  // ========================================================================
  // Computed Values
  // ========================================================================
  
  const isLoading = useMemo(() => loading === 'pending', [loading]);
  const isError = useMemo(() => loading === 'failed' || !!error, [loading, error]);
  const isSuccess = useMemo(() => loading === 'succeeded' && !!dashboardData, [loading, dashboardData]);
  const isEmpty = useMemo(() => !dashboardData && !isLoading, [dashboardData, isLoading]);
  
  /**
   * Check if cache is still valid
   */
  const isCacheValid = useMemo(() => {
    if (!opts.useCache || !lastFetched || !cacheExpiry) return false;
    return new Date() < new Date(cacheExpiry);
  }, [opts.useCache, lastFetched, cacheExpiry]);

  /**
   * Check if data needs refresh
   */
  const needsRefresh = useMemo(() => {
    if (!opts.useCache) return true;
    if (!dashboardData) return true;
    if (!isCacheValid) return true;
    return false;
  }, [opts.useCache, dashboardData, isCacheValid]);

  // ========================================================================
  // Data Fetching Functions
  // ========================================================================
  
  /**
   * Fetch all dashboard data
   */
  const fetchData = useCallback(async (forceRefresh = false) => {
    if (!forceRefresh && !needsRefresh && opts.useCache) {
      console.log('📦 Using cached data');
      return;
    }

    try {
      await dispatch(fetchDashboardData({ forceRefresh })).unwrap();
    } catch (error) {
      console.error('❌ Failed to fetch dashboard data:', error);
      throw error;
    }
  }, [dispatch, opts.useCache]); // Remove needsRefresh from dependencies to prevent infinite loop

  /**
   * Fetch specific data type
   */
  const fetchSpecificData = useCallback(async (dataType: DataType) => {
    try {
      switch (dataType) {
        case 'customerTypes':
          await dispatch(fetchCustomerTypes()).unwrap();
          break;
        case 'accountIndustries':
          await dispatch(fetchAccountIndustries()).unwrap();
          break;
        case 'teams':
          await dispatch(fetchTeams()).unwrap();
          break;
        case 'acvRanges':
          await dispatch(fetchACVRanges()).unwrap();
          break;
        default:
          throw new Error(`Unknown data type: ${dataType}`);
      }
    } catch (error) {
      console.error(`❌ Failed to fetch ${dataType}:`, error);
      throw error;
    }
  }, [dispatch]);

  /**
   * Refresh all data
   */
  const refetch = useCallback(async () => {
    try {
      await dispatch(refreshAllData()).unwrap();
    } catch (error) {
      console.error('❌ Failed to refresh data:', error);
      throw error;
    }
  }, [dispatch]);

  /**
   * Clear all data
   */
  const clearAllData = useCallback(() => {
    dispatch(clearData());
  }, [dispatch]);

  /**
   * Clear error state
   */
  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  // ========================================================================
  // Filter and Selection Functions
  // ========================================================================
  
  /**
   * Update data filters
   */
  const updateFilter = useCallback((filterType: keyof DataFilters, value: any) => {
    dispatch(setDataFilter({ filterType, value }));
  }, [dispatch]);

  /**
   * Reset all filters
   */
  const resetAllFilters = useCallback(() => {
    dispatch(resetFilters());
  }, [dispatch]);

  /**
   * Set selected data type
   */
  const selectDataType = useCallback((dataType: DataType | null) => {
    dispatch(setSelectedDataType(dataType));
  }, [dispatch]);

  // ========================================================================
  // Filtered Data
  // ========================================================================
  
  /**
   * Get filtered data based on current filters
   */
  const getFilteredData = useCallback((data: any[], searchFields: string[] = ['name', 'type', 'industry']) => {
    let filtered = [...data];
    
    // Apply search filter
    if (filters.searchTerm) {
      const searchTerm = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        searchFields.some(field =>
          item[field]?.toString().toLowerCase().includes(searchTerm)
        )
      );
    }
    
    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        const aVal = a[filters.sortBy];
        const bVal = b[filters.sortBy];
        
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          return filters.sortOrder === 'asc' 
            ? aVal.localeCompare(bVal)
            : bVal.localeCompare(aVal);
        }
        
        if (typeof aVal === 'number' && typeof bVal === 'number') {
          return filters.sortOrder === 'asc' ? aVal - bVal : bVal - aVal;
        }
        
        return 0;
      });
    }
    
    // Filter empty values if needed
    if (!filters.showEmpty) {
      filtered = filtered.filter(item => 
        Object.values(item).some(value => 
          value !== null && value !== undefined && value !== ''
        )
      );
    }
    
    return filtered;
  }, [filters]);

  // ========================================================================
  // Effects
  // ========================================================================
  
  /**
   * Auto-fetch data on mount
   */
  useEffect(() => {
    if (opts.autoFetch && needsRefresh) {
      fetchData();
    }
  }, [opts.autoFetch]); // Remove fetchData and needsRefresh to prevent infinite loop

  /**
   * Set up refresh interval
   */
  useEffect(() => {
    if (!opts.refreshInterval || opts.refreshInterval <= 0) return;
    
    const interval = setInterval(() => {
      if (!isLoading) {
        console.log('🔄 Auto-refreshing data...');
        fetchData(true);
      }
    }, opts.refreshInterval);
    
    return () => clearInterval(interval);
  }, [opts.refreshInterval, isLoading, fetchData]);

  /**
   * Focus on specific data type if provided
   */
  useEffect(() => {
    if (opts.dataType && opts.dataType !== selectedDataType) {
      selectDataType(opts.dataType);
    }
  }, [opts.dataType, selectedDataType, selectDataType]);

  // ========================================================================
  // Return Hook Interface
  // ========================================================================
  
  return {
    // Data
    dashboardData,
    customerTypes: getFilteredData(customerTypes, ['type']),
    accountIndustries: getFilteredData(accountIndustries, ['industry']),
    teams: getFilteredData(teams, ['name', 'department']),
    acvRanges: getFilteredData(acvRanges, ['range']),
    
    // State
    loading,
    error,
    isLoading,
    isError,
    isSuccess,
    isEmpty,
    lastFetched,
    isCacheValid,
    needsRefresh,
    
    // Actions
    refetch,
    fetchData,
    fetchSpecificData,
    clearAllData,
    clearErrorState,
    
    // Filters and Selection
    filters,
    selectedDataType,
    updateFilter,
    resetAllFilters,
    selectDataType,
    getFilteredData,
  };
};

// ============================================================================
// Specialized Hooks
// ============================================================================

/**
 * Hook specifically for customer types data
 */
export const useCustomerTypes = () => {
  return useData({ dataType: 'customerTypes' });
};

/**
 * Hook specifically for account industries data
 */
export const useAccountIndustries = () => {
  return useData({ dataType: 'accountIndustries' });
};

/**
 * Hook specifically for teams data
 */
export const useTeams = () => {
  return useData({ dataType: 'teams' });
};

/**
 * Hook specifically for ACV ranges data
 */
export const useACVRanges = () => {
  return useData({ dataType: 'acvRanges' });
};

/**
 * Hook with auto-refresh enabled
 */
export const useDataWithRefresh = (intervalMs: number = 30000) => {
  return useData({ refreshInterval: intervalMs });
};

export default useData;
