{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\cards\\\\CustomerTypeCard.tsx\",\n  _s = $RefreshSig$();\n/**\n * CustomerTypeCard Component for SkyGeni Dashboard\n * \n * Specialized card component for displaying customer type data:\n * - Uses DataCard as base component\n * - Configured specifically for customer type visualization\n * - Doughnut chart for percentage distribution\n * - Customer-specific styling and interactions\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useCustomerTypes } from '../../hooks/useData';\n\n// ============================================================================\n// CustomerTypeCard Props\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// CustomerTypeCard Component\n// ============================================================================\n\nconst CustomerTypeCard = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'doughnut'\n}) => {\n  _s();\n  // Use the custom hook to get customer types data\n  const {\n    customerTypes,\n    loading: hookLoading,\n    error: hookError,\n    isError\n    // refetch,\n  } = useCustomerTypes();\n\n  // Use override data if provided, otherwise use hook data\n  const data = overrideData || customerTypes;\n  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading === 'pending';\n  const error = overrideError || (isError ? hookError : undefined);\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  /**\n   * Transform customer type data for better visualization\n   */\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n    return data.map(customerType => ({\n      ...customerType,\n      // Ensure we have the right field names for the chart\n      name: customerType.type,\n      label: customerType.type,\n      value: customerType.count,\n      // Add additional display information\n      displayText: `${customerType.type} (${customerType.count.toLocaleString()})`,\n      percentageText: customerType.percentage ? `${customerType.percentage.toFixed(1)}%` : ''\n    }));\n  }, [data]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  // const handleRefresh = React.useCallback(() => {\n  //   refetch();\n  // }, [refetch]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(DataCard, {\n    title: \"Customer Types\",\n    data: processedData,\n    chartType: chartType,\n    loading: loading,\n    error: error,\n    className: className,\n    elevation: elevation\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n\n// ============================================================================\n// Export Component and Variants\n// ============================================================================\n\n/**\n * Customer Type Card with Bar Chart\n */\n_s(CustomerTypeCard, \"E9F31+9AYqjV0SVtMtZ1oO+hGPU=\", false, function () {\n  return [useCustomerTypes];\n});\n_c = CustomerTypeCard;\nexport const CustomerTypeBarCard = props => /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n  ...props,\n  chartType: \"bar\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 135,\n  columnNumber: 3\n}, this);\n\n/**\n * Customer Type Card with Doughnut Chart (default)\n */\n_c2 = CustomerTypeBarCard;\nexport const CustomerTypeDoughnutCard = props => /*#__PURE__*/_jsxDEV(CustomerTypeCard, {\n  ...props,\n  chartType: \"doughnut\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 142,\n  columnNumber: 3\n}, this);\n_c3 = CustomerTypeDoughnutCard;\nexport default CustomerTypeCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CustomerTypeCard\");\n$RefreshReg$(_c2, \"CustomerTypeBarCard\");\n$RefreshReg$(_c3, \"CustomerTypeDoughnutCard\");", "map": {"version": 3, "names": ["React", "DataCard", "useCustomerTypes", "jsxDEV", "_jsxDEV", "CustomerTypeCard", "className", "elevation", "data", "overrideData", "loading", "overrideLoading", "error", "overrideError", "chartType", "_s", "customerTypes", "hookLoading", "hookError", "isError", "undefined", "processedData", "useMemo", "length", "map", "customerType", "name", "type", "label", "value", "count", "displayText", "toLocaleString", "percentageText", "percentage", "toFixed", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "CustomerTypeBarCard", "props", "_c2", "CustomerTypeDoughnutCard", "_c3", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/cards/CustomerTypeCard.tsx"], "sourcesContent": ["/**\n * CustomerTypeCard Component for SkyGeni Dashboard\n * \n * Specialized card component for displaying customer type data:\n * - Uses DataCard as base component\n * - Configured specifically for customer type visualization\n * - Doughnut chart for percentage distribution\n * - Customer-specific styling and interactions\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useCustomerTypes } from '../../hooks/useData';\nimport { CustomerType } from '../../types';\n\n// ============================================================================\n// CustomerTypeCard Props\n// ============================================================================\n\ninterface CustomerTypeCardProps {\n  /**\n   * Custom className for styling\n   */\n  className?: string;\n  \n  /**\n   * Card elevation\n   */\n  elevation?: number;\n  \n  /**\n   * Override data (if not using hook)\n   */\n  data?: CustomerType[];\n  \n  /**\n   * Override loading state\n   */\n  loading?: boolean;\n  \n  /**\n   * Override error state\n   */\n  error?: string;\n  \n  /**\n   * Chart type override\n   */\n  chartType?: 'bar' | 'doughnut';\n}\n\n// ============================================================================\n// CustomerTypeCard Component\n// ============================================================================\n\nconst CustomerTypeCard: React.FC<CustomerTypeCardProps> = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'doughnut',\n}) => {\n  // Use the custom hook to get customer types data\n  const {\n    customerTypes,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    // refetch,\n  } = useCustomerTypes();\n\n  // Use override data if provided, otherwise use hook data\n  const data = overrideData || customerTypes;\n  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');\n  const error = overrideError || (isError ? hookError : undefined);\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  /**\n   * Transform customer type data for better visualization\n   */\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n\n    return data.map(customerType => ({\n      ...customerType,\n      // Ensure we have the right field names for the chart\n      name: customerType.type,\n      label: customerType.type,\n      value: customerType.count,\n      // Add additional display information\n      displayText: `${customerType.type} (${customerType.count.toLocaleString()})`,\n      percentageText: customerType.percentage \n        ? `${customerType.percentage.toFixed(1)}%` \n        : '',\n    }));\n  }, [data]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  // const handleRefresh = React.useCallback(() => {\n  //   refetch();\n  // }, [refetch]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return (\n    <DataCard\n      title=\"Customer Types\"\n      data={processedData}\n      chartType={chartType}\n      loading={loading}\n      error={error}\n      className={className}\n      elevation={elevation}\n    />\n  );\n};\n\n// ============================================================================\n// Export Component and Variants\n// ============================================================================\n\n/**\n * Customer Type Card with Bar Chart\n */\nexport const CustomerTypeBarCard: React.FC<Omit<CustomerTypeCardProps, 'chartType'>> = (props) => (\n  <CustomerTypeCard {...props} chartType=\"bar\" />\n);\n\n/**\n * Customer Type Card with Doughnut Chart (default)\n */\nexport const CustomerTypeDoughnutCard: React.FC<Omit<CustomerTypeCardProps, 'chartType'>> = (props) => (\n  <CustomerTypeCard {...props} chartType=\"doughnut\" />\n);\n\nexport default CustomerTypeCard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,gBAAgB,QAAQ,qBAAqB;;AAGtD;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAkCA;AACA;AACA;;AAEA,MAAMC,gBAAiD,GAAGA,CAAC;EACzDC,SAAS;EACTC,SAAS,GAAG,CAAC;EACbC,IAAI,EAAEC,YAAY;EAClBC,OAAO,EAAEC,eAAe;EACxBC,KAAK,EAAEC,aAAa;EACpBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ;EACA,MAAM;IACJC,aAAa;IACbN,OAAO,EAAEO,WAAW;IACpBL,KAAK,EAAEM,SAAS;IAChBC;IACA;EACF,CAAC,GAAGjB,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAMM,IAAI,GAAGC,YAAY,IAAIO,aAAa;EAC1C,MAAMN,OAAO,GAAGC,eAAe,KAAKS,SAAS,GAAGT,eAAe,GAAIM,WAAW,KAAK,SAAU;EAC7F,MAAML,KAAK,GAAGC,aAAa,KAAKM,OAAO,GAAGD,SAAS,GAAGE,SAAS,CAAC;;EAEhE;EACA;EACA;;EAEA;AACF;AACA;EACE,MAAMC,aAAa,GAAGrB,KAAK,CAACsB,OAAO,CAAC,MAAM;IACxC,IAAI,CAACd,IAAI,IAAIA,IAAI,CAACe,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOf,IAAI,CAACgB,GAAG,CAACC,YAAY,KAAK;MAC/B,GAAGA,YAAY;MACf;MACAC,IAAI,EAAED,YAAY,CAACE,IAAI;MACvBC,KAAK,EAAEH,YAAY,CAACE,IAAI;MACxBE,KAAK,EAAEJ,YAAY,CAACK,KAAK;MACzB;MACAC,WAAW,EAAE,GAAGN,YAAY,CAACE,IAAI,KAAKF,YAAY,CAACK,KAAK,CAACE,cAAc,CAAC,CAAC,GAAG;MAC5EC,cAAc,EAAER,YAAY,CAACS,UAAU,GACnC,GAAGT,YAAY,CAACS,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GACxC;IACN,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC3B,IAAI,CAAC,CAAC;;EAEV;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;;EAEA,oBACEJ,OAAA,CAACH,QAAQ;IACPmC,KAAK,EAAC,gBAAgB;IACtB5B,IAAI,EAAEa,aAAc;IACpBP,SAAS,EAAEA,SAAU;IACrBJ,OAAO,EAAEA,OAAQ;IACjBE,KAAK,EAAEA,KAAM;IACbN,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA;EAAU;IAAA8B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEN,CAAC;;AAED;AACA;AACA;;AAEA;AACA;AACA;AAFAzB,EAAA,CA3EMV,gBAAiD;EAAA,QAejDH,gBAAgB;AAAA;AAAAuC,EAAA,GAfhBpC,gBAAiD;AA8EvD,OAAO,MAAMqC,mBAAuE,GAAIC,KAAK,iBAC3FvC,OAAA,CAACC,gBAAgB;EAAA,GAAKsC,KAAK;EAAE7B,SAAS,EAAC;AAAK;EAAAuB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAC/C;;AAED;AACA;AACA;AAFAI,GAAA,GAJaF,mBAAuE;AAOpF,OAAO,MAAMG,wBAA4E,GAAIF,KAAK,iBAChGvC,OAAA,CAACC,gBAAgB;EAAA,GAAKsC,KAAK;EAAE7B,SAAS,EAAC;AAAU;EAAAuB,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CACpD;AAACM,GAAA,GAFWD,wBAA4E;AAIzF,eAAexC,gBAAgB;AAAC,IAAAoC,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}