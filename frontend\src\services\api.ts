/**
 * Clean API Service for SkyGeni Dashboard
 * Simple fetch-based implementation for dashboard data
 */

// ============================================================================
// Types
// ============================================================================

export interface CustomerType {
  type: string;
  count: number;
  percentage: number;
}

export interface AccountIndustry {
  industry: string;
  count: number;
  revenue: number;
}

export interface Team {
  name: string;
  members: number;
  performance: number;
}

export interface ACVRange {
  range: string;
  count: number;
  value: number;
}

export interface DashboardData {
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  summary: {
    totalCustomers: number;
    totalRevenue: number;
    totalTeams: number;
    averageACV: number;
  };
  lastUpdated: string;
}

// ============================================================================
// API Functions
// ============================================================================

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

/**
 * Fetch all dashboard data from /api/data/dashboard endpoint
 */
export async function fetchDashboardData(): Promise<DashboardData> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/data/dashboard`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    // The backend returns data in a wrapper with success, data, message, timestamp
    if (!result.success) {
      throw new Error(result.message || 'API request failed');
    }

    const data = result.data;

    // Calculate summary if not provided
    const summary = data.summary || {
      totalCustomers: data.customerTypes?.reduce((sum: number, item: CustomerType) => sum + item.count, 0) || 0,
      totalRevenue: data.accountIndustries?.reduce((sum: number, item: AccountIndustry) => sum + item.revenue, 0) || 0,
      totalTeams: data.teams?.length || 0,
      averageACV: data.acvRanges?.reduce((sum: number, item: ACVRange) => sum + item.value, 0) / (data.acvRanges?.length || 1) || 0,
    };

    return {
      customerTypes: data.customerTypes || [],
      accountIndustries: data.accountIndustries || [],
      teams: data.teams || [],
      acvRanges: data.acvRanges || [],
      summary,
      lastUpdated: result.timestamp || new Date().toISOString(),
    };
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    throw error;
  }
}
