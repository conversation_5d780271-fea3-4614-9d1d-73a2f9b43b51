{"ast": null, "code": "export default function () {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}", "map": {"version": 3, "names": ["size", "node"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/size.js"], "sourcesContent": ["export default function() {\n  let size = 0;\n  for (const node of this) ++size; // eslint-disable-line no-unused-vars\n  return size;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EACxB,IAAIA,IAAI,GAAG,CAAC;EACZ,KAAK,MAAMC,IAAI,IAAI,IAAI,EAAE,EAAED,IAAI,CAAC,CAAC;EACjC,OAAOA,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}