/**
 * Loader Component for SkyGeni Dashboard
 * 
 * A flexible loading component with multiple variants:
 * - Circular progress indicator
 * - Linear progress bar
 * - Animated dots
 * - Skeleton placeholder
 * 
 * Features:
 * - Multiple size options
 * - Customizable colors
 * - Optional loading messages
 * - Backdrop support
 * - Centered layout options
 */

import React from 'react';
import {
  Box,
  CircularProgress,
  LinearProgress,
  Typography,
  Backdrop,
  Skeleton,
  useTheme,
} from '@mui/material';
import { LoaderProps } from './types';

// ============================================================================
// Size Mappings
// ============================================================================

const SIZE_MAPPINGS = {
  small: 24,
  medium: 40,
  large: 56,
};

// ============================================================================
// Animated Dots Component
// ============================================================================

const AnimatedDots: React.FC<{ color: string; size: number }> = ({ color, size }) => {
  const dotSize = size / 8;
  const spacing = size / 6;

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: `${spacing}px`,
      }}
    >
      {[0, 1, 2].map((index) => (
        <Box
          key={index}
          sx={{
            width: dotSize,
            height: dotSize,
            backgroundColor: color,
            borderRadius: '50%',
            animation: 'dotPulse 1.4s ease-in-out infinite both',
            animationDelay: `${index * 0.16}s`,
            '@keyframes dotPulse': {
              '0%, 80%, 100%': {
                transform: 'scale(0)',
                opacity: 0.5,
              },
              '40%': {
                transform: 'scale(1)',
                opacity: 1,
              },
            },
          }}
        />
      ))}
    </Box>
  );
};

// ============================================================================
// Skeleton Loader Component
// ============================================================================

const SkeletonLoader: React.FC<{ size: number }> = ({ size }) => {
  return (
    <Box sx={{ width: '100%', maxWidth: size * 4 }}>
      <Skeleton variant="text" width="60%" height={size / 2} />
      <Skeleton variant="text" width="80%" height={size / 2} />
      <Skeleton variant="text" width="40%" height={size / 2} />
      <Skeleton variant="rectangular" width="100%" height={size} sx={{ mt: 1 }} />
    </Box>
  );
};

// ============================================================================
// Main Loader Component
// ============================================================================

const Loader: React.FC<LoaderProps> = ({
  size = 'medium',
  color = 'primary',
  message,
  showMessage = true,
  className,
  sx,
  centered = true,
  minHeight = 200,
  backdrop = false,
  variant = 'circular',
}) => {
  const theme = useTheme();

  // Calculate actual size
  const actualSize = typeof size === 'number' ? size : SIZE_MAPPINGS[size];

  // Determine color value
  const colorValue = React.useMemo(() => {
    switch (color) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'inherit':
        return theme.palette.text.primary;
      default:
        return color;
    }
  }, [color, theme]);

  // ========================================================================
  // Render Different Variants
  // ========================================================================

  const renderLoader = () => {
    switch (variant) {
      case 'linear':
        return (
          <Box sx={{ width: '100%', maxWidth: actualSize * 4 }}>
            <LinearProgress
              color={color === 'primary' || color === 'secondary' ? color : undefined}
              sx={color !== 'primary' && color !== 'secondary' ? { color: colorValue } : undefined}
            />
          </Box>
        );

      case 'dots':
        return <AnimatedDots color={colorValue} size={actualSize} />;

      case 'skeleton':
        return <SkeletonLoader size={actualSize} />;

      case 'circular':
      default:
        return (
          <CircularProgress
            size={actualSize}
            color={color === 'primary' || color === 'secondary' ? color : undefined}
            sx={color !== 'primary' && color !== 'secondary' ? { color: colorValue } : undefined}
          />
        );
    }
  };

  // ========================================================================
  // Main Content
  // ========================================================================

  const content = (
    <Box
      className={className}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: centered ? 'center' : 'flex-start',
        gap: 2,
        minHeight: centered ? minHeight : 'auto',
        width: '100%',
        ...sx,
      }}
    >
      {renderLoader()}
      
      {showMessage && message && (
        <Typography
          variant="body2"
          color="text.secondary"
          align="center"
          sx={{
            mt: variant === 'skeleton' ? 0 : 1,
            fontSize: actualSize < 30 ? '0.75rem' : '0.875rem',
          }}
        >
          {message}
        </Typography>
      )}
    </Box>
  );

  // ========================================================================
  // Render with or without backdrop
  // ========================================================================

  if (backdrop) {
    return (
      <Backdrop
        open={true}
        sx={{
          color: '#fff',
          zIndex: theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
        }}
      >
        {content}
      </Backdrop>
    );
  }

  return content;
};

// ============================================================================
// Preset Loader Components
// ============================================================================

/**
 * Small loader for inline use
 */
export const SmallLoader: React.FC<Omit<LoaderProps, 'size'>> = (props) => (
  <Loader {...props} size="small" centered={false} minHeight="auto" />
);

/**
 * Page loader with backdrop
 */
export const PageLoader: React.FC<Omit<LoaderProps, 'backdrop' | 'centered'>> = (props) => (
  <Loader {...props} backdrop={true} centered={true} />
);

/**
 * Card loader for loading states in cards
 */
export const CardLoader: React.FC<Omit<LoaderProps, 'variant'>> = (props) => (
  <Loader {...props} variant="skeleton" />
);

/**
 * Button loader for loading states in buttons
 */
export const ButtonLoader: React.FC<Omit<LoaderProps, 'size' | 'centered'>> = (props) => (
  <Loader {...props} size="small" centered={false} minHeight="auto" showMessage={false} />
);

export default Loader;
