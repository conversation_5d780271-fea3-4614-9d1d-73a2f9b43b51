{"ast": null, "code": "/**\n * Clean API Service for SkyGeni Dashboard\n * Simple fetch-based implementation for dashboard data\n */\n\n// ============================================================================\n// Types\n// ============================================================================\n\n// ============================================================================\n// API Functions\n// ============================================================================\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\n * Fetch all dashboard data from /api/data endpoint\n */\nexport async function fetchDashboardData() {\n  try {\n    var _data$customerTypes, _data$accountIndustri, _data$teams, _data$acvRanges, _data$acvRanges2;\n    const response = await fetch(`${API_BASE_URL}/api/data`);\n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n    const data = await response.json();\n\n    // Calculate summary if not provided\n    const summary = data.summary || {\n      totalCustomers: ((_data$customerTypes = data.customerTypes) === null || _data$customerTypes === void 0 ? void 0 : _data$customerTypes.reduce((sum, item) => sum + item.count, 0)) || 0,\n      totalRevenue: ((_data$accountIndustri = data.accountIndustries) === null || _data$accountIndustri === void 0 ? void 0 : _data$accountIndustri.reduce((sum, item) => sum + item.revenue, 0)) || 0,\n      totalTeams: ((_data$teams = data.teams) === null || _data$teams === void 0 ? void 0 : _data$teams.length) || 0,\n      averageACV: ((_data$acvRanges = data.acvRanges) === null || _data$acvRanges === void 0 ? void 0 : _data$acvRanges.reduce((sum, item) => sum + item.value, 0)) / (((_data$acvRanges2 = data.acvRanges) === null || _data$acvRanges2 === void 0 ? void 0 : _data$acvRanges2.length) || 1) || 0\n    };\n    return {\n      customerTypes: data.customerTypes || [],\n      accountIndustries: data.accountIndustries || [],\n      teams: data.teams || [],\n      acvRanges: data.acvRanges || [],\n      summary,\n      lastUpdated: data.lastUpdated || new Date().toISOString()\n    };\n  } catch (error) {\n    console.error('Failed to fetch dashboard data:', error);\n    throw error;\n  }\n}", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "fetchDashboardData", "_data$customerTypes", "_data$accountIndustri", "_data$teams", "_data$acvRanges", "_data$acvRanges2", "response", "fetch", "ok", "Error", "status", "data", "json", "summary", "totalCustomers", "customerTypes", "reduce", "sum", "item", "count", "totalRevenue", "accountIndustries", "revenue", "totalTeams", "teams", "length", "averageACV", "acvRanges", "value", "lastUpdated", "Date", "toISOString", "error", "console"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/services/api.ts"], "sourcesContent": ["/**\n * Clean API Service for SkyGeni Dashboard\n * Simple fetch-based implementation for dashboard data\n */\n\n// ============================================================================\n// Types\n// ============================================================================\n\nexport interface CustomerType {\n  type: string;\n  count: number;\n  percentage: number;\n}\n\nexport interface AccountIndustry {\n  industry: string;\n  count: number;\n  revenue: number;\n}\n\nexport interface Team {\n  name: string;\n  members: number;\n  performance: number;\n}\n\nexport interface ACVRange {\n  range: string;\n  count: number;\n  value: number;\n}\n\nexport interface DashboardData {\n  customerTypes: CustomerType[];\n  accountIndustries: AccountIndustry[];\n  teams: Team[];\n  acvRanges: ACVRange[];\n  summary: {\n    totalCustomers: number;\n    totalRevenue: number;\n    totalTeams: number;\n    averageACV: number;\n  };\n  lastUpdated: string;\n}\n\n// ============================================================================\n// API Functions\n// ============================================================================\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n\n/**\n * Fetch all dashboard data from /api/data endpoint\n */\nexport async function fetchDashboardData(): Promise<DashboardData> {\n  try {\n    const response = await fetch(`${API_BASE_URL}/api/data`);\n    \n    if (!response.ok) {\n      throw new Error(`HTTP error! status: ${response.status}`);\n    }\n\n    const data = await response.json();\n    \n    // Calculate summary if not provided\n    const summary = data.summary || {\n      totalCustomers: data.customerTypes?.reduce((sum: number, item: CustomerType) => sum + item.count, 0) || 0,\n      totalRevenue: data.accountIndustries?.reduce((sum: number, item: AccountIndustry) => sum + item.revenue, 0) || 0,\n      totalTeams: data.teams?.length || 0,\n      averageACV: data.acvRanges?.reduce((sum: number, item: ACVRange) => sum + item.value, 0) / (data.acvRanges?.length || 1) || 0,\n    };\n\n    return {\n      customerTypes: data.customerTypes || [],\n      accountIndustries: data.accountIndustries || [],\n      teams: data.teams || [],\n      acvRanges: data.acvRanges || [],\n      summary,\n      lastUpdated: data.lastUpdated || new Date().toISOString(),\n    };\n  } catch (error) {\n    console.error('Failed to fetch dashboard data:', error);\n    throw error;\n  }\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAwCA;AACA;AACA;;AAEA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;AAE7E;AACA;AACA;AACA,OAAO,eAAeC,kBAAkBA,CAAA,EAA2B;EACjE,IAAI;IAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,eAAA,EAAAC,gBAAA;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGX,YAAY,WAAW,CAAC;IAExD,IAAI,CAACU,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;IAC3D;IAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;;IAElC;IACA,MAAMC,OAAO,GAAGF,IAAI,CAACE,OAAO,IAAI;MAC9BC,cAAc,EAAE,EAAAb,mBAAA,GAAAU,IAAI,CAACI,aAAa,cAAAd,mBAAA,uBAAlBA,mBAAA,CAAoBe,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAkB,KAAKD,GAAG,GAAGC,IAAI,CAACC,KAAK,EAAE,CAAC,CAAC,KAAI,CAAC;MACzGC,YAAY,EAAE,EAAAlB,qBAAA,GAAAS,IAAI,CAACU,iBAAiB,cAAAnB,qBAAA,uBAAtBA,qBAAA,CAAwBc,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAqB,KAAKD,GAAG,GAAGC,IAAI,CAACI,OAAO,EAAE,CAAC,CAAC,KAAI,CAAC;MAChHC,UAAU,EAAE,EAAApB,WAAA,GAAAQ,IAAI,CAACa,KAAK,cAAArB,WAAA,uBAAVA,WAAA,CAAYsB,MAAM,KAAI,CAAC;MACnCC,UAAU,EAAE,EAAAtB,eAAA,GAAAO,IAAI,CAACgB,SAAS,cAAAvB,eAAA,uBAAdA,eAAA,CAAgBY,MAAM,CAAC,CAACC,GAAW,EAAEC,IAAc,KAAKD,GAAG,GAAGC,IAAI,CAACU,KAAK,EAAE,CAAC,CAAC,KAAI,EAAAvB,gBAAA,GAAAM,IAAI,CAACgB,SAAS,cAAAtB,gBAAA,uBAAdA,gBAAA,CAAgBoB,MAAM,KAAI,CAAC,CAAC,IAAI;IAC9H,CAAC;IAED,OAAO;MACLV,aAAa,EAAEJ,IAAI,CAACI,aAAa,IAAI,EAAE;MACvCM,iBAAiB,EAAEV,IAAI,CAACU,iBAAiB,IAAI,EAAE;MAC/CG,KAAK,EAAEb,IAAI,CAACa,KAAK,IAAI,EAAE;MACvBG,SAAS,EAAEhB,IAAI,CAACgB,SAAS,IAAI,EAAE;MAC/Bd,OAAO;MACPgB,WAAW,EAAElB,IAAI,CAACkB,WAAW,IAAI,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAC1D,CAAC;EACH,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACvD,MAAMA,KAAK;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}