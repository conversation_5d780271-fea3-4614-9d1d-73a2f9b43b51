{"ast": null, "code": "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function () {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}", "map": {"version": 3, "names": ["a", "c", "m", "s"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-hierarchy/src/lcg.js"], "sourcesContent": ["// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n"], "mappings": "AAAA;AACA,MAAMA,CAAC,GAAG,OAAO;AACjB,MAAMC,CAAC,GAAG,UAAU;AACpB,MAAMC,CAAC,GAAG,UAAU,CAAC,CAAC;;AAEtB,eAAe,YAAW;EACxB,IAAIC,CAAC,GAAG,CAAC;EACT,OAAO,MAAM,CAACA,CAAC,GAAG,CAACH,CAAC,GAAGG,CAAC,GAAGF,CAAC,IAAIC,CAAC,IAAIA,CAAC;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}