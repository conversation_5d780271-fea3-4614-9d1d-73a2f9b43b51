/**
 * Data Management Module for SkyGeni Dashboard
 * Handles loading, processing, and serving JSON data files
 * 
 * This module provides a centralized way to:
 * - Load JSON data files from the file system
 * - Validate and transform data
 * - Cache data for performance
 * - Provide type-safe data access
 */

import fs from 'fs';
import path from 'path';
import { 
  CustomerType, 
  AccountIndustry, 
  Team, 
  ACVRange, 
  DashboardData,
  DashboardSummary,
  ChartDataPoint,
  BarChartData,
  DoughnutChartData
} from '../types';
import { 
  DataService, 
  DataValidationResult, 
  DataTransformOptions,
  CacheEntry,
  CacheManager
} from './types';

// ============================================================================
// Constants and Configuration
// ============================================================================

const DATA_DIRECTORY = path.join(__dirname);
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// JSON file names
const DATA_FILES = {
  CUSTOMER_TYPE: 'CustomerType.json',
  ACCOUNT_INDUSTRY: 'AccountIndustry.json',
  TEAM: 'Team.json',
  ACV_RANGE: 'ACVRange.json'
} as const;

// ============================================================================
// Simple In-Memory Cache Implementation
// ============================================================================

class SimpleCache implements CacheManager {
  private cache = new Map<string, CacheEntry>();

  get<T>(key: string): CacheEntry<T> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (this.isExpired(entry)) {
      this.delete(key);
      return null;
    }
    
    return entry as CacheEntry<T>;
  }

  set<T>(key: string, data: T, ttl: number = CACHE_TTL): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      expiresAt: Date.now() + ttl,
      key
    };
    this.cache.set(key, entry);
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiresAt;
  }
}

// ============================================================================
// Data Service Implementation
// ============================================================================

class DataServiceImpl implements DataService {
  private cache: CacheManager;

  constructor() {
    this.cache = new SimpleCache();
  }

  /**
   * Load and parse a JSON file with error handling and caching
   */
  private async loadJsonFile<T>(filename: string): Promise<T[]> {
    const cacheKey = `json_${filename}`;
    
    // Check cache first
    const cached = this.cache.get<T[]>(cacheKey);
    if (cached) {
      console.log(`📦 Cache hit for ${filename}`);
      return cached.data;
    }

    try {
      const filePath = path.join(DATA_DIRECTORY, filename);
      
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️  File not found: ${filename}. Returning empty array.`);
        return [];
      }

      // Read and parse JSON file
      const fileContent = fs.readFileSync(filePath, 'utf-8');
      const data = JSON.parse(fileContent);
      
      // Ensure data is an array
      const arrayData = Array.isArray(data) ? data : [data];
      
      // Cache the result
      this.cache.set(cacheKey, arrayData);
      
      console.log(`✅ Loaded ${filename}: ${arrayData.length} records`);
      return arrayData;
      
    } catch (error) {
      console.error(`❌ Error loading ${filename}:`, error);
      return [];
    }
  }

  /**
   * Load Customer Type data
   */
  async loadCustomerTypes(): Promise<CustomerType[]> {
    const rawData = await this.loadJsonFile<any>(DATA_FILES.CUSTOMER_TYPE);
    return this.transformToCustomerTypes(rawData);
  }

  /**
   * Load Account Industry data
   */
  async loadAccountIndustries(): Promise<AccountIndustry[]> {
    const rawData = await this.loadJsonFile<any>(DATA_FILES.ACCOUNT_INDUSTRY);
    return this.transformToAccountIndustries(rawData);
  }

  /**
   * Load Team data
   */
  async loadTeams(): Promise<Team[]> {
    const rawData = await this.loadJsonFile<any>(DATA_FILES.TEAM);
    return this.transformToTeams(rawData);
  }

  /**
   * Load ACV Range data
   */
  async loadACVRanges(): Promise<ACVRange[]> {
    const rawData = await this.loadJsonFile<any>(DATA_FILES.ACV_RANGE);
    return this.transformToACVRanges(rawData);
  }

  /**
   * Load all data at once for dashboard
   */
  async loadAllData(): Promise<DashboardData> {
    console.log('🔄 Loading all dashboard data...');
    
    const [customerTypes, accountIndustries, teams, acvRanges] = await Promise.all([
      this.loadCustomerTypes(),
      this.loadAccountIndustries(),
      this.loadTeams(),
      this.loadACVRanges()
    ]);

    const summary = this.calculateSummary(customerTypes, accountIndustries, teams, acvRanges);

    console.log('✅ All data loaded successfully');
    
    return {
      customerTypes,
      accountIndustries,
      teams,
      acvRanges,
      summary
    };
  }

  // ============================================================================
  // Data Transformation Methods
  // ============================================================================

  /**
   * Transform raw data to CustomerType format
   * Aggregates data by customer type across all quarters
   */
  private transformToCustomerTypes(rawData: any[]): CustomerType[] {
    const aggregated = new Map<string, { count: number; acv: number }>();

    // Aggregate data by customer type
    rawData.forEach(item => {
      const custType = item.Cust_Type || 'Unknown';
      const existing = aggregated.get(custType) || { count: 0, acv: 0 };
      aggregated.set(custType, {
        count: existing.count + (item.count || 0),
        acv: existing.acv + (item.acv || 0)
      });
    });

    // Calculate total for percentages
    const totalCount = Array.from(aggregated.values()).reduce((sum, item) => sum + item.count, 0);
    const totalACV = Array.from(aggregated.values()).reduce((sum, item) => sum + item.acv, 0);

    // Convert to CustomerType format
    return Array.from(aggregated.entries()).map(([type, data], index) => ({
      id: `customer_${index}`,
      type,
      count: data.count,
      percentage: totalCount > 0 ? (data.count / totalCount) * 100 : 0,
      description: `${type} customers with total ACV of $${data.acv.toLocaleString()}`
    }));
  }

  /**
   * Transform raw data to AccountIndustry format
   * Aggregates data by industry across all quarters
   */
  private transformToAccountIndustries(rawData: any[]): AccountIndustry[] {
    const aggregated = new Map<string, { count: number; acv: number }>();

    // Aggregate data by industry
    rawData.forEach(item => {
      const industry = item.Acct_Industry || 'Unknown';
      const existing = aggregated.get(industry) || { count: 0, acv: 0 };
      aggregated.set(industry, {
        count: existing.count + (item.count || 0),
        acv: existing.acv + (item.acv || 0)
      });
    });

    // Calculate total for percentages
    const totalCount = Array.from(aggregated.values()).reduce((sum, item) => sum + item.count, 0);

    // Convert to AccountIndustry format
    return Array.from(aggregated.entries()).map(([industry, data], index) => ({
      id: `industry_${index}`,
      industry,
      count: data.count,
      percentage: totalCount > 0 ? (data.count / totalCount) * 100 : 0,
      revenue: data.acv,
      description: `${industry} industry with ${data.count} accounts`
    }));
  }

  /**
   * Transform raw data to Team format
   * Aggregates data by team across all quarters
   */
  private transformToTeams(rawData: any[]): Team[] {
    const aggregated = new Map<string, { count: number; acv: number }>();

    // Aggregate data by team
    rawData.forEach(item => {
      const team = item.Team || 'Unknown';
      const existing = aggregated.get(team) || { count: 0, acv: 0 };
      aggregated.set(team, {
        count: existing.count + (item.count || 0),
        acv: existing.acv + (item.acv || 0)
      });
    });

    // Convert to Team format
    return Array.from(aggregated.entries()).map(([teamName, data], index) => ({
      id: `team_${index}`,
      name: teamName,
      memberCount: data.count, // Using count as member count for visualization
      performance: Math.min(100, Math.round((data.acv / 1000000) * 100)), // Performance based on ACV
      department: this.getDepartmentFromTeam(teamName),
      lead: 'TBD' // Provide default value for lead
    }));
  }

  /**
   * Transform raw data to ACVRange format
   * Aggregates data by ACV range across all quarters
   */
  private transformToACVRanges(rawData: any[]): ACVRange[] {
    const aggregated = new Map<string, { count: number; acv: number }>();

    // Aggregate data by ACV range
    rawData.forEach(item => {
      const range = item.ACV_Range || 'Unknown';
      const existing = aggregated.get(range) || { count: 0, acv: 0 };
      aggregated.set(range, {
        count: existing.count + (item.count || 0),
        acv: existing.acv + (item.acv || 0)
      });
    });

    // Calculate total for percentages
    const totalCount = Array.from(aggregated.values()).reduce((sum, item) => sum + item.count, 0);

    // Convert to ACVRange format with proper ordering
    const rangeOrder = ['<$20K', '$20K - 50K', '$50K - 100K', '$100K - 200K', '>=$200K'];

    return rangeOrder
      .filter(range => aggregated.has(range))
      .map((range, index) => {
        const data = aggregated.get(range)!;
        const { minValue, maxValue } = this.parseACVRange(range);

        return {
          id: `acv_${index}`,
          range,
          minValue,
          maxValue,
          count: data.count,
          percentage: totalCount > 0 ? (data.count / totalCount) * 100 : 0,
          totalValue: data.acv
        };
      });
  }

  /**
   * Helper method to determine department from team name
   */
  private getDepartmentFromTeam(teamName: string): string {
    if (teamName.includes('Enterprise')) return 'Enterprise Sales';
    if (['Asia Pac', 'Europe', 'North America', 'Latin America'].includes(teamName)) return 'Regional Sales';
    return 'Sales';
  }

  /**
   * Helper method to parse ACV range string into min/max values
   */
  private parseACVRange(range: string): { minValue: number; maxValue: number } {
    switch (range) {
      case '<$20K':
        return { minValue: 0, maxValue: 20000 };
      case '$20K - 50K':
        return { minValue: 20000, maxValue: 50000 };
      case '$50K - 100K':
        return { minValue: 50000, maxValue: 100000 };
      case '$100K - 200K':
        return { minValue: 100000, maxValue: 200000 };
      case '>=$200K':
        return { minValue: 200000, maxValue: Number.MAX_SAFE_INTEGER };
      default:
        return { minValue: 0, maxValue: 0 };
    }
  }

  /**
   * Calculate dashboard summary statistics
   */
  private calculateSummary(
    customerTypes: CustomerType[],
    accountIndustries: AccountIndustry[],
    teams: Team[],
    acvRanges: ACVRange[]
  ): DashboardSummary {
    const totalCustomers = customerTypes.reduce((sum, ct) => sum + ct.count, 0);
    const totalRevenue = accountIndustries.reduce((sum, ai) => sum + (ai.revenue || 0), 0);
    const totalTeams = teams.length;

    // Calculate average ACV from total revenue and total customers
    const averageACV = totalCustomers > 0 ? totalRevenue / totalCustomers : 0;

    return {
      totalCustomers,
      totalRevenue,
      totalTeams,
      averageACV,
      lastUpdated: new Date().toISOString()
    };
  }

  /**
   * Validate data structure (placeholder implementation)
   */
  validateData(data: any, type: string): DataValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!Array.isArray(data)) {
      errors.push(`Data for ${type} must be an array`);
    }

    if (data.length === 0) {
      warnings.push(`No data found for ${type}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Transform data with options (placeholder implementation)
   */
  transformData(data: any[], options: DataTransformOptions): any[] {
    let result = [...data];

    if (options.filterEmpty) {
      result = result.filter(item => item && Object.keys(item).length > 0);
    }

    if (options.sortBy) {
      result.sort((a, b) => {
        const aVal = a[options.sortBy!];
        const bVal = b[options.sortBy!];
        return aVal > bVal ? 1 : -1;
      });
    }

    return result;
  }
}

// ============================================================================
// Export singleton instance
// ============================================================================

export const dataService = new DataServiceImpl();

// ============================================================================
// Utility functions for chart data preparation
// ============================================================================

/**
 * Convert data to chart format for D3.js visualizations
 */
export const prepareChartData = {
  /**
   * Prepare data for bar charts
   */
  forBarChart(data: Array<{ label?: string; name?: string; value?: number; count?: number }>): BarChartData {
    return {
      categories: data.map(item => item.label || item.name || 'Unknown'),
      values: data.map(item => item.value || item.count || 0),
      colors: data.map((_, index) => `hsl(${(index * 360) / data.length}, 70%, 50%)`)
    };
  },

  /**
   * Prepare data for doughnut charts
   */
  forDoughnutChart(data: Array<{ label?: string; name?: string; value?: number; count?: number }>): DoughnutChartData {
    const total = data.reduce((sum, item) => sum + (item.value || item.count || 0), 0);
    
    const segments: ChartDataPoint[] = data.map((item, index) => ({
      label: item.label || item.name || 'Unknown',
      value: item.value || item.count || 0,
      percentage: total > 0 ? ((item.value || item.count || 0) / total) * 100 : 0,
      color: `hsl(${(index * 360) / data.length}, 70%, 50%)`
    }));

    return {
      segments,
      total,
      centerLabel: `Total: ${total}`
    };
  }
};

// Export the data service and utility functions
export default dataService;
