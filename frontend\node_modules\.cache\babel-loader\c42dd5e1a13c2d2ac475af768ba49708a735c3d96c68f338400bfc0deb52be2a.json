{"ast": null, "code": "export default function (d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n    node = this._root,\n    retainer,\n    previous,\n    next,\n    x0 = this._x0,\n    y0 = this._y0,\n    x1 = this._x1,\n    y1 = this._y1,\n    x,\n    y,\n    xm,\n    ym,\n    right,\n    bottom,\n    i,\n    j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm;else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym;else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[i + 1 & 3] || parent[i + 2 & 3] || parent[i + 3 & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return next ? previous.next = next : delete previous.next, this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3]) && node === (parent[3] || parent[2] || parent[1] || parent[0]) && !node.length) {\n    if (retainer) retainer[j] = node;else this._root = node;\n  }\n  return this;\n}\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}", "map": {"version": 3, "names": ["d", "isNaN", "x", "_x", "call", "y", "_y", "parent", "node", "_root", "retainer", "previous", "next", "x0", "_x0", "y0", "_y0", "x1", "_x1", "y1", "_y1", "xm", "ym", "right", "bottom", "i", "j", "length", "data", "removeAll", "n", "remove"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-quadtree/src/remove.js"], "sourcesContent": ["export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1,\n      x,\n      y,\n      xm,\n      ym,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3])\n      && node === (parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,IAAIC,KAAK,CAACC,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACC,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,IAAIC,KAAK,CAACI,CAAC,GAAG,CAAC,IAAI,CAACC,EAAE,CAACF,IAAI,CAAC,IAAI,EAAEJ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;EAEzF,IAAIO,MAAM;IACNC,IAAI,GAAG,IAAI,CAACC,KAAK;IACjBC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACbC,EAAE,GAAG,IAAI,CAACC,GAAG;IACblB,CAAC;IACDG,CAAC;IACDgB,EAAE;IACFC,EAAE;IACFC,KAAK;IACLC,MAAM;IACNC,CAAC;IACDC,CAAC;;EAEL;EACA,IAAI,CAAClB,IAAI,EAAE,OAAO,IAAI;;EAEtB;EACA;EACA,IAAIA,IAAI,CAACmB,MAAM,EAAE,OAAO,IAAI,EAAE;IAC5B,IAAIJ,KAAK,GAAGrB,CAAC,KAAKmB,EAAE,GAAG,CAACR,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGQ,EAAE,CAAC,KAAMJ,EAAE,GAAGI,EAAE;IAC5D,IAAIG,MAAM,GAAGnB,CAAC,KAAKiB,EAAE,GAAG,CAACP,EAAE,GAAGI,EAAE,IAAI,CAAC,CAAC,EAAEJ,EAAE,GAAGO,EAAE,CAAC,KAAMH,EAAE,GAAGG,EAAE;IAC7D,IAAI,EAAEf,MAAM,GAAGC,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACiB,CAAC,GAAGD,MAAM,IAAI,CAAC,GAAGD,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI;IACvE,IAAI,CAACf,IAAI,CAACmB,MAAM,EAAE;IAClB,IAAIpB,MAAM,CAAEkB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIlB,MAAM,CAAEkB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,IAAIlB,MAAM,CAAEkB,CAAC,GAAG,CAAC,GAAI,CAAC,CAAC,EAAEf,QAAQ,GAAGH,MAAM,EAAEmB,CAAC,GAAGD,CAAC;EACjG;;EAEA;EACA,OAAOjB,IAAI,CAACoB,IAAI,KAAK5B,CAAC,EAAE,IAAI,EAAEW,QAAQ,GAAGH,IAAI,EAAEA,IAAI,GAAGA,IAAI,CAACI,IAAI,CAAC,EAAE,OAAO,IAAI;EAC7E,IAAIA,IAAI,GAAGJ,IAAI,CAACI,IAAI,EAAE,OAAOJ,IAAI,CAACI,IAAI;;EAEtC;EACA,IAAID,QAAQ,EAAE,OAAQC,IAAI,GAAGD,QAAQ,CAACC,IAAI,GAAGA,IAAI,GAAG,OAAOD,QAAQ,CAACC,IAAI,EAAG,IAAI;;EAE/E;EACA,IAAI,CAACL,MAAM,EAAE,OAAO,IAAI,CAACE,KAAK,GAAGG,IAAI,EAAE,IAAI;;EAE3C;EACAA,IAAI,GAAGL,MAAM,CAACkB,CAAC,CAAC,GAAGb,IAAI,GAAG,OAAOL,MAAM,CAACkB,CAAC,CAAC;;EAE1C;EACA,IAAI,CAACjB,IAAI,GAAGD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KACrDC,IAAI,MAAMD,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC,IAC3D,CAACC,IAAI,CAACmB,MAAM,EAAE;IACnB,IAAIjB,QAAQ,EAAEA,QAAQ,CAACgB,CAAC,CAAC,GAAGlB,IAAI,CAAC,KAC5B,IAAI,CAACC,KAAK,GAAGD,IAAI;EACxB;EAEA,OAAO,IAAI;AACb;AAEA,OAAO,SAASqB,SAASA,CAACD,IAAI,EAAE;EAC9B,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEK,CAAC,GAAGF,IAAI,CAACD,MAAM,EAAEF,CAAC,GAAGK,CAAC,EAAE,EAAEL,CAAC,EAAE,IAAI,CAACM,MAAM,CAACH,IAAI,CAACH,CAAC,CAAC,CAAC;EACjE,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}