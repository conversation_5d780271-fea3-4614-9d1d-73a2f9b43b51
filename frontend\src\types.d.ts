/**
 * Global TypeScript type definitions for SkyGeni Dashboard Frontend
 * Contains all interfaces and types used across the React application
 */

// ============================================================================
// API Response Types (matching backend)
// ============================================================================

/**
 * Standard API response wrapper from backend
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

/**
 * Error response structure from backend
 */
export interface ApiError {
  success: false;
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// ============================================================================
// Data Structure Types (matching backend)
// ============================================================================

/**
 * Customer Type data structure
 */
export interface CustomerType {
  id: string;
  type: string;
  count: number;
  percentage: number;
  description?: string;
}

/**
 * Account Industry data structure
 */
export interface AccountIndustry {
  id: string;
  industry: string;
  count: number;
  percentage: number;
  revenue?: number;
  description?: string;
}

/**
 * Team data structure
 */
export interface Team {
  id: string;
  name: string;
  memberCount: number;
  performance: number;
  department: string;
  lead?: string;
}

/**
 * ACV Range data structure
 */
export interface ACVRange {
  id: string;
  range: string;
  minValue: number;
  maxValue: number;
  count: number;
  percentage: number;
  totalValue: number;
}

/**
 * Complete dashboard data structure
 */
export interface DashboardData {
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  summary: DashboardSummary;
}

/**
 * Dashboard summary statistics
 */
export interface DashboardSummary {
  totalCustomers: number;
  totalRevenue: number;
  totalTeams: number;
  averageACV: number;
  lastUpdated: string;
}

// ============================================================================
// Chart Data Types
// ============================================================================

/**
 * Generic chart data point for D3.js visualizations
 */
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
}

/**
 * Bar chart specific data structure
 */
export interface BarChartData {
  categories: string[];
  values: number[];
  colors?: string[];
}

/**
 * Doughnut chart specific data structure
 */
export interface DoughnutChartData {
  segments: ChartDataPoint[];
  total: number;
  centerLabel?: string;
}

// ============================================================================
// Component Props Types
// ============================================================================

/**
 * Base props for chart components
 */
export interface BaseChartProps {
  width?: number;
  height?: number;
  margin?: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  className?: string;
  title?: string;
}

/**
 * Bar chart component props
 */
export interface BarChartProps extends BaseChartProps {
  data: BarChartData;
  xAxisLabel?: string;
  yAxisLabel?: string;
  showGrid?: boolean;
  animate?: boolean;
}

/**
 * Doughnut chart component props
 */
export interface DoughnutChartProps extends BaseChartProps {
  data: DoughnutChartData;
  innerRadius?: number;
  outerRadius?: number;
  showLabels?: boolean;
  showLegend?: boolean;
  animate?: boolean;
}

/**
 * Data card component props
 */
export interface DataCardProps {
  title: string;
  data: any[];
  chartType: 'bar' | 'doughnut';
  loading?: boolean;
  error?: string;
  className?: string;
  elevation?: number;
}

// ============================================================================
// Redux State Types
// ============================================================================

/**
 * Loading states for async operations
 */
export type LoadingState = 'idle' | 'pending' | 'succeeded' | 'failed';

/**
 * Data slice state
 */
export interface DataState {
  dashboardData: DashboardData | null;
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  loading: LoadingState;
  error: string | null;
  lastFetched: string | null;
}

/**
 * Root state interface
 */
export interface RootState {
  data: DataState;
}

// ============================================================================
// Hook Types
// ============================================================================

/**
 * Return type for useData hook
 */
export interface UseDataReturn {
  dashboardData: DashboardData | null;
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  loading: LoadingState;
  error: string | null;
  refetch: () => void;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
}

// ============================================================================
// UI Component Types
// ============================================================================

/**
 * Theme configuration
 */
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
}

/**
 * Layout component props
 */
export interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

/**
 * Header component props
 */
export interface HeaderProps {
  title?: string;
  showRefreshButton?: boolean;
  onRefresh?: () => void;
}

/**
 * Footer component props
 */
export interface FooterProps {
  showLastUpdated?: boolean;
  lastUpdated?: string;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Generic async thunk state
 */
export interface AsyncThunkState {
  loading: LoadingState;
  error: string | null;
}

/**
 * Chart color palette
 */
export interface ColorPalette {
  primary: string[];
  secondary: string[];
  accent: string[];
}

/**
 * Responsive breakpoints
 */
export interface Breakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

// ============================================================================
// D3.js Integration Types
// ============================================================================

/**
 * D3 selection type for chart components
 */
export type D3Selection = d3.Selection<SVGElement, unknown, null, undefined>;

/**
 * D3 scale types
 */
export type D3Scale = d3.ScaleLinear<number, number> | d3.ScaleBand<string>;

/**
 * Chart animation configuration
 */
export interface ChartAnimation {
  duration: number;
  easing: string;
  delay?: number;
}

// ============================================================================
// Error Handling Types
// ============================================================================

/**
 * Error boundary state
 */
export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

/**
 * Error display props
 */
export interface ErrorMessageProps {
  error: string | Error;
  onRetry?: () => void;
  showRetryButton?: boolean;
  className?: string;
}

// ============================================================================
// Form and Input Types
// ============================================================================

/**
 * Filter options for data
 */
export interface FilterOptions {
  searchTerm?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  dateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Export options
 */
export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf';
  includeCharts: boolean;
  filename?: string;
}
