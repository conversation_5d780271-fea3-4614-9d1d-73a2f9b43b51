{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\App.tsx\";\n/**\n * Main App Component for SkyGeni Dashboard\n * \n * Root application component that provides:\n * - Redux store provider\n * - Material-UI theme provider\n * - React Router setup\n * - Global error boundary\n * - Application-wide providers\n */\n\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { store } from './redux/store';\nimport Dashboard from './pages/Dashboard';\n// import ErrorBoundary from './components/common/ErrorBoundary';\n\n// ============================================================================\n// Theme Configuration\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#6366f1',\n      // Modern indigo\n      light: '#818cf8',\n      dark: '#4f46e5'\n    },\n    secondary: {\n      main: '#f59e0b',\n      // Modern amber\n      light: '#fbbf24',\n      dark: '#d97706'\n    },\n    background: {\n      default: '#f8fafc',\n      // Modern gray-50\n      paper: '#ffffff'\n    },\n    text: {\n      primary: '#1e293b',\n      // Modern slate-800\n      secondary: '#64748b' // Modern slate-500\n    },\n    success: {\n      main: '#10b981' // Modern emerald\n    },\n    error: {\n      main: '#ef4444' // Modern red\n    },\n    warning: {\n      main: '#f59e0b' // Modern amber\n    },\n    info: {\n      main: '#3b82f6' // Modern blue\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n      letterSpacing: '-0.025em'\n    },\n    h2: {\n      fontWeight: 700,\n      letterSpacing: '-0.025em'\n    },\n    h3: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em'\n    },\n    h4: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em'\n    },\n    h5: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em'\n    },\n    h6: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em'\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 4px 16px rgba(0,0,0,0.15)'\n          }\n        }\n      }\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500\n        }\n      }\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          fontWeight: 500\n        }\n      }\n    }\n  }\n});\n\n// ============================================================================\n// Error Boundary Component\n// ============================================================================\n\nclass AppErrorBoundary extends React.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Application Error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          textAlign: 'center',\n          minHeight: '100vh',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Something went wrong\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"The application encountered an unexpected error.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          style: {\n            padding: '0.5rem 1rem',\n            backgroundColor: '#1976d2',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: 'pointer',\n            marginTop: '1rem'\n          },\n          children: \"Reload Application\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"details\", {\n          style: {\n            marginTop: '1rem',\n            textAlign: 'left'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n            children: \"Error Details (Development)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n            style: {\n              backgroundColor: '#f5f5f5',\n              padding: '1rem',\n              borderRadius: '4px',\n              overflow: 'auto',\n              maxWidth: '80vw'\n            },\n            children: this.state.error.stack\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\n\n// ============================================================================\n// App Component\n// ============================================================================\n\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(AppErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(Provider, {\n      store: store,\n      children: /*#__PURE__*/_jsxDEV(ThemeProvider, {\n        theme: theme,\n        children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Router, {\n          future: {\n            v7_startTransition: true,\n            v7_relativeSplatPath: true\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"*\",\n              element: /*#__PURE__*/_jsxDEV(Navigate, {\n                to: \"/dashboard\",\n                replace: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "ThemeProvider", "createTheme", "CssBaseline", "store", "Dashboard", "jsxDEV", "_jsxDEV", "theme", "palette", "mode", "primary", "main", "light", "dark", "secondary", "background", "default", "paper", "text", "success", "error", "warning", "info", "typography", "fontFamily", "h1", "fontWeight", "letterSpacing", "h2", "h3", "h4", "h5", "h6", "shape", "borderRadius", "components", "MuiCard", "styleOverrides", "root", "boxShadow", "MuiB<PERSON>on", "textTransform", "MuiChip", "AppError<PERSON>ou<PERSON>ry", "Component", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "componentDidCatch", "errorInfo", "console", "render", "style", "padding", "textAlign", "minHeight", "display", "flexDirection", "justifyContent", "alignItems", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "backgroundColor", "color", "border", "cursor", "marginTop", "process", "env", "NODE_ENV", "overflow", "max<PERSON><PERSON><PERSON>", "stack", "App", "future", "v7_startTransition", "v7_relativeSplatPath", "path", "element", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/App.tsx"], "sourcesContent": ["/**\n * Main App Component for SkyGeni Dashboard\n * \n * Root application component that provides:\n * - Redux store provider\n * - Material-UI theme provider\n * - React Router setup\n * - Global error boundary\n * - Application-wide providers\n */\n\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport { CssBaseline } from '@mui/material';\nimport { store } from './redux/store';\nimport Dashboard from './pages/Dashboard';\n// import ErrorBoundary from './components/common/ErrorBoundary';\n\n// ============================================================================\n// Theme Configuration\n// ============================================================================\n\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    primary: {\n      main: '#6366f1', // Modern indigo\n      light: '#818cf8',\n      dark: '#4f46e5',\n    },\n    secondary: {\n      main: '#f59e0b', // Modern amber\n      light: '#fbbf24',\n      dark: '#d97706',\n    },\n    background: {\n      default: '#f8fafc', // Modern gray-50\n      paper: '#ffffff',\n    },\n    text: {\n      primary: '#1e293b', // Modern slate-800\n      secondary: '#64748b', // Modern slate-500\n    },\n    success: {\n      main: '#10b981', // Modern emerald\n    },\n    error: {\n      main: '#ef4444', // Modern red\n    },\n    warning: {\n      main: '#f59e0b', // Modern amber\n    },\n    info: {\n      main: '#3b82f6', // Modern blue\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n      letterSpacing: '-0.025em',\n    },\n    h2: {\n      fontWeight: 700,\n      letterSpacing: '-0.025em',\n    },\n    h3: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em',\n    },\n    h4: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em',\n    },\n    h5: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em',\n    },\n    h6: {\n      fontWeight: 600,\n      letterSpacing: '-0.025em',\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n          '&:hover': {\n            boxShadow: '0 4px 16px rgba(0,0,0,0.15)',\n          },\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          fontWeight: 500,\n        },\n      },\n    },\n  },\n});\n\n// ============================================================================\n// Error Boundary Component\n// ============================================================================\n\nclass AppErrorBoundary extends React.Component<\n  { children: React.ReactNode },\n  { hasError: boolean; error: Error | null }\n> {\n  constructor(props: { children: React.ReactNode }) {\n    super(props);\n    this.state = { hasError: false, error: null };\n  }\n\n  static getDerivedStateFromError(error: Error) {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Application Error:', error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div style={{ \n          padding: '2rem', \n          textAlign: 'center',\n          minHeight: '100vh',\n          display: 'flex',\n          flexDirection: 'column',\n          justifyContent: 'center',\n          alignItems: 'center',\n        }}>\n          <h1>Something went wrong</h1>\n          <p>The application encountered an unexpected error.</p>\n          <button \n            onClick={() => window.location.reload()}\n            style={{\n              padding: '0.5rem 1rem',\n              backgroundColor: '#1976d2',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              marginTop: '1rem',\n            }}\n          >\n            Reload Application\n          </button>\n          {process.env.NODE_ENV === 'development' && this.state.error && (\n            <details style={{ marginTop: '1rem', textAlign: 'left' }}>\n              <summary>Error Details (Development)</summary>\n              <pre style={{ \n                backgroundColor: '#f5f5f5', \n                padding: '1rem', \n                borderRadius: '4px',\n                overflow: 'auto',\n                maxWidth: '80vw',\n              }}>\n                {this.state.error.stack}\n              </pre>\n            </details>\n          )}\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// ============================================================================\n// App Component\n// ============================================================================\n\nconst App: React.FC = () => {\n  return (\n    <AppErrorBoundary>\n      <Provider store={store}>\n        <ThemeProvider theme={theme}>\n          <CssBaseline />\n          <Router future={{ v7_startTransition: true, v7_relativeSplatPath: true }}>\n            <Routes>\n              {/* Main Dashboard Route */}\n              <Route path=\"/dashboard\" element={<Dashboard />} />\n              \n              {/* Default Route - Redirect to Dashboard */}\n              <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n              \n              {/* Catch-all Route - Redirect to Dashboard */}\n              <Route path=\"*\" element={<Navigate to=\"/dashboard\" replace />} />\n            </Routes>\n          </Router>\n        </ThemeProvider>\n      </Provider>\n    </AppErrorBoundary>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,SAAS,MAAM,mBAAmB;AACzC;;AAEA;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,KAAK,GAAGN,WAAW,CAAC;EACxBO,OAAO,EAAE;IACPC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MAAE;MACjBC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAAE;MACpBC,KAAK,EAAE;IACT,CAAC;IACDC,IAAI,EAAE;MACJR,OAAO,EAAE,SAAS;MAAE;MACpBI,SAAS,EAAE,SAAS,CAAE;IACxB,CAAC;IACDK,OAAO,EAAE;MACPR,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDS,KAAK,EAAE;MACLT,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDU,OAAO,EAAE;MACPV,IAAI,EAAE,SAAS,CAAE;IACnB,CAAC;IACDW,IAAI,EAAE;MACJX,IAAI,EAAE,SAAS,CAAE;IACnB;EACF,CAAC;EACDY,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDC,EAAE,EAAE;MACFF,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDE,EAAE,EAAE;MACFH,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDG,EAAE,EAAE;MACFJ,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDI,EAAE,EAAE;MACFL,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB,CAAC;IACDK,EAAE,EAAE;MACFN,UAAU,EAAE,GAAG;MACfC,aAAa,EAAE;IACjB;EACF,CAAC;EACDM,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,OAAO,EAAE;MACPC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,SAAS,EAAE,2BAA2B;UACtC,SAAS,EAAE;YACTA,SAAS,EAAE;UACb;QACF;MACF;IACF,CAAC;IACDC,SAAS,EAAE;MACTH,cAAc,EAAE;QACdC,IAAI,EAAE;UACJG,aAAa,EAAE,MAAM;UACrBf,UAAU,EAAE;QACd;MACF;IACF,CAAC;IACDgB,OAAO,EAAE;MACPL,cAAc,EAAE;QACdC,IAAI,EAAE;UACJZ,UAAU,EAAE;QACd;MACF;IACF;EACF;AACF,CAAC,CAAC;;AAEF;AACA;AACA;;AAEA,MAAMiB,gBAAgB,SAASlD,KAAK,CAACmD,SAAS,CAG5C;EACAC,WAAWA,CAACC,KAAoC,EAAE;IAChD,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE,KAAK;MAAE5B,KAAK,EAAE;IAAK,CAAC;EAC/C;EAEA,OAAO6B,wBAAwBA,CAAC7B,KAAY,EAAE;IAC5C,OAAO;MAAE4B,QAAQ,EAAE,IAAI;MAAE5B;IAAM,CAAC;EAClC;EAEA8B,iBAAiBA,CAAC9B,KAAY,EAAE+B,SAA0B,EAAE;IAC1DC,OAAO,CAAChC,KAAK,CAAC,oBAAoB,EAAEA,KAAK,EAAE+B,SAAS,CAAC;EACvD;EAEAE,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACN,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACE1C,OAAA;QAAKgD,KAAK,EAAE;UACVC,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,OAAO;UAClBC,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,cAAc,EAAE,QAAQ;UACxBC,UAAU,EAAE;QACd,CAAE;QAAAC,QAAA,gBACAxD,OAAA;UAAAwD,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B5D,OAAA;UAAAwD,QAAA,EAAG;QAAgD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvD5D,OAAA;UACE6D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;UACxChB,KAAK,EAAE;YACLC,OAAO,EAAE,aAAa;YACtBgB,eAAe,EAAE,SAAS;YAC1BC,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,MAAM;YACdvC,YAAY,EAAE,KAAK;YACnBwC,MAAM,EAAE,SAAS;YACjBC,SAAS,EAAE;UACb,CAAE;UAAAb,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAAC/B,KAAK,CAAC3B,KAAK,iBACzDd,OAAA;UAASgD,KAAK,EAAE;YAAEqB,SAAS,EAAE,MAAM;YAAEnB,SAAS,EAAE;UAAO,CAAE;UAAAM,QAAA,gBACvDxD,OAAA;YAAAwD,QAAA,EAAS;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eAC9C5D,OAAA;YAAKgD,KAAK,EAAE;cACViB,eAAe,EAAE,SAAS;cAC1BhB,OAAO,EAAE,MAAM;cACfrB,YAAY,EAAE,KAAK;cACnB6C,QAAQ,EAAE,MAAM;cAChBC,QAAQ,EAAE;YACZ,CAAE;YAAAlB,QAAA,EACC,IAAI,CAACf,KAAK,CAAC3B,KAAK,CAAC6D;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACV;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAEV;IAEA,OAAO,IAAI,CAACpB,KAAK,CAACgB,QAAQ;EAC5B;AACF;;AAEA;AACA;AACA;;AAEA,MAAMoB,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACE5E,OAAA,CAACqC,gBAAgB;IAAAmB,QAAA,eACfxD,OAAA,CAACZ,QAAQ;MAACS,KAAK,EAAEA,KAAM;MAAA2D,QAAA,eACrBxD,OAAA,CAACN,aAAa;QAACO,KAAK,EAAEA,KAAM;QAAAuD,QAAA,gBAC1BxD,OAAA,CAACJ,WAAW;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf5D,OAAA,CAACV,MAAM;UAACuF,MAAM,EAAE;YAAEC,kBAAkB,EAAE,IAAI;YAAEC,oBAAoB,EAAE;UAAK,CAAE;UAAAvB,QAAA,eACvExD,OAAA,CAACT,MAAM;YAAAiE,QAAA,gBAELxD,OAAA,CAACR,KAAK;cAACwF,IAAI,EAAC,YAAY;cAACC,OAAO,eAAEjF,OAAA,CAACF,SAAS;gBAAA2D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGnD5D,OAAA,CAACR,KAAK;cAACwF,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEjF,OAAA,CAACP,QAAQ;gBAACyF,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGjE5D,OAAA,CAACR,KAAK;cAACwF,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEjF,OAAA,CAACP,QAAQ;gBAACyF,EAAE,EAAC,YAAY;gBAACC,OAAO;cAAA;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEvB,CAAC;AAACwB,EAAA,GAtBIR,GAAa;AAwBnB,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}