/**
 * CardChart Component
 * Professional card component with D3.js charts, loading states, and info chips
 */

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Skeleton,
  Fade,
  Grow,
} from '@mui/material';
import DonutChart from '../charts/DonutChart';
import SimpleBarChart from '../charts/SimpleBarChart';

// ============================================================================
// Types
// ============================================================================

interface CardChartProps {
  title: string;
  data: any[];
  chartType: 'bar' | 'donut';
  loading?: boolean;
  error?: string | null;
  totalLabel?: string;
  totalValue?: number;
  averageLabel?: string;
  averageValue?: number;
  itemsCount?: number;
  onRefresh?: () => void;
  className?: string;
  elevation?: number;
}

// ============================================================================
// Helper Functions
// ============================================================================

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toLocaleString();
};

const transformDataForChart = (data: any[], chartType: 'bar' | 'donut') => {
  if (chartType === 'donut') {
    return data.map(item => ({
      label: item.type || item.range || item.industry || item.name || 'Unknown',
      value: item.count || item.value || item.members || 0,
    }));
  } else {
    return data.map(item => ({
      label: item.industry || item.name || item.type || item.range || 'Unknown',
      value: item.count || item.revenue || item.performance || item.value || item.members || 0,
    }));
  }
};

// ============================================================================
// CardChart Component
// ============================================================================

const CardChart: React.FC<CardChartProps> = ({
  title,
  data,
  chartType,
  loading = false,
  error = null,
  totalLabel = 'Total',
  totalValue,
  averageLabel = 'Average',
  averageValue,
  itemsCount,
  onRefresh,
  className,
  elevation = 2,
}) => {
  // Calculate metrics if not provided
  const calculatedTotal = totalValue ?? data.reduce((sum, item) => {
    return sum + (item.count || item.value || item.revenue || item.members || 0);
  }, 0);

  const calculatedAverage = averageValue ?? (data.length > 0 ? calculatedTotal / data.length : 0);
  const calculatedItems = itemsCount ?? data.length;

  // Transform data for charts
  const chartData = transformDataForChart(data, chartType);

  // Calculate center text for donut charts
  const centerText = chartType === 'donut' ? calculatedTotal.toLocaleString() : undefined;

  return (
    <Grow in timeout={800}>
      <Card
        className={className}
        elevation={elevation}
        sx={{
          height: '100%',
          borderRadius: 3,
          transition: 'all 0.3s ease-in-out',
          '&:hover': {
            transform: 'scale(1.02)',
            boxShadow: (theme) => theme.shadows[8],
          },
          p: 2,
        }}
      >
        <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
          {/* Header */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                mb: 1,
              }}
            >
              {title}
            </Typography>

            {/* Info Chips */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
              <Chip
                label={`${totalLabel}: ${formatNumber(calculatedTotal)}`}
                size="small"
                color="primary"
                variant="outlined"
              />
              <Chip
                label={`Items: ${calculatedItems}`}
                size="small"
                color="secondary"
                variant="outlined"
              />
              <Chip
                label={`${averageLabel}: ${formatNumber(calculatedAverage)}`}
                size="small"
                color="info"
                variant="outlined"
              />
            </Box>
          </Box>

          {/* Chart Content */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: 300,
              position: 'relative',
            }}
          >
            {loading ? (
              <Box sx={{ width: '100%', textAlign: 'center' }}>
                <Skeleton variant="rectangular" width="100%" height={250} sx={{ borderRadius: 2 }} />
                <Box sx={{ mt: 1, display: 'flex', gap: 1, justifyContent: 'center' }}>
                  <Skeleton variant="text" width={80} />
                  <Skeleton variant="text" width={60} />
                  <Skeleton variant="text" width={90} />
                </Box>
              </Box>
            ) : error ? (
              <Box sx={{ textAlign: 'center', color: 'error.main' }}>
                <Typography variant="body2" color="error">
                  {error}
                </Typography>
                {onRefresh && (
                  <Typography
                    variant="body2"
                    sx={{ mt: 1, cursor: 'pointer', textDecoration: 'underline' }}
                    onClick={onRefresh}
                  >
                    Try again
                  </Typography>
                )}
              </Box>
            ) : data.length === 0 ? (
              <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body2">No data available</Typography>
              </Box>
            ) : (
              <Fade in timeout={1000}>
                <Box>
                  {chartType === 'donut' ? (
                    <DonutChart
                      data={chartData}
                      width={280}
                      height={280}
                      innerRadius={60}
                      outerRadius={120}
                      centerText={centerText}
                    />
                  ) : (
                    <SimpleBarChart
                      data={chartData}
                      width={350}
                      height={250}
                      margin={{ top: 20, right: 30, bottom: 60, left: 60 }}
                    />
                  )}
                </Box>
              </Fade>
            )}
          </Box>
        </CardContent>
      </Card>
    </Grow>
  );
};

export default CardChart;
