/**
 * CardChart Component
 * Professional card component with D3.js charts, loading states, and info chips
 */

import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Chip,
  Skeleton,
  Fade,
  Grow,
} from '@mui/material';
import DonutChart from '../charts/DonutChart';
import SimpleBarChart from '../charts/SimpleBarChart';

// ============================================================================
// Types
// ============================================================================

interface CardChartProps {
  title: string;
  data: any[];
  chartType: 'bar' | 'donut';
  loading?: boolean;
  error?: string | null;
  totalLabel?: string;
  totalValue?: number;
  averageLabel?: string;
  averageValue?: number;
  itemsCount?: number;
  onRefresh?: () => void;
  className?: string;
  elevation?: number;
}

// ============================================================================
// Helper Functions
// ============================================================================

const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toLocaleString();
};

/**
 * Transform raw data into chart-compatible format
 * Preserves original data for detailed tooltips
 */
const transformDataForChart = (data: any[], chartType: 'bar' | 'donut') => {
  if (chartType === 'donut') {
    return data.map(item => ({
      label: item.type || item.range || item.industry || item.name || 'Unknown',
      value: item.count || item.value || item.memberCount || 0,
      originalData: item, // Keep original data for detailed tooltips
    }));
  } else {
    return data.map(item => ({
      label: item.industry || item.name || item.type || item.range || 'Unknown',
      value: item.revenue || item.memberCount || item.count || item.performance || item.value || 0,
      originalData: item, // Keep original data for detailed tooltips
    }));
  }
};

// ============================================================================
// CardChart Component
// ============================================================================

const CardChart: React.FC<CardChartProps> = ({
  title,
  data,
  chartType,
  loading = false,
  error = null,
  totalLabel = 'Total',
  totalValue,
  averageLabel = 'Average',
  averageValue,
  itemsCount,
  onRefresh,
  className,
  elevation = 2,
}) => {
  // Calculate metrics if not provided
  const calculatedTotal = totalValue ?? data.reduce((sum, item) => {
    return sum + (item.count || item.value || item.revenue || item.members || 0);
  }, 0);

  const calculatedAverage = averageValue ?? (data.length > 0 ? calculatedTotal / data.length : 0);
  const calculatedItems = itemsCount ?? data.length;

  // Transform data for charts
  const chartData = transformDataForChart(data, chartType);

  // Calculate center text for donut charts
  const centerText = chartType === 'donut' ? calculatedTotal.toLocaleString() : undefined;

  return (
    <Grow in timeout={800}>
      <Card
        className={className}
        elevation={elevation}
        sx={{
          height: '100%',
          borderRadius: 3,
          background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
          border: '1px solid rgba(0, 0, 0, 0.05)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          '&:hover': {
            transform: 'translateY(-4px) scale(1.01)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.12)',
            borderColor: 'rgba(25, 118, 210, 0.2)',
          },
          overflow: 'hidden',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: 'linear-gradient(90deg, #1976d2, #dc004e)',
            opacity: 0,
            transition: 'opacity 0.3s ease',
          },
          '&:hover::before': {
            opacity: 1,
          },
        }}
      >
        <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
          {/* Header */}
          <Box sx={{ mb: 3 }}>
            <Typography
              variant="h6"
              component="h3"
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                mb: 2,
                fontSize: '1.1rem',
              }}
            >
              {title}
            </Typography>

            {/* Info Chips */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
              <Chip
                label={`${totalLabel}: ${formatNumber(calculatedTotal)}`}
                size="small"
                sx={{
                  backgroundColor: 'rgba(25, 118, 210, 0.1)',
                  color: '#1976d2',
                  fontWeight: 600,
                  border: '1px solid rgba(25, 118, 210, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(25, 118, 210, 0.15)',
                  },
                }}
              />
              <Chip
                label={`Items: ${calculatedItems}`}
                size="small"
                sx={{
                  backgroundColor: 'rgba(220, 0, 78, 0.1)',
                  color: '#dc004e',
                  fontWeight: 600,
                  border: '1px solid rgba(220, 0, 78, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(220, 0, 78, 0.15)',
                  },
                }}
              />
              <Chip
                label={`${averageLabel}: ${formatNumber(calculatedAverage)}`}
                size="small"
                sx={{
                  backgroundColor: 'rgba(2, 136, 209, 0.1)',
                  color: '#0288d1',
                  fontWeight: 600,
                  border: '1px solid rgba(2, 136, 209, 0.2)',
                  '&:hover': {
                    backgroundColor: 'rgba(2, 136, 209, 0.15)',
                  },
                }}
              />
            </Box>
          </Box>

          {/* Chart Content */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: 320,
              position: 'relative',
              backgroundColor: 'rgba(248, 250, 252, 0.5)',
              borderRadius: 2,
              border: '1px solid rgba(0, 0, 0, 0.05)',
            }}
          >
            {loading ? (
              <Box sx={{ width: '100%', textAlign: 'center', p: 2 }}>
                <Skeleton
                  variant="rectangular"
                  width="100%"
                  height={280}
                  sx={{
                    borderRadius: 2,
                    backgroundColor: 'rgba(0, 0, 0, 0.06)',
                  }}
                />
                <Box sx={{ mt: 2, display: 'flex', gap: 1, justifyContent: 'center' }}>
                  <Skeleton variant="rounded" width={80} height={24} />
                  <Skeleton variant="rounded" width={60} height={24} />
                  <Skeleton variant="rounded" width={90} height={24} />
                </Box>
              </Box>
            ) : error ? (
              <Box sx={{ textAlign: 'center', color: 'error.main', p: 3 }}>
                <Typography variant="body2" color="error" sx={{ mb: 1 }}>
                  {error}
                </Typography>
                {onRefresh && (
                  <Typography
                    variant="body2"
                    sx={{
                      cursor: 'pointer',
                      textDecoration: 'underline',
                      color: 'primary.main',
                      '&:hover': { color: 'primary.dark' }
                    }}
                    onClick={onRefresh}
                  >
                    Try again
                  </Typography>
                )}
              </Box>
            ) : data.length === 0 ? (
              <Box sx={{ textAlign: 'center', color: 'text.secondary', p: 3 }}>
                <Typography variant="body2">No data available</Typography>
              </Box>
            ) : (
              <Fade in timeout={1200}>
                <Box sx={{ p: 1 }}>
                  {chartType === 'donut' ? (
                    <DonutChart
                      data={chartData}
                      width={300}
                      height={300}
                      innerRadius={65}
                      outerRadius={130}
                      centerText={centerText}
                    />
                  ) : (
                    <SimpleBarChart
                      data={chartData}
                      width={380}
                      height={280}
                      margin={{ top: 20, right: 30, bottom: 70, left: 70 }}
                    />
                  )}
                </Box>
              </Fade>
            )}
          </Box>
        </CardContent>
      </Card>
    </Grow>
  );
};

export default CardChart;
