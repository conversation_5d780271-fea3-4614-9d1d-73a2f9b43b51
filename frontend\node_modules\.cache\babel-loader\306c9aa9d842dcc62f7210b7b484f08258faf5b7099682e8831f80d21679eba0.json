{"ast": null, "code": "export default function (x) {\n  return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i,\n    coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient, +x.slice(i + 1)];\n}", "map": {"version": 3, "names": ["x", "Math", "abs", "round", "toLocaleString", "replace", "toString", "formatDecimalParts", "p", "i", "toExponential", "indexOf", "coefficient", "slice", "length"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-format/src/formatDecimal.js"], "sourcesContent": ["export default function(x) {\n  return Math.abs(x = Math.round(x)) >= 1e21\n      ? x.toLocaleString(\"en\").replace(/,/g, \"\")\n      : x.toString(10);\n}\n\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nexport function formatDecimalParts(x, p) {\n  if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n  var i, coefficient = x.slice(0, i);\n\n  // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n  // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n  return [\n    coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n    +x.slice(i + 1)\n  ];\n}\n"], "mappings": "AAAA,eAAe,UAASA,CAAC,EAAE;EACzB,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,GAAGC,IAAI,CAACE,KAAK,CAACH,CAAC,CAAC,CAAC,IAAI,IAAI,GACpCA,CAAC,CAACI,cAAc,CAAC,IAAI,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,GACxCL,CAAC,CAACM,QAAQ,CAAC,EAAE,CAAC;AACtB;;AAEA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACP,CAAC,EAAEQ,CAAC,EAAE;EACvC,IAAI,CAACC,CAAC,GAAG,CAACT,CAAC,GAAGQ,CAAC,GAAGR,CAAC,CAACU,aAAa,CAACF,CAAC,GAAG,CAAC,CAAC,GAAGR,CAAC,CAACU,aAAa,CAAC,CAAC,EAAEC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;EAC9F,IAAIF,CAAC;IAAEG,WAAW,GAAGZ,CAAC,CAACa,KAAK,CAAC,CAAC,EAAEJ,CAAC,CAAC;;EAElC;EACA;EACA,OAAO,CACLG,WAAW,CAACE,MAAM,GAAG,CAAC,GAAGF,WAAW,CAAC,CAAC,CAAC,GAAGA,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,GAAGD,WAAW,EAC5E,CAACZ,CAAC,CAACa,KAAK,CAACJ,CAAC,GAAG,CAAC,CAAC,CAChB;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}