// TypeScript types for the backend

// API response types

// Standard API response format
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

// Error response format
export interface ApiError {
  success: false;
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// Data types

// Customer type data
export interface CustomerType {
  id: string;
  type: string;
  count: number;
  percentage: number;
  description?: string;
}

// Account industry data
export interface AccountIndustry {
  id: string;
  industry: string;
  count: number;
  percentage: number;
  revenue?: number;
  description?: string;
}

/**
 * Team data structure
 * Represents team information and metrics
 */
export interface Team {
  id: string;
  name: string;
  memberCount: number;
  performance: number;
  department: string;
  lead?: string;
}

/**
 * ACV (Annual Contract Value) Range data structure
 * Represents different contract value ranges and their distribution
 */
export interface ACVRange {
  id: string;
  range: string;
  minValue: number;
  maxValue: number;
  count: number;
  percentage: number;
  totalValue: number;
}

// Dashboard data types

// Main dashboard data structure
export interface DashboardData {
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  summary: DashboardSummary;
}

/**
 * Dashboard summary statistics
 * Provides high-level metrics for the dashboard
 */
export interface DashboardSummary {
  totalCustomers: number;
  totalRevenue: number;
  totalTeams: number;
  averageACV: number;
  lastUpdated: string;
}

// Chart data types

// Chart data point
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
}

/**
 * Bar chart specific data structure
 */
export interface BarChartData {
  categories: string[];
  values: number[];
  colors?: string[];
}

/**
 * Doughnut chart specific data structure
 */
export interface DoughnutChartData {
  segments: ChartDataPoint[];
  total: number;
  centerLabel?: string;
}

// Request/Response types

/**
 * Query parameters for data filtering
 */
export interface DataQueryParams {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: string;
}

/**
 * Pagination metadata for API responses
 */
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta;
}
