/**
 * Global type definitions for SkyGeni Dashboard Backend
 * Contains all TypeScript interfaces and types used across the backend application
 */

// ============================================================================
// API Response Types
// ============================================================================

/**
 * Standard API response wrapper for all endpoints
 * Provides consistent response structure across the application
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

/**
 * Error response structure for API endpoints
 */
export interface ApiError {
  success: false;
  error: string;
  message: string;
  statusCode: number;
  timestamp: string;
}

// ============================================================================
// Data Structure Types
// ============================================================================

/**
 * Customer Type data structure
 * Represents different types of customers in the system
 */
export interface CustomerType {
  id: string;
  type: string;
  count: number;
  percentage: number;
  description?: string;
}

/**
 * Account Industry data structure
 * Represents different industry categories for accounts
 */
export interface AccountIndustry {
  id: string;
  industry: string;
  count: number;
  percentage: number;
  revenue?: number;
  description?: string;
}

/**
 * Team data structure
 * Represents team information and metrics
 */
export interface Team {
  id: string;
  name: string;
  memberCount: number;
  performance: number;
  department: string;
  lead?: string;
}

/**
 * ACV (Annual Contract Value) Range data structure
 * Represents different contract value ranges and their distribution
 */
export interface ACVRange {
  id: string;
  range: string;
  minValue: number;
  maxValue: number;
  count: number;
  percentage: number;
  totalValue: number;
}

// ============================================================================
// Aggregated Data Types
// ============================================================================

/**
 * Complete dashboard data structure
 * Contains all data needed for the frontend dashboard
 */
export interface DashboardData {
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  summary: DashboardSummary;
}

/**
 * Dashboard summary statistics
 * Provides high-level metrics for the dashboard
 */
export interface DashboardSummary {
  totalCustomers: number;
  totalRevenue: number;
  totalTeams: number;
  averageACV: number;
  lastUpdated: string;
}

// ============================================================================
// Chart Data Types
// ============================================================================

/**
 * Generic chart data point for D3.js visualizations
 */
export interface ChartDataPoint {
  label: string;
  value: number;
  color?: string;
  percentage?: number;
}

/**
 * Bar chart specific data structure
 */
export interface BarChartData {
  categories: string[];
  values: number[];
  colors?: string[];
}

/**
 * Doughnut chart specific data structure
 */
export interface DoughnutChartData {
  segments: ChartDataPoint[];
  total: number;
  centerLabel?: string;
}

// ============================================================================
// Request/Response Types
// ============================================================================

/**
 * Query parameters for data filtering
 */
export interface DataQueryParams {
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filter?: string;
}

/**
 * Pagination metadata for API responses
 */
export interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * Paginated API response
 */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: PaginationMeta;
}
