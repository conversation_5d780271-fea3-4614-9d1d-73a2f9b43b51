{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\common\\\\Shimmer.tsx\";\n/**\n * Shimmer Component for SkyGeni Dashboard\n * \n * A loading shimmer effect component for better UX during data loading\n */\n\nimport React from 'react';\nimport { Box, Skeleton, Card, CardHeader, CardContent } from '@mui/material';\n\n// ============================================================================\n// Types\n// ============================================================================\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// Shimmer Component\n// ============================================================================\n\nconst Shimmer = ({\n  variant = 'rectangular',\n  width = '100%',\n  height = 200,\n  lines = 3,\n  className\n}) => {\n  if (variant === 'card') {\n    return /*#__PURE__*/_jsxDEV(Card, {\n      className: className,\n      sx: {\n        height: '100%',\n        borderRadius: 3,\n        border: '1px solid rgba(0, 0, 0, 0.05)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"text\",\n          width: \"60%\",\n          height: 32\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 18\n        }, this),\n        action: /*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"circular\",\n          width: 24,\n          height: 24\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 19\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          pt: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n          variant: \"rectangular\",\n          width: \"100%\",\n          height: 250,\n          sx: {\n            borderRadius: 2,\n            mb: 2\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"30%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"20%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"25%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Skeleton, {\n            variant: \"text\",\n            width: \"15%\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this);\n  }\n  if (variant === 'chart') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: className,\n      sx: {\n        width,\n        height,\n        p: 2\n      },\n      children: /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"rectangular\",\n        width: \"100%\",\n        height: \"100%\",\n        sx: {\n          borderRadius: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this);\n  }\n  if (variant === 'text') {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      className: className,\n      children: Array.from({\n        length: lines\n      }).map((_, index) => /*#__PURE__*/_jsxDEV(Skeleton, {\n        variant: \"text\",\n        width: index === lines - 1 ? '60%' : '100%',\n        sx: {\n          mb: 0.5\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Skeleton, {\n    className: className,\n    variant: variant,\n    width: width,\n    height: height,\n    sx: {\n      borderRadius: variant === 'rectangular' ? 2 : 0\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_c = Shimmer;\nexport default Shimmer;\nvar _c;\n$RefreshReg$(_c, \"Shimmer\");", "map": {"version": 3, "names": ["React", "Box", "Skeleton", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Shimmer", "variant", "width", "height", "lines", "className", "sx", "borderRadius", "border", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "action", "pt", "mb", "display", "justifyContent", "p", "Array", "from", "length", "map", "_", "index", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/common/Shimmer.tsx"], "sourcesContent": ["/**\n * Shimmer Component for SkyGeni Dashboard\n * \n * A loading shimmer effect component for better UX during data loading\n */\n\nimport React from 'react';\nimport { Box, Skeleton, Card, CardHeader, CardContent } from '@mui/material';\n\n// ============================================================================\n// Types\n// ============================================================================\n\ninterface ShimmerProps {\n  variant?: 'card' | 'chart' | 'text' | 'rectangular' | 'circular';\n  width?: number | string;\n  height?: number | string;\n  lines?: number;\n  className?: string;\n}\n\n// ============================================================================\n// Shimmer Component\n// ============================================================================\n\nconst Shimmer: React.FC<ShimmerProps> = ({\n  variant = 'rectangular',\n  width = '100%',\n  height = 200,\n  lines = 3,\n  className,\n}) => {\n  if (variant === 'card') {\n    return (\n      <Card \n        className={className}\n        sx={{\n          height: '100%',\n          borderRadius: 3,\n          border: '1px solid rgba(0, 0, 0, 0.05)',\n        }}\n      >\n        <CardHeader\n          title={<Skeleton variant=\"text\" width=\"60%\" height={32} />}\n          action={<Skeleton variant=\"circular\" width={24} height={24} />}\n        />\n        <CardContent sx={{ pt: 0 }}>\n          <Skeleton \n            variant=\"rectangular\" \n            width=\"100%\" \n            height={250}\n            sx={{ borderRadius: 2, mb: 2 }}\n          />\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>\n            <Skeleton variant=\"text\" width=\"30%\" />\n            <Skeleton variant=\"text\" width=\"20%\" />\n          </Box>\n          <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>\n            <Skeleton variant=\"text\" width=\"25%\" />\n            <Skeleton variant=\"text\" width=\"15%\" />\n          </Box>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  if (variant === 'chart') {\n    return (\n      <Box className={className} sx={{ width, height, p: 2 }}>\n        <Skeleton \n          variant=\"rectangular\" \n          width=\"100%\" \n          height=\"100%\"\n          sx={{ borderRadius: 2 }}\n        />\n      </Box>\n    );\n  }\n\n  if (variant === 'text') {\n    return (\n      <Box className={className}>\n        {Array.from({ length: lines }).map((_, index) => (\n          <Skeleton \n            key={index}\n            variant=\"text\" \n            width={index === lines - 1 ? '60%' : '100%'}\n            sx={{ mb: 0.5 }}\n          />\n        ))}\n      </Box>\n    );\n  }\n\n  return (\n    <Skeleton \n      className={className}\n      variant={variant as any}\n      width={width}\n      height={height}\n      sx={{ borderRadius: variant === 'rectangular' ? 2 : 0 }}\n    />\n  );\n};\n\nexport default Shimmer;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,UAAU,EAAEC,WAAW,QAAQ,eAAe;;AAE5E;AACA;AACA;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAUA;AACA;AACA;;AAEA,MAAMC,OAA+B,GAAGA,CAAC;EACvCC,OAAO,GAAG,aAAa;EACvBC,KAAK,GAAG,MAAM;EACdC,MAAM,GAAG,GAAG;EACZC,KAAK,GAAG,CAAC;EACTC;AACF,CAAC,KAAK;EACJ,IAAIJ,OAAO,KAAK,MAAM,EAAE;IACtB,oBACEF,OAAA,CAACJ,IAAI;MACHU,SAAS,EAAEA,SAAU;MACrBC,EAAE,EAAE;QACFH,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE,CAAC;QACfC,MAAM,EAAE;MACV,CAAE;MAAAC,QAAA,gBAEFV,OAAA,CAACH,UAAU;QACTc,KAAK,eAAEX,OAAA,CAACL,QAAQ;UAACO,OAAO,EAAC,MAAM;UAACC,KAAK,EAAC,KAAK;UAACC,MAAM,EAAE;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC3DC,MAAM,eAAEhB,OAAA,CAACL,QAAQ;UAACO,OAAO,EAAC,UAAU;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE;QAAG;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChE,CAAC,eACFf,OAAA,CAACF,WAAW;QAACS,EAAE,EAAE;UAAEU,EAAE,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzBV,OAAA,CAACL,QAAQ;UACPO,OAAO,EAAC,aAAa;UACrBC,KAAK,EAAC,MAAM;UACZC,MAAM,EAAE,GAAI;UACZG,EAAE,EAAE;YAAEC,YAAY,EAAE,CAAC;YAAEU,EAAE,EAAE;UAAE;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC,eACFf,OAAA,CAACN,GAAG;UAACa,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE,eAAe;YAAEF,EAAE,EAAE;UAAE,CAAE;UAAAR,QAAA,gBACnEV,OAAA,CAACL,QAAQ;YAACO,OAAO,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCf,OAAA,CAACL,QAAQ;YAACO,OAAO,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eACNf,OAAA,CAACN,GAAG;UAACa,EAAE,EAAE;YAAEY,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAV,QAAA,gBAC5DV,OAAA,CAACL,QAAQ;YAACO,OAAO,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvCf,OAAA,CAACL,QAAQ;YAACO,OAAO,EAAC,MAAM;YAACC,KAAK,EAAC;UAAK;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEX;EAEA,IAAIb,OAAO,KAAK,OAAO,EAAE;IACvB,oBACEF,OAAA,CAACN,GAAG;MAACY,SAAS,EAAEA,SAAU;MAACC,EAAE,EAAE;QAAEJ,KAAK;QAAEC,MAAM;QAAEiB,CAAC,EAAE;MAAE,CAAE;MAAAX,QAAA,eACrDV,OAAA,CAACL,QAAQ;QACPO,OAAO,EAAC,aAAa;QACrBC,KAAK,EAAC,MAAM;QACZC,MAAM,EAAC,MAAM;QACbG,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAE;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,IAAIb,OAAO,KAAK,MAAM,EAAE;IACtB,oBACEF,OAAA,CAACN,GAAG;MAACY,SAAS,EAAEA,SAAU;MAAAI,QAAA,EACvBY,KAAK,CAACC,IAAI,CAAC;QAAEC,MAAM,EAAEnB;MAAM,CAAC,CAAC,CAACoB,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1C3B,OAAA,CAACL,QAAQ;QAEPO,OAAO,EAAC,MAAM;QACdC,KAAK,EAAEwB,KAAK,KAAKtB,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,MAAO;QAC5CE,EAAE,EAAE;UAAEW,EAAE,EAAE;QAAI;MAAE,GAHXS,KAAK;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEf,OAAA,CAACL,QAAQ;IACPW,SAAS,EAAEA,SAAU;IACrBJ,OAAO,EAAEA,OAAe;IACxBC,KAAK,EAAEA,KAAM;IACbC,MAAM,EAAEA,MAAO;IACfG,EAAE,EAAE;MAAEC,YAAY,EAAEN,OAAO,KAAK,aAAa,GAAG,CAAC,GAAG;IAAE;EAAE;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzD,CAAC;AAEN,CAAC;AAACa,EAAA,GA9EI3B,OAA+B;AAgFrC,eAAeA,OAAO;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}