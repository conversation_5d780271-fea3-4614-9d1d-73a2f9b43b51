{"ast": null, "code": "import Quad from \"./quad.js\";\nexport default function (callback) {\n  var quads = [],\n    q,\n    node = this._root,\n    child,\n    x0,\n    y0,\n    x1,\n    y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2,\n        ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}", "map": {"version": 3, "names": ["Quad", "callback", "quads", "q", "node", "_root", "child", "x0", "y0", "x1", "y1", "push", "_x0", "_y0", "_x1", "_y1", "pop", "length", "xm", "ym"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-quadtree/src/visit.js"], "sourcesContent": ["import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,WAAW;AAE5B,eAAe,UAASC,QAAQ,EAAE;EAChC,IAAIC,KAAK,GAAG,EAAE;IAAEC,CAAC;IAAEC,IAAI,GAAG,IAAI,CAACC,KAAK;IAAEC,KAAK;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;IAAEC,EAAE;EAC3D,IAAIN,IAAI,EAAEF,KAAK,CAACS,IAAI,CAAC,IAAIX,IAAI,CAACI,IAAI,EAAE,IAAI,CAACQ,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,EAAE,IAAI,CAACC,GAAG,CAAC,CAAC;EAC5E,OAAOZ,CAAC,GAAGD,KAAK,CAACc,GAAG,CAAC,CAAC,EAAE;IACtB,IAAI,CAACf,QAAQ,CAACG,IAAI,GAAGD,CAAC,CAACC,IAAI,EAAEG,EAAE,GAAGJ,CAAC,CAACI,EAAE,EAAEC,EAAE,GAAGL,CAAC,CAACK,EAAE,EAAEC,EAAE,GAAGN,CAAC,CAACM,EAAE,EAAEC,EAAE,GAAGP,CAAC,CAACO,EAAE,CAAC,IAAIN,IAAI,CAACa,MAAM,EAAE;MACvF,IAAIC,EAAE,GAAG,CAACX,EAAE,GAAGE,EAAE,IAAI,CAAC;QAAEU,EAAE,GAAG,CAACX,EAAE,GAAGE,EAAE,IAAI,CAAC;MAC1C,IAAIJ,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACS,IAAI,CAAC,IAAIX,IAAI,CAACM,KAAK,EAAEY,EAAE,EAAEC,EAAE,EAAEV,EAAE,EAAEC,EAAE,CAAC,CAAC;MAChE,IAAIJ,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACS,IAAI,CAAC,IAAIX,IAAI,CAACM,KAAK,EAAEC,EAAE,EAAEY,EAAE,EAAED,EAAE,EAAER,EAAE,CAAC,CAAC;MAChE,IAAIJ,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACS,IAAI,CAAC,IAAIX,IAAI,CAACM,KAAK,EAAEY,EAAE,EAAEV,EAAE,EAAEC,EAAE,EAAEU,EAAE,CAAC,CAAC;MAChE,IAAIb,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC,EAAEF,KAAK,CAACS,IAAI,CAAC,IAAIX,IAAI,CAACM,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEU,EAAE,EAAEC,EAAE,CAAC,CAAC;IAClE;EACF;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}