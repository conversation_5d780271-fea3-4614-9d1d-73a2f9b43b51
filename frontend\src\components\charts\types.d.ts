/**
 * Chart Component Type Definitions
 * Contains types specific to D3.js chart components
 */

import * as d3 from 'd3';
import { ChartDataPoint, BarChartData, DoughnutChartData } from '../../types';

// ============================================================================
// Base Chart Types
// ============================================================================

/**
 * Base chart configuration
 */
export interface BaseChartConfig {
  width: number;
  height: number;
  margin: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

/**
 * Chart dimensions after applying margins
 */
export interface ChartDimensions {
  width: number;
  height: number;
  innerWidth: number;
  innerHeight: number;
}

/**
 * Chart animation configuration
 */
export interface ChartAnimation {
  duration: number;
  delay?: number;
  easing?: string;
}

/**
 * Chart color scheme
 */
export interface ChartColorScheme {
  primary: string[];
  secondary: string[];
  background: string;
  text: string;
  grid: string;
  axis: string;
}

// ============================================================================
// D3.js Type Definitions
// ============================================================================

/**
 * D3 selection types for different elements
 */
export type D3Selection = d3.Selection<SVGElement, unknown, null, undefined>;
export type D3GSelection = d3.Selection<SVGGElement, unknown, null, undefined>;
export type D3RectSelection = d3.Selection<SVGRectElement, unknown, null, undefined>;
export type D3PathSelection = d3.Selection<SVGPathElement, unknown, null, undefined>;
export type D3TextSelection = d3.Selection<SVGTextElement, unknown, null, undefined>;

/**
 * D3 scale types
 */
export type D3LinearScale = d3.ScaleLinear<number, number>;
export type D3BandScale = d3.ScaleBand<string>;
export type D3OrdinalScale = d3.ScaleOrdinal<string, string>;
export type D3ArcGenerator = d3.Arc<any, d3.DefaultArcObject>;

/**
 * D3 axis types
 */
export type D3Axis = d3.Axis<d3.NumberValue> | d3.Axis<string>;

// ============================================================================
// Bar Chart Types
// ============================================================================

/**
 * Bar chart specific configuration
 */
export interface BarChartConfig extends BaseChartConfig {
  showGrid: boolean;
  showLabels: boolean;
  showTooltip: boolean;
  barPadding: number;
  cornerRadius: number;
  xAxisLabel?: string;
  yAxisLabel?: string;
}

/**
 * Bar chart data point with additional properties
 */
export interface BarChartDataPoint extends ChartDataPoint {
  x: string;
  y: number;
  color: string;
  index: number;
}

/**
 * Bar chart scales
 */
export interface BarChartScales {
  x: D3BandScale;
  y: D3LinearScale;
  color: D3OrdinalScale;
}

/**
 * Bar chart props
 */
export interface BarChartProps {
  data: BarChartData;
  config?: Partial<BarChartConfig>;
  animation?: ChartAnimation;
  colorScheme?: ChartColorScheme;
  onBarClick?: (data: BarChartDataPoint, event: MouseEvent) => void;
  onBarHover?: (data: BarChartDataPoint | null, event: MouseEvent) => void;
  className?: string;
  title?: string;
}

// ============================================================================
// Doughnut Chart Types
// ============================================================================

/**
 * Doughnut chart specific configuration
 */
export interface DoughnutChartConfig extends BaseChartConfig {
  innerRadius: number;
  outerRadius: number;
  padAngle: number;
  cornerRadius: number;
  showLabels: boolean;
  showLegend: boolean;
  showTooltip: boolean;
  centerText?: string;
}

/**
 * Doughnut chart data point with additional properties
 */
export interface DoughnutChartDataPoint extends ChartDataPoint {
  startAngle: number;
  endAngle: number;
  index: number;
  data: ChartDataPoint;
}

/**
 * Doughnut chart scales
 */
export interface DoughnutChartScales {
  color: D3OrdinalScale;
  arc: D3ArcGenerator;
  pie: d3.Pie<any, ChartDataPoint>;
}

/**
 * Doughnut chart props
 */
export interface DoughnutChartProps {
  data: DoughnutChartData;
  config?: Partial<DoughnutChartConfig>;
  animation?: ChartAnimation;
  colorScheme?: ChartColorScheme;
  onSegmentClick?: (data: DoughnutChartDataPoint, event: MouseEvent) => void;
  onSegmentHover?: (data: DoughnutChartDataPoint | null, event: MouseEvent) => void;
  className?: string;
  title?: string;
}

// ============================================================================
// Tooltip Types
// ============================================================================

/**
 * Tooltip configuration
 */
export interface TooltipConfig {
  show: boolean;
  followCursor: boolean;
  offset: { x: number; y: number };
  className?: string;
}

/**
 * Tooltip data for different chart types
 */
export interface TooltipData {
  title: string;
  value: string | number;
  percentage?: number;
  color?: string;
  additionalInfo?: Record<string, any>;
}

/**
 * Tooltip props
 */
export interface TooltipProps {
  data: TooltipData | null;
  position: { x: number; y: number };
  visible: boolean;
  config?: TooltipConfig;
}

// ============================================================================
// Legend Types
// ============================================================================

/**
 * Legend configuration
 */
export interface LegendConfig {
  show: boolean;
  position: 'top' | 'bottom' | 'left' | 'right';
  orientation: 'horizontal' | 'vertical';
  itemSpacing: number;
  symbolSize: number;
  maxItems?: number;
}

/**
 * Legend item
 */
export interface LegendItem {
  label: string;
  color: string;
  value?: number;
  percentage?: number;
  visible: boolean;
}

/**
 * Legend props
 */
export interface LegendProps {
  items: LegendItem[];
  config?: LegendConfig;
  onItemClick?: (item: LegendItem, index: number) => void;
  onItemHover?: (item: LegendItem | null, index: number) => void;
  className?: string;
}

// ============================================================================
// Chart Container Types
// ============================================================================

/**
 * Chart container props
 */
export interface ChartContainerProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  loading?: boolean;
  error?: string;
  className?: string;
  actions?: React.ReactNode;
}

/**
 * Responsive chart wrapper props
 */
export interface ResponsiveChartProps {
  children: (dimensions: ChartDimensions) => React.ReactNode;
  aspectRatio?: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
  className?: string;
}

// ============================================================================
// Chart Utilities Types
// ============================================================================

/**
 * Chart data transformer function type
 */
export type ChartDataTransformer<T, R> = (data: T) => R;

/**
 * Chart event handler types
 */
export type ChartEventHandler<T> = (data: T, event: MouseEvent) => void;
export type ChartHoverHandler<T> = (data: T | null, event: MouseEvent) => void;

/**
 * Chart theme
 */
export interface ChartTheme {
  colors: ChartColorScheme;
  fonts: {
    family: string;
    size: {
      small: number;
      medium: number;
      large: number;
    };
  };
  spacing: {
    small: number;
    medium: number;
    large: number;
  };
  borderRadius: number;
  shadows: {
    light: string;
    medium: string;
    heavy: string;
  };
}

// ============================================================================
// Export all types
// ============================================================================

export type {
  ChartDataPoint,
  BarChartData,
  DoughnutChartData
} from '../../types';
