/**
 * Redux Slice Type Definitions
 * Contains types specific to Redux slices and state management
 */

import {
  DashboardData,
  CustomerType,
  AccountIndustry,
  Team,
  ACVRange,
  LoadingState
} from '../../types';

// ============================================================================
// Data Slice Types
// ============================================================================

/**
 * Data slice state interface
 */
export interface DataState {
  // Main dashboard data
  dashboardData: DashboardData | null;
  
  // Individual data arrays (for easier access)
  customerTypes: CustomerType[];
  accountIndustries: AccountIndustry[];
  teams: Team[];
  acvRanges: ACVRange[];
  
  // Loading and error states
  loading: LoadingState;
  error: string | null;
  
  // Metadata
  lastFetched: string | null;
  cacheExpiry: string | null;
  
  // UI state
  selectedDataType: DataType | null;
  filters: DataFilters;
}

/**
 * Data type enumeration for filtering and selection
 */
export type DataType = 'customerTypes' | 'accountIndustries' | 'teams' | 'acvRanges';

/**
 * Data filters interface
 */
export interface DataFilters {
  searchTerm: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  showEmpty: boolean;
}

// ============================================================================
// Action Payload Types
// ============================================================================

/**
 * Fetch dashboard data action payload
 */
export interface FetchDashboardDataPayload {
  forceRefresh?: boolean;
  cacheTimeout?: number;
}

/**
 * Set data filter action payload
 */
export interface SetDataFilterPayload {
  filterType: keyof DataFilters;
  value: any;
}

/**
 * Update individual data action payload
 */
export interface UpdateIndividualDataPayload {
  dataType: DataType;
  data: CustomerType[] | AccountIndustry[] | Team[] | ACVRange[];
}

// ============================================================================
// Async Thunk Types
// ============================================================================

/**
 * Thunk API configuration for data slice
 */
export interface DataThunkAPI {
  state: { data: DataState };
  rejectValue: string;
}

/**
 * Fetch data options
 */
export interface FetchDataOptions {
  useCache?: boolean;
  timeout?: number;
  retries?: number;
}

// ============================================================================
// Selector Types
// ============================================================================

/**
 * Memoized selector return types
 */
export interface DataSelectors {
  selectDashboardData: (state: { data: DataState }) => DashboardData | null;
  selectCustomerTypes: (state: { data: DataState }) => CustomerType[];
  selectAccountIndustries: (state: { data: DataState }) => AccountIndustry[];
  selectTeams: (state: { data: DataState }) => Team[];
  selectACVRanges: (state: { data: DataState }) => ACVRange[];
  selectLoading: (state: { data: DataState }) => LoadingState;
  selectError: (state: { data: DataState }) => string | null;
  selectIsLoading: (state: { data: DataState }) => boolean;
  selectIsError: (state: { data: DataState }) => boolean;
  selectIsSuccess: (state: { data: DataState }) => boolean;
  selectLastFetched: (state: { data: DataState }) => string | null;
  selectFilters: (state: { data: DataState }) => DataFilters;
  selectFilteredData: (dataType: DataType) => (state: { data: DataState }) => any[];
}

// ============================================================================
// Cache Types
// ============================================================================

/**
 * Cache configuration
 */
export interface CacheConfig {
  enabled: boolean;
  timeout: number; // in milliseconds
  maxAge: number; // in milliseconds
}

/**
 * Cache metadata
 */
export interface CacheMetadata {
  timestamp: string;
  expiresAt: string;
  version: string;
}

// ============================================================================
// Error Types
// ============================================================================

/**
 * Data slice specific error types
 */
export type DataError = 
  | 'FETCH_FAILED'
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'PARSE_ERROR'
  | 'VALIDATION_ERROR'
  | 'CACHE_ERROR'
  | 'UNKNOWN_ERROR';

/**
 * Error details interface
 */
export interface ErrorDetails {
  type: DataError;
  message: string;
  timestamp: string;
  retryable: boolean;
  details?: any;
}

// ============================================================================
// Performance Types
// ============================================================================

/**
 * Performance metrics for data operations
 */
export interface PerformanceMetrics {
  fetchDuration: number;
  cacheHitRate: number;
  errorRate: number;
  lastFetchTime: number;
}

// ============================================================================
// Utility Types
// ============================================================================

/**
 * Data transformation options
 */
export interface DataTransformOptions {
  normalize: boolean;
  sort: boolean;
  filter: boolean;
  validate: boolean;
}

/**
 * Data validation result
 */
export interface DataValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// ============================================================================
// Export all types
// ============================================================================

export type {
  LoadingState,
  DashboardData,
  CustomerType,
  AccountIndustry,
  Team,
  ACVRange
} from '../../types';
