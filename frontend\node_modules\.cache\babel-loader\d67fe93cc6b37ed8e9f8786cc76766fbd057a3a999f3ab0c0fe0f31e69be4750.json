{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\charts\\\\BarChart.tsx\",\n  _s = $RefreshSig$();\n/**\n * BarChart Component for SkyGeni Dashboard\n * \n * A responsive D3.js-powered bar chart component with:\n * - Interactive hover effects\n * - Smooth animations\n * - Customizable styling\n * - Tooltip support\n * - Responsive design\n * - TypeScript support\n */\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as d3 from 'd3';\nimport { Box, Typography, useTheme } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_CONFIG = {\n  width: 600,\n  height: 400,\n  margin: {\n    top: 20,\n    right: 30,\n    bottom: 60,\n    left: 60\n  },\n  showGrid: true,\n  showLabels: true,\n  showTooltip: true,\n  barPadding: 0.1,\n  cornerRadius: 4,\n  xAxisLabel: '',\n  yAxisLabel: ''\n};\n\n// ============================================================================\n// BarChart Component\n// ============================================================================\n\nconst BarChart = ({\n  data,\n  config = {},\n  animation = {\n    duration: 750,\n    delay: 0\n  },\n  colorScheme,\n  onBarClick,\n  onBarHover,\n  className,\n  title\n}) => {\n  _s();\n  const theme = useTheme();\n  const svgRef = useRef(null);\n  const tooltipRef = useRef(null);\n  const [tooltip, setTooltip] = useState({\n    data: null,\n    position: {\n      x: 0,\n      y: 0\n    },\n    visible: false\n  });\n\n  // Merge default config with provided config\n  const chartConfig = {\n    ...DEFAULT_CONFIG,\n    ...config\n  };\n\n  // Calculate chart dimensions\n  const dimensions = {\n    width: chartConfig.width,\n    height: chartConfig.height,\n    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,\n    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom\n  };\n\n  // Default color scheme\n  const colors = colorScheme || {\n    primary: [theme.palette.primary.main, theme.palette.secondary.main, '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'],\n    secondary: [],\n    background: theme.palette.background.paper,\n    text: theme.palette.text.primary,\n    grid: theme.palette.divider,\n    axis: theme.palette.text.secondary\n  };\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  const processedData = React.useMemo(() => {\n    return data.categories.map((category, index) => {\n      var _data$colors;\n      return {\n        label: category,\n        value: data.values[index] || 0,\n        color: ((_data$colors = data.colors) === null || _data$colors === void 0 ? void 0 : _data$colors[index]) || colors.primary[index % colors.primary.length],\n        x: category,\n        y: data.values[index] || 0,\n        index\n      };\n    });\n  }, [data, colors.primary]);\n\n  // ========================================================================\n  // D3 Scales\n  // ========================================================================\n\n  const scales = React.useMemo(() => {\n    const xScale = d3.scaleBand().domain(processedData.map(d => d.x)).range([0, dimensions.innerWidth]).padding(chartConfig.barPadding);\n    const yScale = d3.scaleLinear().domain([0, d3.max(processedData, d => d.y) || 0]).nice().range([dimensions.innerHeight, 0]);\n    const colorScale = d3.scaleOrdinal().domain(processedData.map(d => d.x)).range(colors.primary);\n    return {\n      x: xScale,\n      y: yScale,\n      color: colorScale\n    };\n  }, [processedData, dimensions, chartConfig.barPadding, colors.primary]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleBarMouseEnter = useCallback((event, d) => {\n    var _svgRef$current;\n    // Prevent event bubbling to avoid loops\n    event.stopPropagation();\n    const rect = (_svgRef$current = svgRef.current) === null || _svgRef$current === void 0 ? void 0 : _svgRef$current.getBoundingClientRect();\n    if (!rect) return;\n\n    // Use requestAnimationFrame to prevent rapid state updates\n    requestAnimationFrame(() => {\n      const tooltipData = {\n        title: d.label,\n        value: d.value,\n        color: d.color\n      };\n      setTooltip({\n        data: tooltipData,\n        position: {\n          x: event.clientX - rect.left,\n          y: event.clientY - rect.top\n        },\n        visible: true\n      });\n    });\n    onBarHover === null || onBarHover === void 0 ? void 0 : onBarHover(d, event);\n  }, [onBarHover]);\n  const handleBarMouseLeave = useCallback(event => {\n    // Prevent event bubbling\n    event.stopPropagation();\n\n    // Use requestAnimationFrame to prevent rapid state updates\n    requestAnimationFrame(() => {\n      setTooltip(prev => ({\n        ...prev,\n        visible: false\n      }));\n    });\n    onBarHover === null || onBarHover === void 0 ? void 0 : onBarHover(null, event);\n  }, [onBarHover]);\n  const handleBarClick = useCallback((event, d) => {\n    onBarClick === null || onBarClick === void 0 ? void 0 : onBarClick(d, event);\n  }, [onBarClick]);\n\n  // ========================================================================\n  // Chart Rendering\n  // ========================================================================\n\n  useEffect(() => {\n    if (!svgRef.current || processedData.length === 0) return;\n    const svg = d3.select(svgRef.current);\n\n    // Clear previous content\n    svg.selectAll('*').remove();\n\n    // Create main group with margins\n    const g = svg.append('g').attr('transform', `translate(${chartConfig.margin.left},${chartConfig.margin.top})`);\n\n    // Add grid lines if enabled\n    if (chartConfig.showGrid) {\n      // Horizontal grid lines\n      g.append('g').attr('class', 'grid').selectAll('line').data(scales.y.ticks()).enter().append('line').attr('x1', 0).attr('x2', dimensions.innerWidth).attr('y1', d => scales.y(d)).attr('y2', d => scales.y(d)).attr('stroke', colors.grid).attr('stroke-width', 1).attr('opacity', 0.3);\n    }\n\n    // Add X axis\n    g.append('g').attr('class', 'x-axis').attr('transform', `translate(0,${dimensions.innerHeight})`).call(d3.axisBottom(scales.x)).selectAll('text').style('fill', colors.axis).style('font-size', '12px');\n\n    // Add Y axis\n    g.append('g').attr('class', 'y-axis').call(d3.axisLeft(scales.y)).selectAll('text').style('fill', colors.axis).style('font-size', '12px');\n\n    // Add axis labels\n    if (chartConfig.xAxisLabel) {\n      g.append('text').attr('class', 'x-axis-label').attr('text-anchor', 'middle').attr('x', dimensions.innerWidth / 2).attr('y', dimensions.innerHeight + 40).style('fill', colors.text).style('font-size', '14px').text(chartConfig.xAxisLabel);\n    }\n    if (chartConfig.yAxisLabel) {\n      g.append('text').attr('class', 'y-axis-label').attr('text-anchor', 'middle').attr('transform', 'rotate(-90)').attr('x', -dimensions.innerHeight / 2).attr('y', -40).style('fill', colors.text).style('font-size', '14px').text(chartConfig.yAxisLabel);\n    }\n\n    // Add bars\n    const bars = g.selectAll('.bar').data(processedData).enter().append('rect').attr('class', 'bar').attr('x', d => scales.x(d.x) || 0).attr('width', scales.x.bandwidth()).attr('y', dimensions.innerHeight) // Start from bottom for animation\n    .attr('height', 0) // Start with 0 height for animation\n    .attr('fill', d => d.color).attr('rx', chartConfig.cornerRadius).attr('ry', chartConfig.cornerRadius).style('cursor', 'pointer').on('mouseenter', function (event, d) {\n      // Highlight effect\n      d3.select(this).transition().duration(150).attr('opacity', 0.8);\n      handleBarMouseEnter(event, d);\n    }).on('mouseleave', function (event, d) {\n      // Remove highlight\n      d3.select(this).transition().duration(150).attr('opacity', 1);\n      handleBarMouseLeave(event);\n    }).on('click', handleBarClick);\n\n    // Animate bars\n    bars.transition().duration(animation.duration).delay((d, i) => (animation.delay || 0) + i * 50).ease(d3.easeBackOut.overshoot(1.1)).attr('y', d => scales.y(d.y)).attr('height', d => dimensions.innerHeight - scales.y(d.y));\n\n    // Add value labels if enabled\n    if (chartConfig.showLabels) {\n      const labels = g.selectAll('.bar-label').data(processedData).enter().append('text').attr('class', 'bar-label').attr('text-anchor', 'middle').attr('x', d => (scales.x(d.x) || 0) + scales.x.bandwidth() / 2).attr('y', dimensions.innerHeight) // Start from bottom\n      .style('fill', colors.text).style('font-size', '12px').style('font-weight', 'bold').style('opacity', 0).text(d => d.value.toLocaleString());\n\n      // Animate labels\n      labels.transition().duration(animation.duration).delay((d, i) => (animation.delay || 0) + i * 50 + 200).attr('y', d => scales.y(d.y) - 5).style('opacity', 1);\n    }\n  }, [processedData, scales, dimensions, chartConfig, colors, animation, handleBarMouseEnter, handleBarMouseLeave, handleBarClick]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: className,\n    sx: {\n      position: 'relative',\n      width: '100%'\n    },\n    children: [title && /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      component: \"h3\",\n      gutterBottom: true,\n      align: \"center\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'relative',\n        display: 'inline-block'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        ref: svgRef,\n        width: dimensions.width,\n        height: dimensions.height,\n        style: {\n          background: colors.background,\n          borderRadius: theme.shape.borderRadius,\n          boxShadow: theme.shadows[1]\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), tooltip.visible && tooltip.data && /*#__PURE__*/_jsxDEV(Box, {\n        ref: tooltipRef,\n        sx: {\n          position: 'absolute',\n          left: tooltip.position.x + 10,\n          top: tooltip.position.y - 10,\n          background: theme.palette.background.paper,\n          border: `1px solid ${theme.palette.divider}`,\n          borderRadius: 1,\n          padding: 1,\n          boxShadow: theme.shadows[3],\n          pointerEvents: 'none',\n          zIndex: 1000,\n          fontSize: '0.875rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          fontWeight: \"bold\",\n          children: tooltip.data.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          children: [\"Value: \", tooltip.data.value.toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 348,\n    columnNumber: 5\n  }, this);\n};\n_s(BarChart, \"l1M0P0eGYafq3fUh+rS5R2FIb54=\", false, function () {\n  return [useTheme];\n});\n_c = BarChart;\nexport default BarChart;\nvar _c;\n$RefreshReg$(_c, \"BarChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "useCallback", "d3", "Box", "Typography", "useTheme", "jsxDEV", "_jsxDEV", "DEFAULT_CONFIG", "width", "height", "margin", "top", "right", "bottom", "left", "showGrid", "showLabels", "showTooltip", "barPadding", "cornerRadius", "xAxisLabel", "yAxisLabel", "<PERSON><PERSON><PERSON>", "data", "config", "animation", "duration", "delay", "colorScheme", "onBarClick", "onBarHover", "className", "title", "_s", "theme", "svgRef", "tooltipRef", "tooltip", "setTooltip", "position", "x", "y", "visible", "chartConfig", "dimensions", "innerWidth", "innerHeight", "colors", "primary", "palette", "main", "secondary", "background", "paper", "text", "grid", "divider", "axis", "processedData", "useMemo", "categories", "map", "category", "index", "_data$colors", "label", "value", "values", "color", "length", "scales", "xScale", "scaleBand", "domain", "d", "range", "padding", "yScale", "scaleLinear", "max", "nice", "colorScale", "scaleOrdinal", "handleBarMouseEnter", "event", "_svgRef$current", "stopPropagation", "rect", "current", "getBoundingClientRect", "requestAnimationFrame", "tooltipData", "clientX", "clientY", "handleBarMouseLeave", "prev", "handleBarClick", "svg", "select", "selectAll", "remove", "g", "append", "attr", "ticks", "enter", "call", "axisBottom", "style", "axisLeft", "bars", "bandwidth", "on", "transition", "i", "ease", "easeBackOut", "overshoot", "labels", "toLocaleString", "sx", "children", "variant", "component", "gutterBottom", "align", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "ref", "borderRadius", "shape", "boxShadow", "shadows", "border", "pointerEvents", "zIndex", "fontSize", "fontWeight", "_c", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/charts/BarChart.tsx"], "sourcesContent": ["/**\n * BarChart Component for SkyGeni Dashboard\n * \n * A responsive D3.js-powered bar chart component with:\n * - Interactive hover effects\n * - Smooth animations\n * - Customizable styling\n * - Tooltip support\n * - Responsive design\n * - TypeScript support\n */\n\nimport React, { useEffect, useRef, useState, useCallback } from 'react';\nimport * as d3 from 'd3';\nimport { Box, Typography, useTheme } from '@mui/material';\nimport {\n  BarChartProps,\n  BarChartConfig,\n  BarChartDataPoint,\n  BarChartScales,\n  ChartDimensions,\n  TooltipData,\n  D3Selection,\n  D3GSelection\n} from './types';\n\n// ============================================================================\n// Default Configuration\n// ============================================================================\n\nconst DEFAULT_CONFIG: BarChartConfig = {\n  width: 600,\n  height: 400,\n  margin: { top: 20, right: 30, bottom: 60, left: 60 },\n  showGrid: true,\n  showLabels: true,\n  showTooltip: true,\n  barPadding: 0.1,\n  cornerRadius: 4,\n  xAxisLabel: '',\n  yAxisLabel: '',\n};\n\n// ============================================================================\n// BarChart Component\n// ============================================================================\n\nconst BarChart: React.FC<BarChartProps> = ({\n  data,\n  config = {},\n  animation = { duration: 750, delay: 0 },\n  colorScheme,\n  onBarClick,\n  onBarHover,\n  className,\n  title,\n}) => {\n  const theme = useTheme();\n  const svgRef = useRef<SVGSVGElement>(null);\n  const tooltipRef = useRef<HTMLDivElement>(null);\n  const [tooltip, setTooltip] = useState<{\n    data: TooltipData | null;\n    position: { x: number; y: number };\n    visible: boolean;\n  }>({\n    data: null,\n    position: { x: 0, y: 0 },\n    visible: false,\n  });\n\n  // Merge default config with provided config\n  const chartConfig: BarChartConfig = { ...DEFAULT_CONFIG, ...config };\n\n  // Calculate chart dimensions\n  const dimensions: ChartDimensions = {\n    width: chartConfig.width,\n    height: chartConfig.height,\n    innerWidth: chartConfig.width - chartConfig.margin.left - chartConfig.margin.right,\n    innerHeight: chartConfig.height - chartConfig.margin.top - chartConfig.margin.bottom,\n  };\n\n  // Default color scheme\n  const colors = colorScheme || {\n    primary: [\n      theme.palette.primary.main,\n      theme.palette.secondary.main,\n      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD',\n      '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'\n    ],\n    secondary: [],\n    background: theme.palette.background.paper,\n    text: theme.palette.text.primary,\n    grid: theme.palette.divider,\n    axis: theme.palette.text.secondary,\n  };\n\n  // ========================================================================\n  // Data Processing\n  // ========================================================================\n\n  const processedData: BarChartDataPoint[] = React.useMemo(() => {\n    return data.categories.map((category, index) => ({\n      label: category,\n      value: data.values[index] || 0,\n      color: data.colors?.[index] || colors.primary[index % colors.primary.length],\n      x: category,\n      y: data.values[index] || 0,\n      index,\n    }));\n  }, [data, colors.primary]);\n\n  // ========================================================================\n  // D3 Scales\n  // ========================================================================\n\n  const scales: BarChartScales = React.useMemo(() => {\n    const xScale = d3\n      .scaleBand()\n      .domain(processedData.map(d => d.x))\n      .range([0, dimensions.innerWidth])\n      .padding(chartConfig.barPadding);\n\n    const yScale = d3\n      .scaleLinear()\n      .domain([0, d3.max(processedData, d => d.y) || 0])\n      .nice()\n      .range([dimensions.innerHeight, 0]);\n\n    const colorScale = d3\n      .scaleOrdinal<string>()\n      .domain(processedData.map(d => d.x))\n      .range(colors.primary);\n\n    return { x: xScale, y: yScale, color: colorScale };\n  }, [processedData, dimensions, chartConfig.barPadding, colors.primary]);\n\n  // ========================================================================\n  // Event Handlers\n  // ========================================================================\n\n  const handleBarMouseEnter = useCallback((event: MouseEvent, d: BarChartDataPoint) => {\n    // Prevent event bubbling to avoid loops\n    event.stopPropagation();\n\n    const rect = svgRef.current?.getBoundingClientRect();\n    if (!rect) return;\n\n    // Use requestAnimationFrame to prevent rapid state updates\n    requestAnimationFrame(() => {\n      const tooltipData: TooltipData = {\n        title: d.label,\n        value: d.value,\n        color: d.color,\n      };\n\n      setTooltip({\n        data: tooltipData,\n        position: {\n          x: event.clientX - rect.left,\n          y: event.clientY - rect.top,\n        },\n        visible: true,\n      });\n    });\n\n    onBarHover?.(d, event);\n  }, [onBarHover]);\n\n  const handleBarMouseLeave = useCallback((event: MouseEvent) => {\n    // Prevent event bubbling\n    event.stopPropagation();\n\n    // Use requestAnimationFrame to prevent rapid state updates\n    requestAnimationFrame(() => {\n      setTooltip(prev => ({ ...prev, visible: false }));\n    });\n\n    onBarHover?.(null, event);\n  }, [onBarHover]);\n\n  const handleBarClick = useCallback((event: MouseEvent, d: BarChartDataPoint) => {\n    onBarClick?.(d, event);\n  }, [onBarClick]);\n\n  // ========================================================================\n  // Chart Rendering\n  // ========================================================================\n\n  useEffect(() => {\n    if (!svgRef.current || processedData.length === 0) return;\n\n    const svg = d3.select(svgRef.current);\n    \n    // Clear previous content\n    svg.selectAll('*').remove();\n\n    // Create main group with margins\n    const g = svg\n      .append('g')\n      .attr('transform', `translate(${chartConfig.margin.left},${chartConfig.margin.top})`);\n\n    // Add grid lines if enabled\n    if (chartConfig.showGrid) {\n      // Horizontal grid lines\n      g.append('g')\n        .attr('class', 'grid')\n        .selectAll('line')\n        .data(scales.y.ticks())\n        .enter()\n        .append('line')\n        .attr('x1', 0)\n        .attr('x2', dimensions.innerWidth)\n        .attr('y1', d => scales.y(d))\n        .attr('y2', d => scales.y(d))\n        .attr('stroke', colors.grid)\n        .attr('stroke-width', 1)\n        .attr('opacity', 0.3);\n    }\n\n    // Add X axis\n    g.append('g')\n      .attr('class', 'x-axis')\n      .attr('transform', `translate(0,${dimensions.innerHeight})`)\n      .call(d3.axisBottom(scales.x))\n      .selectAll('text')\n      .style('fill', colors.axis)\n      .style('font-size', '12px');\n\n    // Add Y axis\n    g.append('g')\n      .attr('class', 'y-axis')\n      .call(d3.axisLeft(scales.y))\n      .selectAll('text')\n      .style('fill', colors.axis)\n      .style('font-size', '12px');\n\n    // Add axis labels\n    if (chartConfig.xAxisLabel) {\n      g.append('text')\n        .attr('class', 'x-axis-label')\n        .attr('text-anchor', 'middle')\n        .attr('x', dimensions.innerWidth / 2)\n        .attr('y', dimensions.innerHeight + 40)\n        .style('fill', colors.text)\n        .style('font-size', '14px')\n        .text(chartConfig.xAxisLabel);\n    }\n\n    if (chartConfig.yAxisLabel) {\n      g.append('text')\n        .attr('class', 'y-axis-label')\n        .attr('text-anchor', 'middle')\n        .attr('transform', 'rotate(-90)')\n        .attr('x', -dimensions.innerHeight / 2)\n        .attr('y', -40)\n        .style('fill', colors.text)\n        .style('font-size', '14px')\n        .text(chartConfig.yAxisLabel);\n    }\n\n    // Add bars\n    const bars = g\n      .selectAll('.bar')\n      .data(processedData)\n      .enter()\n      .append('rect')\n      .attr('class', 'bar')\n      .attr('x', d => scales.x(d.x) || 0)\n      .attr('width', scales.x.bandwidth())\n      .attr('y', dimensions.innerHeight) // Start from bottom for animation\n      .attr('height', 0) // Start with 0 height for animation\n      .attr('fill', d => d.color)\n      .attr('rx', chartConfig.cornerRadius)\n      .attr('ry', chartConfig.cornerRadius)\n      .style('cursor', 'pointer')\n      .on('mouseenter', function(event, d) {\n        // Highlight effect\n        d3.select(this)\n          .transition()\n          .duration(150)\n          .attr('opacity', 0.8);\n        \n        handleBarMouseEnter(event, d);\n      })\n      .on('mouseleave', function(event, d) {\n        // Remove highlight\n        d3.select(this)\n          .transition()\n          .duration(150)\n          .attr('opacity', 1);\n        \n        handleBarMouseLeave(event);\n      })\n      .on('click', handleBarClick);\n\n    // Animate bars\n    bars\n      .transition()\n      .duration(animation.duration)\n      .delay((d, i) => (animation.delay || 0) + i * 50)\n      .ease(d3.easeBackOut.overshoot(1.1))\n      .attr('y', d => scales.y(d.y))\n      .attr('height', d => dimensions.innerHeight - scales.y(d.y));\n\n    // Add value labels if enabled\n    if (chartConfig.showLabels) {\n      const labels = g\n        .selectAll('.bar-label')\n        .data(processedData)\n        .enter()\n        .append('text')\n        .attr('class', 'bar-label')\n        .attr('text-anchor', 'middle')\n        .attr('x', d => (scales.x(d.x) || 0) + scales.x.bandwidth() / 2)\n        .attr('y', dimensions.innerHeight) // Start from bottom\n        .style('fill', colors.text)\n        .style('font-size', '12px')\n        .style('font-weight', 'bold')\n        .style('opacity', 0)\n        .text(d => d.value.toLocaleString());\n\n      // Animate labels\n      labels\n        .transition()\n        .duration(animation.duration)\n        .delay((d, i) => (animation.delay || 0) + i * 50 + 200)\n        .attr('y', d => scales.y(d.y) - 5)\n        .style('opacity', 1);\n    }\n\n  }, [\n    processedData,\n    scales,\n    dimensions,\n    chartConfig,\n    colors,\n    animation,\n    handleBarMouseEnter,\n    handleBarMouseLeave,\n    handleBarClick,\n  ]);\n\n  // ========================================================================\n  // Render Component\n  // ========================================================================\n\n  return (\n    <Box className={className} sx={{ position: 'relative', width: '100%' }}>\n      {title && (\n        <Typography variant=\"h6\" component=\"h3\" gutterBottom align=\"center\">\n          {title}\n        </Typography>\n      )}\n      \n      <Box sx={{ position: 'relative', display: 'inline-block' }}>\n        <svg\n          ref={svgRef}\n          width={dimensions.width}\n          height={dimensions.height}\n          style={{\n            background: colors.background,\n            borderRadius: theme.shape.borderRadius,\n            boxShadow: theme.shadows[1],\n          }}\n        />\n        \n        {/* Tooltip */}\n        {tooltip.visible && tooltip.data && (\n          <Box\n            ref={tooltipRef}\n            sx={{\n              position: 'absolute',\n              left: tooltip.position.x + 10,\n              top: tooltip.position.y - 10,\n              background: theme.palette.background.paper,\n              border: `1px solid ${theme.palette.divider}`,\n              borderRadius: 1,\n              padding: 1,\n              boxShadow: theme.shadows[3],\n              pointerEvents: 'none',\n              zIndex: 1000,\n              fontSize: '0.875rem',\n            }}\n          >\n            <Typography variant=\"body2\" fontWeight=\"bold\">\n              {tooltip.data.title}\n            </Typography>\n            <Typography variant=\"body2\" color=\"text.secondary\">\n              Value: {tooltip.data.value.toLocaleString()}\n            </Typography>\n          </Box>\n        )}\n      </Box>\n    </Box>\n  );\n};\n\nexport default BarChart;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACvE,OAAO,KAAKC,EAAE,MAAM,IAAI;AACxB,SAASC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY1D;AACA;AACA;;AAEA,MAAMC,cAA8B,GAAG;EACrCC,KAAK,EAAE,GAAG;EACVC,MAAM,EAAE,GAAG;EACXC,MAAM,EAAE;IAAEC,GAAG,EAAE,EAAE;IAAEC,KAAK,EAAE,EAAE;IAAEC,MAAM,EAAE,EAAE;IAAEC,IAAI,EAAE;EAAG,CAAC;EACpDC,QAAQ,EAAE,IAAI;EACdC,UAAU,EAAE,IAAI;EAChBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,GAAG;EACfC,YAAY,EAAE,CAAC;EACfC,UAAU,EAAE,EAAE;EACdC,UAAU,EAAE;AACd,CAAC;;AAED;AACA;AACA;;AAEA,MAAMC,QAAiC,GAAGA,CAAC;EACzCC,IAAI;EACJC,MAAM,GAAG,CAAC,CAAC;EACXC,SAAS,GAAG;IAAEC,QAAQ,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAE,CAAC;EACvCC,WAAW;EACXC,UAAU;EACVC,UAAU;EACVC,SAAS;EACTC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,KAAK,GAAG9B,QAAQ,CAAC,CAAC;EACxB,MAAM+B,MAAM,GAAGrC,MAAM,CAAgB,IAAI,CAAC;EAC1C,MAAMsC,UAAU,GAAGtC,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAInC;IACDwB,IAAI,EAAE,IAAI;IACVgB,QAAQ,EAAE;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;IACxBC,OAAO,EAAE;EACX,CAAC,CAAC;;EAEF;EACA,MAAMC,WAA2B,GAAG;IAAE,GAAGpC,cAAc;IAAE,GAAGiB;EAAO,CAAC;;EAEpE;EACA,MAAMoB,UAA2B,GAAG;IAClCpC,KAAK,EAAEmC,WAAW,CAACnC,KAAK;IACxBC,MAAM,EAAEkC,WAAW,CAAClC,MAAM;IAC1BoC,UAAU,EAAEF,WAAW,CAACnC,KAAK,GAAGmC,WAAW,CAACjC,MAAM,CAACI,IAAI,GAAG6B,WAAW,CAACjC,MAAM,CAACE,KAAK;IAClFkC,WAAW,EAAEH,WAAW,CAAClC,MAAM,GAAGkC,WAAW,CAACjC,MAAM,CAACC,GAAG,GAAGgC,WAAW,CAACjC,MAAM,CAACG;EAChF,CAAC;;EAED;EACA,MAAMkC,MAAM,GAAGnB,WAAW,IAAI;IAC5BoB,OAAO,EAAE,CACPd,KAAK,CAACe,OAAO,CAACD,OAAO,CAACE,IAAI,EAC1BhB,KAAK,CAACe,OAAO,CAACE,SAAS,CAACD,IAAI,EAC5B,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAChE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAC3C;IACDC,SAAS,EAAE,EAAE;IACbC,UAAU,EAAElB,KAAK,CAACe,OAAO,CAACG,UAAU,CAACC,KAAK;IAC1CC,IAAI,EAAEpB,KAAK,CAACe,OAAO,CAACK,IAAI,CAACN,OAAO;IAChCO,IAAI,EAAErB,KAAK,CAACe,OAAO,CAACO,OAAO;IAC3BC,IAAI,EAAEvB,KAAK,CAACe,OAAO,CAACK,IAAI,CAACH;EAC3B,CAAC;;EAED;EACA;EACA;;EAEA,MAAMO,aAAkC,GAAG9D,KAAK,CAAC+D,OAAO,CAAC,MAAM;IAC7D,OAAOpC,IAAI,CAACqC,UAAU,CAACC,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK;MAAA,IAAAC,YAAA;MAAA,OAAM;QAC/CC,KAAK,EAAEH,QAAQ;QACfI,KAAK,EAAE3C,IAAI,CAAC4C,MAAM,CAACJ,KAAK,CAAC,IAAI,CAAC;QAC9BK,KAAK,EAAE,EAAAJ,YAAA,GAAAzC,IAAI,CAACwB,MAAM,cAAAiB,YAAA,uBAAXA,YAAA,CAAcD,KAAK,CAAC,KAAIhB,MAAM,CAACC,OAAO,CAACe,KAAK,GAAGhB,MAAM,CAACC,OAAO,CAACqB,MAAM,CAAC;QAC5E7B,CAAC,EAAEsB,QAAQ;QACXrB,CAAC,EAAElB,IAAI,CAAC4C,MAAM,CAACJ,KAAK,CAAC,IAAI,CAAC;QAC1BA;MACF,CAAC;IAAA,CAAC,CAAC;EACL,CAAC,EAAE,CAACxC,IAAI,EAAEwB,MAAM,CAACC,OAAO,CAAC,CAAC;;EAE1B;EACA;EACA;;EAEA,MAAMsB,MAAsB,GAAG1E,KAAK,CAAC+D,OAAO,CAAC,MAAM;IACjD,MAAMY,MAAM,GAAGtE,EAAE,CACduE,SAAS,CAAC,CAAC,CACXC,MAAM,CAACf,aAAa,CAACG,GAAG,CAACa,CAAC,IAAIA,CAAC,CAAClC,CAAC,CAAC,CAAC,CACnCmC,KAAK,CAAC,CAAC,CAAC,EAAE/B,UAAU,CAACC,UAAU,CAAC,CAAC,CACjC+B,OAAO,CAACjC,WAAW,CAACzB,UAAU,CAAC;IAElC,MAAM2D,MAAM,GAAG5E,EAAE,CACd6E,WAAW,CAAC,CAAC,CACbL,MAAM,CAAC,CAAC,CAAC,EAAExE,EAAE,CAAC8E,GAAG,CAACrB,aAAa,EAAEgB,CAAC,IAAIA,CAAC,CAACjC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CACjDuC,IAAI,CAAC,CAAC,CACNL,KAAK,CAAC,CAAC/B,UAAU,CAACE,WAAW,EAAE,CAAC,CAAC,CAAC;IAErC,MAAMmC,UAAU,GAAGhF,EAAE,CAClBiF,YAAY,CAAS,CAAC,CACtBT,MAAM,CAACf,aAAa,CAACG,GAAG,CAACa,CAAC,IAAIA,CAAC,CAAClC,CAAC,CAAC,CAAC,CACnCmC,KAAK,CAAC5B,MAAM,CAACC,OAAO,CAAC;IAExB,OAAO;MAAER,CAAC,EAAE+B,MAAM;MAAE9B,CAAC,EAAEoC,MAAM;MAAET,KAAK,EAAEa;IAAW,CAAC;EACpD,CAAC,EAAE,CAACvB,aAAa,EAAEd,UAAU,EAAED,WAAW,CAACzB,UAAU,EAAE6B,MAAM,CAACC,OAAO,CAAC,CAAC;;EAEvE;EACA;EACA;;EAEA,MAAMmC,mBAAmB,GAAGnF,WAAW,CAAC,CAACoF,KAAiB,EAAEV,CAAoB,KAAK;IAAA,IAAAW,eAAA;IACnF;IACAD,KAAK,CAACE,eAAe,CAAC,CAAC;IAEvB,MAAMC,IAAI,IAAAF,eAAA,GAAGlD,MAAM,CAACqD,OAAO,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,qBAAqB,CAAC,CAAC;IACpD,IAAI,CAACF,IAAI,EAAE;;IAEX;IACAG,qBAAqB,CAAC,MAAM;MAC1B,MAAMC,WAAwB,GAAG;QAC/B3D,KAAK,EAAE0C,CAAC,CAACT,KAAK;QACdC,KAAK,EAAEQ,CAAC,CAACR,KAAK;QACdE,KAAK,EAAEM,CAAC,CAACN;MACX,CAAC;MAED9B,UAAU,CAAC;QACTf,IAAI,EAAEoE,WAAW;QACjBpD,QAAQ,EAAE;UACRC,CAAC,EAAE4C,KAAK,CAACQ,OAAO,GAAGL,IAAI,CAACzE,IAAI;UAC5B2B,CAAC,EAAE2C,KAAK,CAACS,OAAO,GAAGN,IAAI,CAAC5E;QAC1B,CAAC;QACD+B,OAAO,EAAE;MACX,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFZ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG4C,CAAC,EAAEU,KAAK,CAAC;EACxB,CAAC,EAAE,CAACtD,UAAU,CAAC,CAAC;EAEhB,MAAMgE,mBAAmB,GAAG9F,WAAW,CAAEoF,KAAiB,IAAK;IAC7D;IACAA,KAAK,CAACE,eAAe,CAAC,CAAC;;IAEvB;IACAI,qBAAqB,CAAC,MAAM;MAC1BpD,UAAU,CAACyD,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAErD,OAAO,EAAE;MAAM,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IAEFZ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,IAAI,EAAEsD,KAAK,CAAC;EAC3B,CAAC,EAAE,CAACtD,UAAU,CAAC,CAAC;EAEhB,MAAMkE,cAAc,GAAGhG,WAAW,CAAC,CAACoF,KAAiB,EAAEV,CAAoB,KAAK;IAC9E7C,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG6C,CAAC,EAAEU,KAAK,CAAC;EACxB,CAAC,EAAE,CAACvD,UAAU,CAAC,CAAC;;EAEhB;EACA;EACA;;EAEAhC,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,MAAM,CAACqD,OAAO,IAAI9B,aAAa,CAACW,MAAM,KAAK,CAAC,EAAE;IAEnD,MAAM4B,GAAG,GAAGhG,EAAE,CAACiG,MAAM,CAAC/D,MAAM,CAACqD,OAAO,CAAC;;IAErC;IACAS,GAAG,CAACE,SAAS,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAAC;;IAE3B;IACA,MAAMC,CAAC,GAAGJ,GAAG,CACVK,MAAM,CAAC,GAAG,CAAC,CACXC,IAAI,CAAC,WAAW,EAAE,aAAa5D,WAAW,CAACjC,MAAM,CAACI,IAAI,IAAI6B,WAAW,CAACjC,MAAM,CAACC,GAAG,GAAG,CAAC;;IAEvF;IACA,IAAIgC,WAAW,CAAC5B,QAAQ,EAAE;MACxB;MACAsF,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CACVC,IAAI,CAAC,OAAO,EAAE,MAAM,CAAC,CACrBJ,SAAS,CAAC,MAAM,CAAC,CACjB5E,IAAI,CAAC+C,MAAM,CAAC7B,CAAC,CAAC+D,KAAK,CAAC,CAAC,CAAC,CACtBC,KAAK,CAAC,CAAC,CACPH,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CACbA,IAAI,CAAC,IAAI,EAAE3D,UAAU,CAACC,UAAU,CAAC,CACjC0D,IAAI,CAAC,IAAI,EAAE7B,CAAC,IAAIJ,MAAM,CAAC7B,CAAC,CAACiC,CAAC,CAAC,CAAC,CAC5B6B,IAAI,CAAC,IAAI,EAAE7B,CAAC,IAAIJ,MAAM,CAAC7B,CAAC,CAACiC,CAAC,CAAC,CAAC,CAC5B6B,IAAI,CAAC,QAAQ,EAAExD,MAAM,CAACQ,IAAI,CAAC,CAC3BgD,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC,CACvBA,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC;IACzB;;IAEA;IACAF,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CACVC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CACvBA,IAAI,CAAC,WAAW,EAAE,eAAe3D,UAAU,CAACE,WAAW,GAAG,CAAC,CAC3D4D,IAAI,CAACzG,EAAE,CAAC0G,UAAU,CAACrC,MAAM,CAAC9B,CAAC,CAAC,CAAC,CAC7B2D,SAAS,CAAC,MAAM,CAAC,CACjBS,KAAK,CAAC,MAAM,EAAE7D,MAAM,CAACU,IAAI,CAAC,CAC1BmD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC;;IAE7B;IACAP,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CACVC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CACvBG,IAAI,CAACzG,EAAE,CAAC4G,QAAQ,CAACvC,MAAM,CAAC7B,CAAC,CAAC,CAAC,CAC3B0D,SAAS,CAAC,MAAM,CAAC,CACjBS,KAAK,CAAC,MAAM,EAAE7D,MAAM,CAACU,IAAI,CAAC,CAC1BmD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC;;IAE7B;IACA,IAAIjE,WAAW,CAACvB,UAAU,EAAE;MAC1BiF,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACbC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAC7BA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,GAAG,EAAE3D,UAAU,CAACC,UAAU,GAAG,CAAC,CAAC,CACpC0D,IAAI,CAAC,GAAG,EAAE3D,UAAU,CAACE,WAAW,GAAG,EAAE,CAAC,CACtC8D,KAAK,CAAC,MAAM,EAAE7D,MAAM,CAACO,IAAI,CAAC,CAC1BsD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BtD,IAAI,CAACX,WAAW,CAACvB,UAAU,CAAC;IACjC;IAEA,IAAIuB,WAAW,CAACtB,UAAU,EAAE;MAC1BgF,CAAC,CAACC,MAAM,CAAC,MAAM,CAAC,CACbC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAC7BA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAChCA,IAAI,CAAC,GAAG,EAAE,CAAC3D,UAAU,CAACE,WAAW,GAAG,CAAC,CAAC,CACtCyD,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CACdK,KAAK,CAAC,MAAM,EAAE7D,MAAM,CAACO,IAAI,CAAC,CAC1BsD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BtD,IAAI,CAACX,WAAW,CAACtB,UAAU,CAAC;IACjC;;IAEA;IACA,MAAMyF,IAAI,GAAGT,CAAC,CACXF,SAAS,CAAC,MAAM,CAAC,CACjB5E,IAAI,CAACmC,aAAa,CAAC,CACnB+C,KAAK,CAAC,CAAC,CACPH,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CACpBA,IAAI,CAAC,GAAG,EAAE7B,CAAC,IAAIJ,MAAM,CAAC9B,CAAC,CAACkC,CAAC,CAAClC,CAAC,CAAC,IAAI,CAAC,CAAC,CAClC+D,IAAI,CAAC,OAAO,EAAEjC,MAAM,CAAC9B,CAAC,CAACuE,SAAS,CAAC,CAAC,CAAC,CACnCR,IAAI,CAAC,GAAG,EAAE3D,UAAU,CAACE,WAAW,CAAC,CAAC;IAAA,CAClCyD,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;IAAA,CAClBA,IAAI,CAAC,MAAM,EAAE7B,CAAC,IAAIA,CAAC,CAACN,KAAK,CAAC,CAC1BmC,IAAI,CAAC,IAAI,EAAE5D,WAAW,CAACxB,YAAY,CAAC,CACpCoF,IAAI,CAAC,IAAI,EAAE5D,WAAW,CAACxB,YAAY,CAAC,CACpCyF,KAAK,CAAC,QAAQ,EAAE,SAAS,CAAC,CAC1BI,EAAE,CAAC,YAAY,EAAE,UAAS5B,KAAK,EAAEV,CAAC,EAAE;MACnC;MACAzE,EAAE,CAACiG,MAAM,CAAC,IAAI,CAAC,CACZe,UAAU,CAAC,CAAC,CACZvF,QAAQ,CAAC,GAAG,CAAC,CACb6E,IAAI,CAAC,SAAS,EAAE,GAAG,CAAC;MAEvBpB,mBAAmB,CAACC,KAAK,EAAEV,CAAC,CAAC;IAC/B,CAAC,CAAC,CACDsC,EAAE,CAAC,YAAY,EAAE,UAAS5B,KAAK,EAAEV,CAAC,EAAE;MACnC;MACAzE,EAAE,CAACiG,MAAM,CAAC,IAAI,CAAC,CACZe,UAAU,CAAC,CAAC,CACZvF,QAAQ,CAAC,GAAG,CAAC,CACb6E,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;MAErBT,mBAAmB,CAACV,KAAK,CAAC;IAC5B,CAAC,CAAC,CACD4B,EAAE,CAAC,OAAO,EAAEhB,cAAc,CAAC;;IAE9B;IACAc,IAAI,CACDG,UAAU,CAAC,CAAC,CACZvF,QAAQ,CAACD,SAAS,CAACC,QAAQ,CAAC,CAC5BC,KAAK,CAAC,CAAC+C,CAAC,EAAEwC,CAAC,KAAK,CAACzF,SAAS,CAACE,KAAK,IAAI,CAAC,IAAIuF,CAAC,GAAG,EAAE,CAAC,CAChDC,IAAI,CAAClH,EAAE,CAACmH,WAAW,CAACC,SAAS,CAAC,GAAG,CAAC,CAAC,CACnCd,IAAI,CAAC,GAAG,EAAE7B,CAAC,IAAIJ,MAAM,CAAC7B,CAAC,CAACiC,CAAC,CAACjC,CAAC,CAAC,CAAC,CAC7B8D,IAAI,CAAC,QAAQ,EAAE7B,CAAC,IAAI9B,UAAU,CAACE,WAAW,GAAGwB,MAAM,CAAC7B,CAAC,CAACiC,CAAC,CAACjC,CAAC,CAAC,CAAC;;IAE9D;IACA,IAAIE,WAAW,CAAC3B,UAAU,EAAE;MAC1B,MAAMsG,MAAM,GAAGjB,CAAC,CACbF,SAAS,CAAC,YAAY,CAAC,CACvB5E,IAAI,CAACmC,aAAa,CAAC,CACnB+C,KAAK,CAAC,CAAC,CACPH,MAAM,CAAC,MAAM,CAAC,CACdC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAC1BA,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAC7BA,IAAI,CAAC,GAAG,EAAE7B,CAAC,IAAI,CAACJ,MAAM,CAAC9B,CAAC,CAACkC,CAAC,CAAClC,CAAC,CAAC,IAAI,CAAC,IAAI8B,MAAM,CAAC9B,CAAC,CAACuE,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAC/DR,IAAI,CAAC,GAAG,EAAE3D,UAAU,CAACE,WAAW,CAAC,CAAC;MAAA,CAClC8D,KAAK,CAAC,MAAM,EAAE7D,MAAM,CAACO,IAAI,CAAC,CAC1BsD,KAAK,CAAC,WAAW,EAAE,MAAM,CAAC,CAC1BA,KAAK,CAAC,aAAa,EAAE,MAAM,CAAC,CAC5BA,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC,CACnBtD,IAAI,CAACoB,CAAC,IAAIA,CAAC,CAACR,KAAK,CAACqD,cAAc,CAAC,CAAC,CAAC;;MAEtC;MACAD,MAAM,CACHL,UAAU,CAAC,CAAC,CACZvF,QAAQ,CAACD,SAAS,CAACC,QAAQ,CAAC,CAC5BC,KAAK,CAAC,CAAC+C,CAAC,EAAEwC,CAAC,KAAK,CAACzF,SAAS,CAACE,KAAK,IAAI,CAAC,IAAIuF,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,CACtDX,IAAI,CAAC,GAAG,EAAE7B,CAAC,IAAIJ,MAAM,CAAC7B,CAAC,CAACiC,CAAC,CAACjC,CAAC,CAAC,GAAG,CAAC,CAAC,CACjCmE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IACxB;EAEF,CAAC,EAAE,CACDlD,aAAa,EACbY,MAAM,EACN1B,UAAU,EACVD,WAAW,EACXI,MAAM,EACNtB,SAAS,EACT0D,mBAAmB,EACnBW,mBAAmB,EACnBE,cAAc,CACf,CAAC;;EAEF;EACA;EACA;;EAEA,oBACE1F,OAAA,CAACJ,GAAG;IAAC6B,SAAS,EAAEA,SAAU;IAACyF,EAAE,EAAE;MAAEjF,QAAQ,EAAE,UAAU;MAAE/B,KAAK,EAAE;IAAO,CAAE;IAAAiH,QAAA,GACpEzF,KAAK,iBACJ1B,OAAA,CAACH,UAAU;MAACuH,OAAO,EAAC,IAAI;MAACC,SAAS,EAAC,IAAI;MAACC,YAAY;MAACC,KAAK,EAAC,QAAQ;MAAAJ,QAAA,EAChEzF;IAAK;MAAA8F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACb,eAED3H,OAAA,CAACJ,GAAG;MAACsH,EAAE,EAAE;QAAEjF,QAAQ,EAAE,UAAU;QAAE2F,OAAO,EAAE;MAAe,CAAE;MAAAT,QAAA,gBACzDnH,OAAA;QACE6H,GAAG,EAAEhG,MAAO;QACZ3B,KAAK,EAAEoC,UAAU,CAACpC,KAAM;QACxBC,MAAM,EAAEmC,UAAU,CAACnC,MAAO;QAC1BmG,KAAK,EAAE;UACLxD,UAAU,EAAEL,MAAM,CAACK,UAAU;UAC7BgF,YAAY,EAAElG,KAAK,CAACmG,KAAK,CAACD,YAAY;UACtCE,SAAS,EAAEpG,KAAK,CAACqG,OAAO,CAAC,CAAC;QAC5B;MAAE;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGD5F,OAAO,CAACK,OAAO,IAAIL,OAAO,CAACd,IAAI,iBAC9BjB,OAAA,CAACJ,GAAG;QACFiI,GAAG,EAAE/F,UAAW;QAChBoF,EAAE,EAAE;UACFjF,QAAQ,EAAE,UAAU;UACpBzB,IAAI,EAAEuB,OAAO,CAACE,QAAQ,CAACC,CAAC,GAAG,EAAE;UAC7B7B,GAAG,EAAE0B,OAAO,CAACE,QAAQ,CAACE,CAAC,GAAG,EAAE;UAC5BW,UAAU,EAAElB,KAAK,CAACe,OAAO,CAACG,UAAU,CAACC,KAAK;UAC1CmF,MAAM,EAAE,aAAatG,KAAK,CAACe,OAAO,CAACO,OAAO,EAAE;UAC5C4E,YAAY,EAAE,CAAC;UACfxD,OAAO,EAAE,CAAC;UACV0D,SAAS,EAAEpG,KAAK,CAACqG,OAAO,CAAC,CAAC,CAAC;UAC3BE,aAAa,EAAE,MAAM;UACrBC,MAAM,EAAE,IAAI;UACZC,QAAQ,EAAE;QACZ,CAAE;QAAAlB,QAAA,gBAEFnH,OAAA,CAACH,UAAU;UAACuH,OAAO,EAAC,OAAO;UAACkB,UAAU,EAAC,MAAM;UAAAnB,QAAA,EAC1CpF,OAAO,CAACd,IAAI,CAACS;QAAK;UAAA8F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACb3H,OAAA,CAACH,UAAU;UAACuH,OAAO,EAAC,OAAO;UAACtD,KAAK,EAAC,gBAAgB;UAAAqD,QAAA,GAAC,SAC1C,EAACpF,OAAO,CAACd,IAAI,CAAC2C,KAAK,CAACqD,cAAc,CAAC,CAAC;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChG,EAAA,CA5VIX,QAAiC;EAAA,QAUvBlB,QAAQ;AAAA;AAAAyI,EAAA,GAVlBvH,QAAiC;AA8VvC,eAAeA,QAAQ;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}