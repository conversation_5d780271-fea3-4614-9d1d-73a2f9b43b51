{"ast": null, "code": "export default function () {\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n  return null;\n}", "map": {"version": 3, "names": ["groups", "_groups", "j", "m", "length", "group", "i", "n", "node"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/node_modules/d3-selection/src/selection/node.js"], "sourcesContent": ["export default function() {\n\n  for (var groups = this._groups, j = 0, m = groups.length; j < m; ++j) {\n    for (var group = groups[j], i = 0, n = group.length; i < n; ++i) {\n      var node = group[i];\n      if (node) return node;\n    }\n  }\n\n  return null;\n}\n"], "mappings": "AAAA,eAAe,YAAW;EAExB,KAAK,IAAIA,MAAM,GAAG,IAAI,CAACC,OAAO,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;IACpE,KAAK,IAAIG,KAAK,GAAGL,MAAM,CAACE,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGF,KAAK,CAACD,MAAM,EAAEE,CAAC,GAAGC,CAAC,EAAE,EAAED,CAAC,EAAE;MAC/D,IAAIE,IAAI,GAAGH,KAAK,CAACC,CAAC,CAAC;MACnB,IAAIE,IAAI,EAAE,OAAOA,IAAI;IACvB;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}