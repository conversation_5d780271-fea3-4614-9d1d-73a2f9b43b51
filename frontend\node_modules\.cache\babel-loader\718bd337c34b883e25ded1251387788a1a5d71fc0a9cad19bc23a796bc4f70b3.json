{"ast": null, "code": "var _jsxFileName = \"D:\\\\Company-assignment\\\\skygeni\\\\project-skygeni\\\\frontend\\\\src\\\\components\\\\cards\\\\ACVRangeCard.tsx\",\n  _s = $RefreshSig$();\n/**\n * ACVRangeCard Component for SkyGeni Dashboard\n * Specialized card for ACV range data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useACVRanges } from '../../hooks/useData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ACVRangeCard = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'doughnut'\n}) => {\n  _s();\n  const {\n    acvRanges,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch\n  } = useACVRanges();\n  const data = overrideData || acvRanges;\n  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading === 'pending';\n  const error = overrideError || (isError ? hookError : undefined);\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n    return data.map(acvRange => ({\n      ...acvRange,\n      name: acvRange.range,\n      label: acvRange.range,\n      value: acvRange.count,\n      count: acvRange.count,\n      displayText: `${acvRange.range} (${acvRange.count} contracts)`,\n      valueText: `$${acvRange.totalValue.toLocaleString()}`,\n      percentageText: acvRange.percentage ? `${acvRange.percentage.toFixed(1)}%` : ''\n    }));\n  }, [data]);\n  return /*#__PURE__*/_jsxDEV(DataCard, {\n    title: \"ACV Ranges\",\n    data: processedData,\n    chartType: chartType,\n    loading: loading,\n    error: error,\n    className: className,\n    elevation: elevation\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(ACVRangeCard, \"1rQ1pEnUw0s3malTsXCxqxXiGuo=\", false, function () {\n  return [useACVRanges];\n});\n_c = ACVRangeCard;\nexport const ACVRangeBarCard = props => /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n  ...props,\n  chartType: \"bar\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 71,\n  columnNumber: 3\n}, this);\n_c2 = ACVRangeBarCard;\nexport const ACVRangeDoughnutCard = props => /*#__PURE__*/_jsxDEV(ACVRangeCard, {\n  ...props,\n  chartType: \"doughnut\"\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 75,\n  columnNumber: 3\n}, this);\n_c3 = ACVRangeDoughnutCard;\nexport default ACVRangeCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ACVRangeCard\");\n$RefreshReg$(_c2, \"ACVRangeBarCard\");\n$RefreshReg$(_c3, \"ACVRangeDoughnutCard\");", "map": {"version": 3, "names": ["React", "DataCard", "useACVRanges", "jsxDEV", "_jsxDEV", "ACVRangeCard", "className", "elevation", "data", "overrideData", "loading", "overrideLoading", "error", "overrideError", "chartType", "_s", "acvRanges", "hookLoading", "hookError", "isError", "refetch", "undefined", "processedData", "useMemo", "length", "map", "acvRange", "name", "range", "label", "value", "count", "displayText", "valueText", "totalValue", "toLocaleString", "percentageText", "percentage", "toFixed", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ACVRangeBarCard", "props", "_c2", "ACVRangeDoughnutCard", "_c3", "$RefreshReg$"], "sources": ["D:/Company-assignment/skygeni/project-skygeni/frontend/src/components/cards/ACVRangeCard.tsx"], "sourcesContent": ["/**\n * ACVRangeCard Component for SkyGeni Dashboard\n * Specialized card for ACV range data visualization\n */\n\nimport React from 'react';\nimport DataCard from './DataCard';\nimport { useACVRanges } from '../../hooks/useData';\nimport { ACVRange } from '../../types';\n\ninterface ACVRangeCardProps {\n  className?: string;\n  elevation?: number;\n  data?: ACVRange[];\n  loading?: boolean;\n  error?: string;\n  chartType?: 'bar' | 'doughnut';\n}\n\nconst ACVRangeCard: React.FC<ACVRangeCardProps> = ({\n  className,\n  elevation = 2,\n  data: overrideData,\n  loading: overrideLoading,\n  error: overrideError,\n  chartType = 'doughnut',\n}) => {\n  const {\n    acvRanges,\n    loading: hookLoading,\n    error: hookError,\n    isError,\n    refetch,\n  } = useACVRanges();\n\n  const data = overrideData || acvRanges;\n  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');\n  const error = overrideError || (isError ? hookError : undefined);\n\n  const processedData = React.useMemo(() => {\n    if (!data || data.length === 0) return [];\n\n    return data.map(acvRange => ({\n      ...acvRange,\n      name: acvRange.range,\n      label: acvRange.range,\n      value: acvRange.count,\n      count: acvRange.count,\n      displayText: `${acvRange.range} (${acvRange.count} contracts)`,\n      valueText: `$${acvRange.totalValue.toLocaleString()}`,\n      percentageText: acvRange.percentage \n        ? `${acvRange.percentage.toFixed(1)}%` \n        : '',\n    }));\n  }, [data]);\n\n  return (\n    <DataCard\n      title=\"ACV Ranges\"\n      data={processedData}\n      chartType={chartType}\n      loading={loading}\n      error={error}\n      className={className}\n      elevation={elevation}\n    />\n  );\n};\n\nexport const ACVRangeBarCard: React.FC<Omit<ACVRangeCardProps, 'chartType'>> = (props) => (\n  <ACVRangeCard {...props} chartType=\"bar\" />\n);\n\nexport const ACVRangeDoughnutCard: React.FC<Omit<ACVRangeCardProps, 'chartType'>> = (props) => (\n  <ACVRangeCard {...props} chartType=\"doughnut\" />\n);\n\nexport default ACVRangeCard;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,YAAY;AACjC,SAASC,YAAY,QAAQ,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYnD,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,SAAS;EACTC,SAAS,GAAG,CAAC;EACbC,IAAI,EAAEC,YAAY;EAClBC,OAAO,EAAEC,eAAe;EACxBC,KAAK,EAAEC,aAAa;EACpBC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IACJC,SAAS;IACTN,OAAO,EAAEO,WAAW;IACpBL,KAAK,EAAEM,SAAS;IAChBC,OAAO;IACPC;EACF,CAAC,GAAGlB,YAAY,CAAC,CAAC;EAElB,MAAMM,IAAI,GAAGC,YAAY,IAAIO,SAAS;EACtC,MAAMN,OAAO,GAAGC,eAAe,KAAKU,SAAS,GAAGV,eAAe,GAAIM,WAAW,KAAK,SAAU;EAC7F,MAAML,KAAK,GAAGC,aAAa,KAAKM,OAAO,GAAGD,SAAS,GAAGG,SAAS,CAAC;EAEhE,MAAMC,aAAa,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAM;IACxC,IAAI,CAACf,IAAI,IAAIA,IAAI,CAACgB,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEzC,OAAOhB,IAAI,CAACiB,GAAG,CAACC,QAAQ,KAAK;MAC3B,GAAGA,QAAQ;MACXC,IAAI,EAAED,QAAQ,CAACE,KAAK;MACpBC,KAAK,EAAEH,QAAQ,CAACE,KAAK;MACrBE,KAAK,EAAEJ,QAAQ,CAACK,KAAK;MACrBA,KAAK,EAAEL,QAAQ,CAACK,KAAK;MACrBC,WAAW,EAAE,GAAGN,QAAQ,CAACE,KAAK,KAAKF,QAAQ,CAACK,KAAK,aAAa;MAC9DE,SAAS,EAAE,IAAIP,QAAQ,CAACQ,UAAU,CAACC,cAAc,CAAC,CAAC,EAAE;MACrDC,cAAc,EAAEV,QAAQ,CAACW,UAAU,GAC/B,GAAGX,QAAQ,CAACW,UAAU,CAACC,OAAO,CAAC,CAAC,CAAC,GAAG,GACpC;IACN,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAC9B,IAAI,CAAC,CAAC;EAEV,oBACEJ,OAAA,CAACH,QAAQ;IACPsC,KAAK,EAAC,YAAY;IAClB/B,IAAI,EAAEc,aAAc;IACpBR,SAAS,EAAEA,SAAU;IACrBJ,OAAO,EAAEA,OAAQ;IACjBE,KAAK,EAAEA,KAAM;IACbN,SAAS,EAAEA,SAAU;IACrBC,SAAS,EAAEA;EAAU;IAAAiC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtB,CAAC;AAEN,CAAC;AAAC5B,EAAA,CAhDIV,YAAyC;EAAA,QAczCH,YAAY;AAAA;AAAA0C,EAAA,GAdZvC,YAAyC;AAkD/C,OAAO,MAAMwC,eAA+D,GAAIC,KAAK,iBACnF1C,OAAA,CAACC,YAAY;EAAA,GAAKyC,KAAK;EAAEhC,SAAS,EAAC;AAAK;EAAA0B,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAC3C;AAACI,GAAA,GAFWF,eAA+D;AAI5E,OAAO,MAAMG,oBAAoE,GAAIF,KAAK,iBACxF1C,OAAA,CAACC,YAAY;EAAA,GAAKyC,KAAK;EAAEhC,SAAS,EAAC;AAAU;EAAA0B,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OAAE,CAChD;AAACM,GAAA,GAFWD,oBAAoE;AAIjF,eAAe3C,YAAY;AAAC,IAAAuC,EAAA,EAAAG,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAN,EAAA;AAAAM,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}