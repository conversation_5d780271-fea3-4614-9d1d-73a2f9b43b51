/**
 * CleanHeader Component for SkyGeni Dashboard
 * Professional header with logo, title, and refresh functionality
 */

import React, { useState, useCallback, useMemo } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  IconButton,
  Tooltip,
  Fade,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// ============================================================================
// Types
// ============================================================================

interface CleanHeaderProps {
  title?: string;
  lastUpdated?: Date | string | null;
  isLoading?: boolean;
  onRefresh?: () => void;
  className?: string;
}

// ============================================================================
// CleanHeader Component
// ============================================================================

const CleanHeader: React.FC<CleanHeaderProps> = ({
  title = 'SkyGenI Dashboard',
  lastUpdated,
  isLoading = false,
  onRefresh,
  className,
}) => {
  const theme = useTheme();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleRefresh = useCallback(async () => {
    if (isLoading || isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh?.();
    } finally {
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  }, [isLoading, isRefreshing, onRefresh]);

  // ========================================================================
  // Computed Values
  // ========================================================================

  const formatLastUpdated = useMemo(() => {
    if (!lastUpdated) return 'Never';
    
    const date = lastUpdated instanceof Date ? lastUpdated : new Date(lastUpdated);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  }, [lastUpdated]);

  return (
    <Fade in timeout={800}>
      <AppBar
        position="sticky"
        className={className}
        elevation={2}
        sx={{
          background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
          backdropFilter: 'blur(10px)',
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 64, sm: 70 } }}>
          {/* Left Section - Logo & Title */}
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            {/* Logo */}
            <Box
              sx={{
                width: 40,
                height: 40,
                borderRadius: 2,
                background: `linear-gradient(135deg, ${theme.palette.secondary.main} 0%, ${theme.palette.secondary.dark} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: theme.shadows[3],
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.2rem',
                }}
              >
                S
              </Typography>
            </Box>

            {/* Title */}
            <Typography
              variant="h5"
              component="h1"
              sx={{
                fontWeight: 700,
                color: 'white',
                fontSize: { xs: '1.25rem', sm: '1.5rem' },
                lineHeight: 1.2,
              }}
            >
              {title}
            </Typography>
          </Box>

          {/* Right Section - Last Updated & Refresh */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {/* Last Updated */}
            <Typography
              variant="body2"
              sx={{
                color: alpha(theme.palette.common.white, 0.9),
                fontSize: '0.875rem',
                display: { xs: 'none', md: 'block' },
              }}
            >
              Last Updated: {formatLastUpdated}
            </Typography>

            {/* Refresh Button */}
            <Tooltip title="Refresh Data">
              <IconButton
                onClick={handleRefresh}
                disabled={isLoading || isRefreshing}
                sx={{
                  color: 'white',
                  '&:hover': {
                    backgroundColor: alpha(theme.palette.common.white, 0.1),
                    transform: 'scale(1.1)',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <RefreshIcon
                  sx={{
                    animation: (isLoading || isRefreshing) ? 'spin 1s linear infinite' : 'none',
                    '@keyframes spin': {
                      '0%': { transform: 'rotate(0deg)' },
                      '100%': { transform: 'rotate(360deg)' },
                    },
                  }}
                />
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
    </Fade>
  );
};

export default CleanHeader;
