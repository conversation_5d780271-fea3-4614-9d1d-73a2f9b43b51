// Header bar with title and refresh button

import React, { useState, useCallback, useMemo } from 'react';
import {
  AppBar,
  Toolbar,
  Typography,
  Box,
  IconButton,
  Tooltip,
  Fade,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
} from '@mui/icons-material';



interface CleanHeaderProps {
  title?: string;
  lastUpdated?: Date | string | null;
  isLoading?: boolean;
  onRefresh?: () => void;
  className?: string;
}



const CleanHeader: React.FC<CleanHeaderProps> = ({
  title = 'SkyGenI Dashboard',
  lastUpdated,
  isLoading = false,
  onRefresh,
  className,
}) => {
  const theme = useTheme();
  const [isRefreshing, setIsRefreshing] = useState(false);



  const handleRefresh = useCallback(async () => {
    if (isLoading || isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh?.();
    } finally {
      setTimeout(() => setIsRefreshing(false), 1000);
    }
  }, [isLoading, isRefreshing, onRefresh]);

  // ========================================================================
  // Computed Values
  // ========================================================================

  const formatLastUpdated = useMemo(() => {
    if (!lastUpdated) return 'Never';
    
    const date = lastUpdated instanceof Date ? lastUpdated : new Date(lastUpdated);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  }, [lastUpdated]);

  return (
    <Fade in timeout={800}>
      <AppBar
        position="sticky"
        className={className}
        elevation={1}
        sx={{
          background: `linear-gradient(135deg,
            ${alpha(theme.palette.primary.main, 0.95)} 0%,
            ${alpha(theme.palette.primary.dark, 0.9)} 50%,
            ${alpha(theme.palette.secondary.main, 0.85)} 100%)`,
          backdropFilter: 'blur(20px)',
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: `0 2px 20px ${alpha(theme.palette.primary.main, 0.15)}`,
        }}
      >
        <Toolbar sx={{ minHeight: { xs: 56, sm: 64 }, px: { xs: 2, sm: 3 } }}>
          {/* Left Section - Logo & Title */}
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            {/* Logo */}
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 2,
                background: `linear-gradient(135deg,
                  ${theme.palette.secondary.main} 0%,
                  ${theme.palette.secondary.dark} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mr: 2,
                boxShadow: `0 4px 12px ${alpha(theme.palette.secondary.main, 0.3)}`,
                border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  color: 'white',
                  fontWeight: 'bold',
                  fontSize: '1.1rem',
                }}
              >
                S
              </Typography>
            </Box>

            {/* Title */}
            <Typography
              variant="h6"
              component="h1"
              sx={{
                fontWeight: 600,
                color: 'white',
                fontSize: { xs: '1.1rem', sm: '1.25rem' },
                lineHeight: 1.2,
                textShadow: `0 1px 3px ${alpha(theme.palette.common.black, 0.3)}`,
              }}
            >
              {title}
            </Typography>
          </Box>

          {/* Right Section - Last Updated & Refresh */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            {/* Last Updated */}
            <Box
              sx={{
                display: { xs: 'none', md: 'flex' },
                alignItems: 'center',
                backgroundColor: alpha(theme.palette.common.white, 0.1),
                borderRadius: 2,
                px: 2,
                py: 0.5,
                border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: alpha(theme.palette.common.white, 0.95),
                  fontSize: '0.8rem',
                  fontWeight: 500,
                }}
              >
                Updated: {formatLastUpdated}
              </Typography>
            </Box>

            {/* Refresh Button */}
            <Tooltip title="Refresh Data">
              <span>
                <IconButton
                  onClick={handleRefresh}
                  disabled={isLoading || isRefreshing}
                  sx={{
                    color: 'white',
                    backgroundColor: alpha(theme.palette.common.white, 0.1),
                    border: `1px solid ${alpha(theme.palette.common.white, 0.2)}`,
                    width: 40,
                    height: 40,
                    '&:hover': {
                      backgroundColor: alpha(theme.palette.common.white, 0.2),
                      transform: 'scale(1.05)',
                      boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.2)}`,
                    },
                    '&:disabled': {
                      color: alpha(theme.palette.common.white, 0.5),
                      backgroundColor: alpha(theme.palette.common.white, 0.05),
                    },
                    transition: 'all 0.2s ease-in-out',
                  }}
                >
                  <RefreshIcon
                    sx={{
                      fontSize: '1.2rem',
                      animation: (isLoading || isRefreshing) ? 'spin 1s linear infinite' : 'none',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' },
                      },
                    }}
                  />
                </IconButton>
              </span>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>
    </Fade>
  );
};

export default CleanHeader;
