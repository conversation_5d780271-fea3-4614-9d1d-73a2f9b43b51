{"name": "skygeni-dashboard-frontend", "version": "1.0.0", "description": "React frontend for SkyGeni Dashboard with Material-UI and D3.js visualizations", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/material": "^5.15.1", "@mui/icons-material": "^5.15.1", "@mui/lab": "^5.0.0-alpha.157", "@reduxjs/toolkit": "^2.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "d3": "^7.8.5", "d3-selection": "^3.0.0", "d3-scale": "^4.0.2", "d3-axis": "^3.0.0", "d3-shape": "^3.2.0", "d3-array": "^3.2.4", "d3-color": "^3.1.0", "d3-interpolate": "^3.0.1", "d3-transition": "^3.0.1", "axios": "^1.6.2", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/d3": "^7.4.3", "@types/d3-selection": "^3.0.10", "@types/d3-scale": "^4.0.8", "@types/d3-axis": "^3.0.6", "@types/d3-shape": "^3.1.6", "@types/d3-array": "^3.2.1", "@types/d3-color": "^3.1.3", "@types/d3-interpolate": "^3.0.4", "@types/d3-transition": "^3.0.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.16.4", "@testing-library/user-event": "^13.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "analyze": "npm run build && npx serve -s build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "react-hooks/exhaustive-deps": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}