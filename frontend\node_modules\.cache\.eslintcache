[{"D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx": "1", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx": "2", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts": "3", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx": "4", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts": "5", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts": "6", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx": "7", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx": "8", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx": "9", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts": "10", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx": "11", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx": "12", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts": "13", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx": "14", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx": "15", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx": "16", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx": "17", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx": "18", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx": "19", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx": "20", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Shimmer.tsx": "21", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\CleanApp.tsx": "22", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\CleanDashboard.tsx": "23", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\services\\api.ts": "24", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\CleanHeader.tsx": "25", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\CleanFooter.tsx": "26", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CardChart.tsx": "27", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DonutChart.tsx": "28", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\SimpleBarChart.tsx": "29"}, {"size": 3878, "mtime": 1750858675885, "results": "30", "hashOfConfig": "31"}, {"size": 5966, "mtime": 1750854848856, "results": "32", "hashOfConfig": "31"}, {"size": 6344, "mtime": 1750853835815, "results": "33", "hashOfConfig": "31"}, {"size": 8886, "mtime": 1750856982493, "results": "34", "hashOfConfig": "31"}, {"size": 10953, "mtime": 1750854126811, "results": "35", "hashOfConfig": "31"}, {"size": 10552, "mtime": 1750851483951, "results": "36", "hashOfConfig": "31"}, {"size": 4839, "mtime": 1750851961802, "results": "37", "hashOfConfig": "31"}, {"size": 8087, "mtime": 1750851785500, "results": "38", "hashOfConfig": "31"}, {"size": 6968, "mtime": 1750851753757, "results": "39", "hashOfConfig": "31"}, {"size": 717, "mtime": 1750851887911, "results": "40", "hashOfConfig": "31"}, {"size": 6149, "mtime": 1750851940253, "results": "41", "hashOfConfig": "31"}, {"size": 5226, "mtime": 1750856181796, "results": "42", "hashOfConfig": "31"}, {"size": 8957, "mtime": 1750851397132, "results": "43", "hashOfConfig": "31"}, {"size": 4108, "mtime": 1750853589258, "results": "44", "hashOfConfig": "31"}, {"size": 2080, "mtime": 1750853487580, "results": "45", "hashOfConfig": "31"}, {"size": 8063, "mtime": 1750857146196, "results": "46", "hashOfConfig": "31"}, {"size": 1832, "mtime": 1750853499407, "results": "47", "hashOfConfig": "31"}, {"size": 2024, "mtime": 1750853512008, "results": "48", "hashOfConfig": "31"}, {"size": 11692, "mtime": 1750856936939, "results": "49", "hashOfConfig": "31"}, {"size": 11474, "mtime": 1750856963814, "results": "50", "hashOfConfig": "31"}, {"size": 2807, "mtime": 1750856095665, "results": "51", "hashOfConfig": "31"}, {"size": 2154, "mtime": 1750858633883, "results": "52", "hashOfConfig": "31"}, {"size": 5835, "mtime": 1750858594789, "results": "53", "hashOfConfig": "31"}, {"size": 2398, "mtime": 1750858230788, "results": "54", "hashOfConfig": "31"}, {"size": 5499, "mtime": 1750858495735, "results": "55", "hashOfConfig": "31"}, {"size": 4272, "mtime": 1750858532935, "results": "56", "hashOfConfig": "31"}, {"size": 6716, "mtime": 1750858421304, "results": "57", "hashOfConfig": "31"}, {"size": 5592, "mtime": 1750858258431, "results": "58", "hashOfConfig": "31"}, {"size": 6269, "mtime": 1750858389576, "results": "59", "hashOfConfig": "31"}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qmqg9d", {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts", [], ["147"], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts", ["148", "149", "150", "151", "152", "153"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx", ["154", "155", "156", "157", "158", "159", "160", "161", "162", "163", "164"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts", ["165"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx", ["166"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx", ["167", "168"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx", ["169"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx", ["170"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx", ["171", "172", "173", "174", "175"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx", ["176", "177", "178", "179", "180", "181"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Shimmer.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\CleanApp.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\CleanDashboard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\services\\api.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\CleanHeader.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\CleanFooter.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CardChart.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DonutChart.tsx", ["182", "183"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\SimpleBarChart.tsx", [], [], {"ruleId": "184", "severity": 1, "message": "185", "line": 282, "column": 6, "nodeType": "186", "endLine": 282, "endColumn": 8, "suggestions": "187", "suppressions": "188"}, {"ruleId": "189", "severity": 1, "message": "190", "line": 17, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 17, "endColumn": 14}, {"ruleId": "189", "severity": 1, "message": "193", "line": 22, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 22, "endColumn": 16}, {"ruleId": "189", "severity": 1, "message": "194", "line": 23, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 15}, {"ruleId": "189", "severity": 1, "message": "195", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 18}, {"ruleId": "189", "severity": 1, "message": "196", "line": 25, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 25, "endColumn": 7}, {"ruleId": "189", "severity": 1, "message": "197", "line": 26, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 26, "endColumn": 11}, {"ruleId": "189", "severity": 1, "message": "198", "line": 21, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 21, "endColumn": 7}, {"ruleId": "189", "severity": 1, "message": "199", "line": 28, "column": 18, "nodeType": "191", "messageId": "192", "endLine": 28, "endColumn": 30}, {"ruleId": "189", "severity": 1, "message": "200", "line": 29, "column": 18, "nodeType": "191", "messageId": "192", "endLine": 29, "endColumn": 31}, {"ruleId": "189", "severity": 1, "message": "201", "line": 30, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 30, "endColumn": 27}, {"ruleId": "189", "severity": 1, "message": "202", "line": 31, "column": 11, "nodeType": "191", "messageId": "192", "endLine": 31, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "203", "line": 49, "column": 5, "nodeType": "191", "messageId": "192", "endLine": 49, "endColumn": 16}, {"ruleId": "189", "severity": 1, "message": "204", "line": 70, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 70, "endColumn": 26}, {"ruleId": "189", "severity": 1, "message": "205", "line": 75, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 75, "endColumn": 23}, {"ruleId": "189", "severity": 1, "message": "206", "line": 79, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 79, "endColumn": 19}, {"ruleId": "189", "severity": 1, "message": "207", "line": 87, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 87, "endColumn": 26}, {"ruleId": "189", "severity": 1, "message": "208", "line": 110, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 110, "endColumn": 19}, {"ruleId": "209", "severity": 1, "message": "210", "line": 300, "column": 3, "nodeType": "211", "messageId": "212", "endLine": 300, "endColumn": 20}, {"ruleId": "189", "severity": 1, "message": "213", "line": 33, "column": 5, "nodeType": "191", "messageId": "192", "endLine": 33, "endColumn": 12}, {"ruleId": "189", "severity": 1, "message": "214", "line": 103, "column": 9, "nodeType": "191", "messageId": "192", "endLine": 103, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "216", "line": 270, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 270, "endColumn": 23}, {"ruleId": "189", "severity": 1, "message": "213", "line": 33, "column": 5, "nodeType": "191", "messageId": "192", "endLine": 33, "endColumn": 12}, {"ruleId": "189", "severity": 1, "message": "213", "line": 33, "column": 5, "nodeType": "191", "messageId": "192", "endLine": 33, "endColumn": 12}, {"ruleId": "189", "severity": 1, "message": "219", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 14}, {"ruleId": "189", "severity": 1, "message": "220", "line": 25, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 25, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "221", "line": 74, "column": 9, "nodeType": "222", "endLine": 74, "endColumn": 76}, {"ruleId": "184", "severity": 1, "message": "223", "line": 77, "column": 9, "nodeType": "222", "endLine": 82, "endColumn": 4}, {"ruleId": "184", "severity": 1, "message": "224", "line": 94, "column": 9, "nodeType": "222", "endLine": 106, "endColumn": 4}, {"ruleId": "189", "severity": 1, "message": "219", "line": 23, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 23, "endColumn": 14}, {"ruleId": "189", "severity": 1, "message": "220", "line": 24, "column": 3, "nodeType": "191", "messageId": "192", "endLine": 24, "endColumn": 15}, {"ruleId": "184", "severity": 1, "message": "225", "line": 72, "column": 9, "nodeType": "222", "endLine": 72, "endColumn": 71}, {"ruleId": "184", "severity": 1, "message": "226", "line": 75, "column": 9, "nodeType": "222", "endLine": 80, "endColumn": 4}, {"ruleId": "184", "severity": 1, "message": "227", "line": 75, "column": 9, "nodeType": "222", "endLine": 80, "endColumn": 4}, {"ruleId": "184", "severity": 1, "message": "228", "line": 83, "column": 9, "nodeType": "222", "endLine": 95, "endColumn": 4}, {"ruleId": "189", "severity": 1, "message": "229", "line": 8, "column": 15, "nodeType": "191", "messageId": "192", "endLine": 8, "endColumn": 22}, {"ruleId": "189", "severity": 1, "message": "230", "line": 73, "column": 11, "nodeType": "191", "messageId": "192", "endLine": 73, "endColumn": 17}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dashboardData', 'fetchData', and 'opts.autoFetch'. Either include them or remove the dependency array.", "ArrayExpression", ["231"], ["232"], "@typescript-eslint/no-unused-vars", "'DataFilters' is defined but never used.", "Identifier", "unusedVar", "'DashboardData' is defined but never used.", "'CustomerType' is defined but never used.", "'AccountIndustry' is defined but never used.", "'Team' is defined but never used.", "'ACVRange' is defined but never used.", "'Chip' is defined but never used.", "'DarkModeIcon' is defined but never used.", "'LightModeIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'lastFetched' is assigned a value but never used.", "'handleThemeToggle' is assigned a value but never used.", "'handleSettings' is assigned a value but never used.", "'handleInfo' is assigned a value but never used.", "'formatLastUpdated' is assigned a value but never used.", "'dataStatus' is assigned a value but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'refetch' is assigned a value but never used.", "'summary' is assigned a value but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'D3Selection' is defined but never used.", "'D3GSelection' is defined but never used.", "The 'chartConfig' object makes the dependencies of useEffect Hook (at line 319) change on every render. To fix this, wrap the initialization of 'chartConfig' in its own useMemo() Hook.", "VariableDeclarator", "The 'dimensions' object makes the dependencies of useEffect Hook (at line 319) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'colors' logical expression could make the dependencies of useEffect Hook (at line 319) change on every render. To fix this, wrap the initialization of 'colors' in its own useMemo() Hook.", "The 'chartConfig' object makes the dependencies of useEffect Hook (at line 325) change on every render. To fix this, wrap the initialization of 'chartConfig' in its own useMemo() Hook.", "The 'dimensions' object makes the dependencies of useMemo Hook (at line 135) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'dimensions' object makes the dependencies of useEffect Hook (at line 325) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'colors' logical expression could make the dependencies of useEffect Hook (at line 325) change on every render. To fix this, wrap the initialization of 'colors' in its own useMemo() Hook.", "'Tooltip' is defined but never used.", "'radius' is assigned a value but never used.", {"desc": "233", "fix": "234"}, {"kind": "235", "justification": "236"}, "Update the dependencies array to be: [dashboardData, fetchData, opts.autoFetch]", {"range": "237", "text": "238"}, "directive", "", [8290, 8292], "[dashboardData, fetchData, opts.autoFetch]"]