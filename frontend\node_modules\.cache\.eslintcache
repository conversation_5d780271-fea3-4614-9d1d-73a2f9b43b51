[{"D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx": "1", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx": "2", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts": "3", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx": "4", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts": "5", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts": "6", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx": "7", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx": "8", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx": "9", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts": "10", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx": "11", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx": "12", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts": "13", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx": "14", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx": "15", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx": "16", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx": "17", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx": "18", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx": "19", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx": "20", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Shimmer.tsx": "21"}, {"size": 3863, "mtime": *************, "results": "22", "hashOfConfig": "23"}, {"size": 5966, "mtime": *************, "results": "24", "hashOfConfig": "23"}, {"size": 6344, "mtime": *************, "results": "25", "hashOfConfig": "23"}, {"size": 13428, "mtime": *************, "results": "26", "hashOfConfig": "23"}, {"size": 10953, "mtime": 1750854126811, "results": "27", "hashOfConfig": "23"}, {"size": 10552, "mtime": 1750851483951, "results": "28", "hashOfConfig": "23"}, {"size": 4839, "mtime": 1750851961802, "results": "29", "hashOfConfig": "23"}, {"size": 8087, "mtime": 1750851785500, "results": "30", "hashOfConfig": "23"}, {"size": 6968, "mtime": 1750851753757, "results": "31", "hashOfConfig": "23"}, {"size": 717, "mtime": 1750851887911, "results": "32", "hashOfConfig": "23"}, {"size": 6149, "mtime": 1750851940253, "results": "33", "hashOfConfig": "23"}, {"size": 5226, "mtime": 1750856181796, "results": "34", "hashOfConfig": "23"}, {"size": 8957, "mtime": 1750851397132, "results": "35", "hashOfConfig": "23"}, {"size": 4108, "mtime": 1750853589258, "results": "36", "hashOfConfig": "23"}, {"size": 2080, "mtime": 1750853487580, "results": "37", "hashOfConfig": "23"}, {"size": 10150, "mtime": 1750856132259, "results": "38", "hashOfConfig": "23"}, {"size": 1832, "mtime": 1750853499407, "results": "39", "hashOfConfig": "23"}, {"size": 2024, "mtime": 1750853512008, "results": "40", "hashOfConfig": "23"}, {"size": 12882, "mtime": 1750856073581, "results": "41", "hashOfConfig": "23"}, {"size": 11914, "mtime": 1750855360774, "results": "42", "hashOfConfig": "23"}, {"size": 2807, "mtime": 1750856095665, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qmqg9d", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts", [], ["107"], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts", ["108", "109", "110", "111", "112", "113"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx", ["114", "115", "116", "117", "118", "119", "120", "121", "122", "123", "124"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts", ["125"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx", ["126"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx", ["127", "128", "129"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx", ["130"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx", ["131"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx", ["132", "133", "134"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx", ["135", "136", "137", "138", "139", "140"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Shimmer.tsx", [], [], {"ruleId": "141", "severity": 1, "message": "142", "line": 282, "column": 6, "nodeType": "143", "endLine": 282, "endColumn": 8, "suggestions": "144", "suppressions": "145"}, {"ruleId": "146", "severity": 1, "message": "147", "line": 17, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 17, "endColumn": 14}, {"ruleId": "146", "severity": 1, "message": "150", "line": 22, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 22, "endColumn": 16}, {"ruleId": "146", "severity": 1, "message": "151", "line": 23, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 23, "endColumn": 15}, {"ruleId": "146", "severity": 1, "message": "152", "line": 24, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 18}, {"ruleId": "146", "severity": 1, "message": "153", "line": 25, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 25, "endColumn": 7}, {"ruleId": "146", "severity": 1, "message": "154", "line": 26, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 26, "endColumn": 11}, {"ruleId": "146", "severity": 1, "message": "155", "line": 21, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 21, "endColumn": 7}, {"ruleId": "146", "severity": 1, "message": "156", "line": 28, "column": 18, "nodeType": "148", "messageId": "149", "endLine": 28, "endColumn": 30}, {"ruleId": "146", "severity": 1, "message": "157", "line": 29, "column": 18, "nodeType": "148", "messageId": "149", "endLine": 29, "endColumn": 31}, {"ruleId": "146", "severity": 1, "message": "158", "line": 30, "column": 15, "nodeType": "148", "messageId": "149", "endLine": 30, "endColumn": 27}, {"ruleId": "146", "severity": 1, "message": "159", "line": 31, "column": 11, "nodeType": "148", "messageId": "149", "endLine": 31, "endColumn": 19}, {"ruleId": "146", "severity": 1, "message": "160", "line": 49, "column": 5, "nodeType": "148", "messageId": "149", "endLine": 49, "endColumn": 16}, {"ruleId": "146", "severity": 1, "message": "161", "line": 70, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 70, "endColumn": 26}, {"ruleId": "146", "severity": 1, "message": "162", "line": 75, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 75, "endColumn": 23}, {"ruleId": "146", "severity": 1, "message": "163", "line": 79, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 79, "endColumn": 19}, {"ruleId": "146", "severity": 1, "message": "164", "line": 87, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 87, "endColumn": 26}, {"ruleId": "146", "severity": 1, "message": "165", "line": 110, "column": 9, "nodeType": "148", "messageId": "149", "endLine": 110, "endColumn": 19}, {"ruleId": "166", "severity": 1, "message": "167", "line": 300, "column": 3, "nodeType": "168", "messageId": "169", "endLine": 300, "endColumn": 20}, {"ruleId": "146", "severity": 1, "message": "170", "line": 33, "column": 5, "nodeType": "148", "messageId": "149", "endLine": 33, "endColumn": 12}, {"ruleId": "146", "severity": 1, "message": "171", "line": 24, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 11}, {"ruleId": "146", "severity": 1, "message": "172", "line": 34, "column": 8, "nodeType": "148", "messageId": "149", "endLine": 34, "endColumn": 14}, {"ruleId": "173", "severity": 1, "message": "174", "line": 290, "column": 9, "nodeType": "175", "messageId": "176", "endLine": 290, "endColumn": 23}, {"ruleId": "146", "severity": 1, "message": "170", "line": 33, "column": 5, "nodeType": "148", "messageId": "149", "endLine": 33, "endColumn": 12}, {"ruleId": "146", "severity": 1, "message": "170", "line": 33, "column": 5, "nodeType": "148", "messageId": "149", "endLine": 33, "endColumn": 12}, {"ruleId": "146", "severity": 1, "message": "177", "line": 24, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 14}, {"ruleId": "146", "severity": 1, "message": "178", "line": 25, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 25, "endColumn": 15}, {"ruleId": "141", "severity": 1, "message": "179", "line": 355, "column": 6, "nodeType": "143", "endLine": 369, "endColumn": 4, "suggestions": "180"}, {"ruleId": "146", "severity": 1, "message": "177", "line": 23, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 23, "endColumn": 14}, {"ruleId": "146", "severity": 1, "message": "178", "line": 24, "column": 3, "nodeType": "148", "messageId": "149", "endLine": 24, "endColumn": 15}, {"ruleId": "141", "severity": 1, "message": "181", "line": 72, "column": 9, "nodeType": "182", "endLine": 72, "endColumn": 71}, {"ruleId": "141", "severity": 1, "message": "183", "line": 75, "column": 9, "nodeType": "182", "endLine": 80, "endColumn": 4}, {"ruleId": "141", "severity": 1, "message": "184", "line": 75, "column": 9, "nodeType": "182", "endLine": 80, "endColumn": 4}, {"ruleId": "141", "severity": 1, "message": "185", "line": 83, "column": 9, "nodeType": "182", "endLine": 95, "endColumn": 4}, "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'dashboardData', 'fetchData', and 'opts.autoFetch'. Either include them or remove the dependency array.", "ArrayExpression", ["186"], ["187"], "@typescript-eslint/no-unused-vars", "'DataFilters' is defined but never used.", "Identifier", "unusedVar", "'DashboardData' is defined but never used.", "'CustomerType' is defined but never used.", "'AccountIndustry' is defined but never used.", "'Team' is defined but never used.", "'ACVRange' is defined but never used.", "'Chip' is defined but never used.", "'DarkModeIcon' is defined but never used.", "'LightModeIcon' is defined but never used.", "'SettingsIcon' is defined but never used.", "'InfoIcon' is defined but never used.", "'lastFetched' is assigned a value but never used.", "'handleThemeToggle' is assigned a value but never used.", "'handleSettings' is assigned a value but never used.", "'handleInfo' is assigned a value but never used.", "'formatLastUpdated' is assigned a value but never used.", "'dataStatus' is assigned a value but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'refetch' is assigned a value but never used.", "'useTheme' is defined but never used.", "'Loader' is defined but never used.", "react/jsx-no-duplicate-props", "No duplicate props allowed", "JSXAttribute", "noDuplicateProps", "'D3Selection' is defined but never used.", "'D3GSelection' is defined but never used.", "React Hook useEffect has a missing dependency: 'data.segments'. Either include it or remove the dependency array.", ["188"], "The 'chartConfig' object makes the dependencies of useEffect Hook (at line 340) change on every render. To fix this, wrap the initialization of 'chartConfig' in its own useMemo() Hook.", "VariableDeclarator", "The 'dimensions' object makes the dependencies of useMemo Hook (at line 135) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'dimensions' object makes the dependencies of useEffect Hook (at line 340) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'colors' logical expression could make the dependencies of useEffect Hook (at line 340) change on every render. To fix this, wrap the initialization of 'colors' in its own useMemo() Hook.", {"desc": "189", "fix": "190"}, {"kind": "191", "justification": "192"}, {"desc": "193", "fix": "194"}, "Update the dependencies array to be: [dashboardData, fetchData, opts.autoFetch]", {"range": "195", "text": "196"}, "directive", "", "Update the dependencies array to be: [processedData, scales, dimensions, chartConfig, colors, animation, centerX, centerY, outerRadius, data.centerLabel, handleSegmentMouseEnter, handleSegmentMouseLeave, handleSegmentClick, data.segments]", {"range": "197", "text": "198"}, [8290, 8292], "[dashboardData, fetchData, opts.autoFetch]", [10674, 10917], "[processedData, scales, dimensions, chartConfig, colors, animation, centerX, centerY, outerRadius, data.centerLabel, handleSegmentMouseEnter, handleSegmentMouseLeave, handleSegmentClick, data.segments]"]