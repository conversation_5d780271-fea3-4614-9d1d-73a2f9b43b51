[{"D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx": "1", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx": "2", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts": "3", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx": "4", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts": "5", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts": "6", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx": "7", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx": "8", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx": "9", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts": "10", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx": "11", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx": "12", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts": "13", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx": "14", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx": "15", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx": "16", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx": "17", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx": "18", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx": "19", "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx": "20"}, {"size": 3863, "mtime": *************, "results": "21", "hashOfConfig": "22"}, {"size": 5207, "mtime": *************, "results": "23", "hashOfConfig": "22"}, {"size": 6344, "mtime": *************, "results": "24", "hashOfConfig": "22"}, {"size": 8144, "mtime": *************, "results": "25", "hashOfConfig": "22"}, {"size": 10972, "mtime": *************, "results": "26", "hashOfConfig": "22"}, {"size": 10552, "mtime": 1750851483951, "results": "27", "hashOfConfig": "22"}, {"size": 4839, "mtime": 1750851961802, "results": "28", "hashOfConfig": "22"}, {"size": 8087, "mtime": 1750851785500, "results": "29", "hashOfConfig": "22"}, {"size": 6968, "mtime": 1750851753757, "results": "30", "hashOfConfig": "22"}, {"size": 717, "mtime": 1750851887911, "results": "31", "hashOfConfig": "22"}, {"size": 6149, "mtime": 1750851940253, "results": "32", "hashOfConfig": "22"}, {"size": 7031, "mtime": 1750853548497, "results": "33", "hashOfConfig": "22"}, {"size": 8957, "mtime": 1750851397132, "results": "34", "hashOfConfig": "22"}, {"size": 4108, "mtime": 1750853589258, "results": "35", "hashOfConfig": "22"}, {"size": 2080, "mtime": 1750853487580, "results": "36", "hashOfConfig": "22"}, {"size": 9632, "mtime": 1750853560556, "results": "37", "hashOfConfig": "22"}, {"size": 1832, "mtime": 1750853499407, "results": "38", "hashOfConfig": "22"}, {"size": 2024, "mtime": 1750853512008, "results": "39", "hashOfConfig": "22"}, {"size": 11536, "mtime": 1750851687011, "results": "40", "hashOfConfig": "22"}, {"size": 11304, "mtime": 1750851605437, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1qmqg9d", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\index.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\App.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\store.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\pages\\Dashboard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\hooks\\useData.ts", ["102", "103", "104", "105", "106", "107", "108", "109"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\redux\\slices\\dataSlice.ts", ["110", "111", "112", "113", "114", "115"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Layout.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\ErrorMessage.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\common\\Loader.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\index.ts", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Footer.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\layout\\Header.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\api\\index.ts", ["116"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\CustomerTypeCard.tsx", [], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\AccountIndustryCard.tsx", ["117"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\DataCard.tsx", ["118"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\TeamCard.tsx", ["119"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\cards\\ACVRangeCard.tsx", ["120"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\DoughnutChart.tsx", ["121", "122", "123", "124", "125"], [], "D:\\Company-assignment\\skygeni\\project-skygeni\\frontend\\src\\components\\charts\\BarChart.tsx", ["126", "127", "128", "129", "130", "131"], [], {"ruleId": "132", "severity": 1, "message": "133", "line": 32, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 32, "endColumn": 15}, {"ruleId": "132", "severity": 1, "message": "136", "line": 33, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 33, "endColumn": 16}, {"ruleId": "132", "severity": 1, "message": "137", "line": 34, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 34, "endColumn": 15}, {"ruleId": "132", "severity": 1, "message": "138", "line": 35, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 35, "endColumn": 18}, {"ruleId": "132", "severity": 1, "message": "139", "line": 36, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 36, "endColumn": 7}, {"ruleId": "132", "severity": 1, "message": "140", "line": 37, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 37, "endColumn": 11}, {"ruleId": "141", "severity": 1, "message": "142", "line": 146, "column": 6, "nodeType": "143", "endLine": 146, "endColumn": 31, "suggestions": "144"}, {"ruleId": "141", "severity": 1, "message": "145", "line": 289, "column": 6, "nodeType": "143", "endLine": 289, "endColumn": 22, "suggestions": "146"}, {"ruleId": "132", "severity": 1, "message": "147", "line": 17, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 17, "endColumn": 14}, {"ruleId": "132", "severity": 1, "message": "136", "line": 22, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 22, "endColumn": 16}, {"ruleId": "132", "severity": 1, "message": "137", "line": 23, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 23, "endColumn": 15}, {"ruleId": "132", "severity": 1, "message": "138", "line": 24, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 24, "endColumn": 18}, {"ruleId": "132", "severity": 1, "message": "139", "line": 25, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 25, "endColumn": 7}, {"ruleId": "132", "severity": 1, "message": "140", "line": 26, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 26, "endColumn": 11}, {"ruleId": "148", "severity": 1, "message": "149", "line": 300, "column": 3, "nodeType": "150", "messageId": "151", "endLine": 300, "endColumn": 20}, {"ruleId": "132", "severity": 1, "message": "152", "line": 33, "column": 5, "nodeType": "134", "messageId": "135", "endLine": 33, "endColumn": 12}, {"ruleId": "132", "severity": 1, "message": "153", "line": 24, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 24, "endColumn": 11}, {"ruleId": "132", "severity": 1, "message": "152", "line": 33, "column": 5, "nodeType": "134", "messageId": "135", "endLine": 33, "endColumn": 12}, {"ruleId": "132", "severity": 1, "message": "152", "line": 33, "column": 5, "nodeType": "134", "messageId": "135", "endLine": 33, "endColumn": 12}, {"ruleId": "132", "severity": 1, "message": "154", "line": 24, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 24, "endColumn": 14}, {"ruleId": "132", "severity": 1, "message": "155", "line": 25, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 25, "endColumn": 15}, {"ruleId": "141", "severity": 1, "message": "156", "line": 74, "column": 9, "nodeType": "157", "endLine": 74, "endColumn": 76}, {"ruleId": "141", "severity": 1, "message": "158", "line": 77, "column": 9, "nodeType": "157", "endLine": 82, "endColumn": 4}, {"ruleId": "141", "severity": 1, "message": "159", "line": 94, "column": 9, "nodeType": "157", "endLine": 106, "endColumn": 4}, {"ruleId": "132", "severity": 1, "message": "154", "line": 23, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 23, "endColumn": 14}, {"ruleId": "132", "severity": 1, "message": "155", "line": 24, "column": 3, "nodeType": "134", "messageId": "135", "endLine": 24, "endColumn": 15}, {"ruleId": "141", "severity": 1, "message": "160", "line": 72, "column": 9, "nodeType": "157", "endLine": 72, "endColumn": 71}, {"ruleId": "141", "severity": 1, "message": "161", "line": 75, "column": 9, "nodeType": "157", "endLine": 80, "endColumn": 4}, {"ruleId": "141", "severity": 1, "message": "162", "line": 75, "column": 9, "nodeType": "157", "endLine": 80, "endColumn": 4}, {"ruleId": "141", "severity": 1, "message": "163", "line": 83, "column": 9, "nodeType": "157", "endLine": 95, "endColumn": 4}, "@typescript-eslint/no-unused-vars", "'LoadingState' is defined but never used.", "Identifier", "unusedVar", "'DashboardData' is defined but never used.", "'CustomerType' is defined but never used.", "'AccountIndustry' is defined but never used.", "'Team' is defined but never used.", "'ACVRange' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'needsRefresh'. Either include it or remove the dependency array.", "ArrayExpression", ["164"], "React Hook useEffect has missing dependencies: 'fetchData' and 'needsRefresh'. Either include them or remove the dependency array.", ["165"], "'DataFilters' is defined but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'refetch' is assigned a value but never used.", "'useTheme' is defined but never used.", "'D3Selection' is defined but never used.", "'D3GSelection' is defined but never used.", "The 'chartConfig' object makes the dependencies of useEffect Hook (at line 311) change on every render. To fix this, wrap the initialization of 'chartConfig' in its own useMemo() Hook.", "VariableDeclarator", "The 'dimensions' object makes the dependencies of useEffect Hook (at line 311) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'colors' logical expression could make the dependencies of useEffect Hook (at line 311) change on every render. To fix this, wrap the initialization of 'colors' in its own useMemo() Hook.", "The 'chartConfig' object makes the dependencies of useEffect Hook (at line 318) change on every render. To fix this, wrap the initialization of 'chartConfig' in its own useMemo() Hook.", "The 'dimensions' object makes the dependencies of useMemo Hook (at line 135) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'dimensions' object makes the dependencies of useEffect Hook (at line 318) change on every render. To fix this, wrap the initialization of 'dimensions' in its own useMemo() Hook.", "The 'colors' logical expression could make the dependencies of useEffect Hook (at line 318) change on every render. To fix this, wrap the initialization of 'colors' in its own useMemo() Hook.", {"desc": "166", "fix": "167"}, {"desc": "168", "fix": "169"}, "Update the dependencies array to be: [dispatch, needsRefresh, opts.useCache]", {"range": "170", "text": "171"}, "Update the dependencies array to be: [fetchData, needsRefresh, opts.autoFetch]", {"range": "172", "text": "173"}, [4496, 4521], "[dispatch, needsRefresh, opts.useCache]", [8402, 8418], "[fetchData, needsRefresh, opts.autoFetch]"]