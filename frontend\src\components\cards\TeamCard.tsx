/**
 * TeamCard Component for SkyGeni Dashboard
 * Specialized card for team data visualization
 */

import React from 'react';
import DataCard from './DataCard';
import { useTeams } from '../../hooks/useData';
import { Team } from '../../types';

interface TeamCardProps {
  className?: string;
  elevation?: number;
  data?: Team[];
  loading?: boolean;
  error?: string;
  chartType?: 'bar' | 'doughnut';
}

const TeamCard: React.FC<TeamCardProps> = ({
  className,
  elevation = 2,
  data: overrideData,
  loading: overrideLoading,
  error: overrideError,
  chartType = 'bar',
}) => {
  const {
    teams,
    loading: hookLoading,
    error: hookError,
    isError,
    refetch,
  } = useTeams();

  const data = overrideData || teams;
  const loading = overrideLoading !== undefined ? overrideLoading : (hookLoading === 'pending');
  const error = overrideError || (isError ? hookError : undefined);

  const processedData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data.map(team => ({
      ...team,
      label: team.name,
      value: team.memberCount,
      count: team.memberCount,
      displayText: `${team.name} (${team.memberCount} members)`,
      performanceText: `Performance: ${team.performance}%`,
      departmentText: team.department,
    }));
  }, [data]);

  return (
    <DataCard
      title="Teams"
      data={processedData}
      chartType={chartType}
      loading={loading}
      error={error}
      className={className}
      elevation={elevation}
    />
  );
};

export const TeamBarCard: React.FC<Omit<TeamCardProps, 'chartType'>> = (props) => (
  <TeamCard {...props} chartType="bar" />
);

export const TeamDoughnutCard: React.FC<Omit<TeamCardProps, 'chartType'>> = (props) => (
  <TeamCard {...props} chartType="doughnut" />
);

export default TeamCard;
