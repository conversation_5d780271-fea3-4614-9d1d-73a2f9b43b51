/**
 * CustomerTypeCard Component for SkyGeni Dashboard
 * 
 * Specialized card component for displaying customer type data:
 * - Uses DataCard as base component
 * - Configured specifically for customer type visualization
 * - Doughnut chart for percentage distribution
 * - Customer-specific styling and interactions
 */

import React from 'react';
import DataCard from './DataCard';
import { useCustomerTypes } from '../../hooks/useData';
import { CustomerType } from '../../types';

// ============================================================================
// CustomerTypeCard Props
// ============================================================================

interface CustomerTypeCardProps {
  /**
   * Custom className for styling
   */
  className?: string;
  
  /**
   * Card elevation
   */
  elevation?: number;
  
  /**
   * Override data (if not using hook)
   */
  data?: CustomerType[];
  
  /**
   * Override loading state
   */
  loading?: boolean;
  
  /**
   * Override error state
   */
  error?: string;
  
  /**
   * Chart type override
   */
  chartType?: 'bar' | 'doughnut';
}

// ============================================================================
// CustomerTypeCard Component
// ============================================================================

const CustomerTypeCard: React.FC<CustomerTypeCardProps> = ({
  className,
  elevation = 2,
  data: overrideData,
  loading: overrideLoading,
  error: overrideError,
  chartType = 'doughnut',
}) => {
  // Use the custom hook to get customer types data
  const {
    customerTypes,
    loading: hookLoading,
    error: hookError,
    isError,
    refetch,
  } = useCustomerTypes();

  // Use override data if provided, otherwise use hook data
  const data = overrideData || customerTypes;
  const loading = overrideLoading !== undefined ? overrideLoading : hookLoading;
  const error = overrideError || (isError ? hookError : undefined);

  // ========================================================================
  // Data Processing
  // ========================================================================

  /**
   * Transform customer type data for better visualization
   */
  const processedData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    return data.map(customerType => ({
      ...customerType,
      // Ensure we have the right field names for the chart
      name: customerType.type,
      label: customerType.type,
      value: customerType.count,
      // Add additional display information
      displayText: `${customerType.type} (${customerType.count.toLocaleString()})`,
      percentageText: customerType.percentage 
        ? `${customerType.percentage.toFixed(1)}%` 
        : '',
    }));
  }, [data]);

  // ========================================================================
  // Event Handlers
  // ========================================================================

  const handleRefresh = React.useCallback(() => {
    refetch();
  }, [refetch]);

  // ========================================================================
  // Render Component
  // ========================================================================

  return (
    <DataCard
      title="Customer Types"
      data={processedData}
      chartType={chartType}
      loading={loading}
      error={error}
      className={className}
      elevation={elevation}
    />
  );
};

// ============================================================================
// Export Component and Variants
// ============================================================================

/**
 * Customer Type Card with Bar Chart
 */
export const CustomerTypeBarCard: React.FC<Omit<CustomerTypeCardProps, 'chartType'>> = (props) => (
  <CustomerTypeCard {...props} chartType="bar" />
);

/**
 * Customer Type Card with Doughnut Chart (default)
 */
export const CustomerTypeDoughnutCard: React.FC<Omit<CustomerTypeCardProps, 'chartType'>> = (props) => (
  <CustomerTypeCard {...props} chartType="doughnut" />
);

export default CustomerTypeCard;
