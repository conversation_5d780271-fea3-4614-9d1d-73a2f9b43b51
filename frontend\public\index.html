<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#1976d2" />
    <meta name="description" content="SkyGeni Dashboard - Data visualization and analytics platform built with React, TypeScript, and D3.js" />
    <meta name="keywords" content="dashboard, analytics, data visualization, charts, React, TypeScript, D3.js" />
    <meta name="author" content="SkyGeni Team" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://skygeni-dashboard.com/" />
    <meta property="og:title" content="SkyGeni Dashboard" />
    <meta property="og:description" content="Data visualization and analytics platform" />
    <meta property="og:image" content="%PUBLIC_URL%/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://skygeni-dashboard.com/" />
    <meta property="twitter:title" content="SkyGeni Dashboard" />
    <meta property="twitter:description" content="Data visualization and analytics platform" />
    <meta property="twitter:image" content="%PUBLIC_URL%/twitter-image.png" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    
    <!-- Manifest -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
    
    <!-- Material Icons -->
    <link 
      href="https://fonts.googleapis.com/icon?family=Material+Icons" 
      rel="stylesheet" 
    />
    
    <title>SkyGeni Dashboard</title>
    
    <!-- Preload critical resources -->
    <!-- <link rel="preload" href="/api/data/dashboard" as="fetch" crossorigin="anonymous" /> -->
    
    <!-- Critical CSS for loading state -->
    <style>
      /* Critical loading styles */
      #root {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Roboto', sans-serif;
      }
      
      .initial-loader {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
        color: #1976d2;
      }
      
      .initial-loader-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e3f2fd;
        border-top: 4px solid #1976d2;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .initial-loader-text {
        font-size: 14px;
        color: #666;
      }
    </style>
  </head>
  <body>
    <noscript>
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        font-family: Arial, sans-serif;
        text-align: center;
        padding: 2rem;
        background-color: #f5f5f5;
      ">
        <h1 style="color: #d32f2f; margin-bottom: 1rem;">
          JavaScript Required
        </h1>
        <p style="color: #666; margin-bottom: 1rem;">
          SkyGeni Dashboard requires JavaScript to run.
        </p>
        <p style="color: #666;">
          Please enable JavaScript in your browser and refresh the page.
        </p>
      </div>
    </noscript>
    
    <div id="root">
      <!-- Initial loading state -->
      <div class="initial-loader">
        <div class="initial-loader-spinner"></div>
        <div class="initial-loader-text">Loading SkyGeni Dashboard...</div>
      </div>
    </div>
    
    <!-- Service Worker Registration -->
    <!-- Temporarily disabled to avoid 404 errors -->
    <!--
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
    -->
  </body>
</html>
